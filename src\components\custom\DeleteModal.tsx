import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
  Text,
} from "@chakra-ui/react";
import { capitalizeFirst } from "../../utils";

function CustomDeleteModal({
  isModalOpen,
  closeModal,
  confirmDelete,
  text = "designation",
}: {
  isModalOpen: boolean;
  closeModal: () => void;
  confirmDelete: () => void;
  text: string;
}) {
  return (
    <>
      <Modal isOpen={isModalOpen} onClose={closeModal} isCentered size="md">
        <ModalOverlay opacity={0} />
        <ModalContent>
          <ModalHeader fontWeight="500" color="red" fontSize="large">
            Confirm Delete
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text color="gray.600" fontSize="small">
              Are you sure you want to delete{" "}
              <Text as="span" fontWeight="medium" color="black">
                {capitalizeF<PERSON><PERSON>(text)}?
              </Text>{" "}
              This cannot be undone it's done.
            </Text>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="outline"
              color="gray.800"
              fontWeight="500"
              fontSize="small"
              size="sm"
              onClick={closeModal}
              _hover={{ bg: "gray.100" }}
              mr={3}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              color="white"
              bg="red"
              size="sm"
              border="1px solid transparent"
              _hover={{ color: "red", bg: "white", border: "1px solid" }}
              colorScheme="red"
              fontWeight="500"
              fontSize="small"
              onClick={confirmDelete}
            >
              Delete
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}

export default CustomDeleteModal;
