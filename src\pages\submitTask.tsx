import { <PERSON>, Tab, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>anel, <PERSON>b<PERSON>ane<PERSON>, Tabs } from "@chakra-ui/react";
import { HiOutlineUserCircle } from "react-icons/hi";
import AppBar from "../components/AppBar";
import SubmitMyTaskTable from "../tabs/submitTask/SubmitMyTask";

const SubmitTask = () => {
  return (
    <>
      <AppBar
        heading="Submit Task"
        avatar="../../logo.png"
        imgAlt="Organization Avatar"
      />
      <Tabs colorScheme="primary" isLazy>
        <TabList>
          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
            <Box as="span" mr="2">
              <HiOutlineUserCircle size="22px" />
            </Box>
            Submit My Task
          </Tab>
        </TabList>

        <TabPanels pt="3">
          <TabPanel px="0">
            <SubmitMyTaskTable />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </>
  );
};

export default SubmitTask;
