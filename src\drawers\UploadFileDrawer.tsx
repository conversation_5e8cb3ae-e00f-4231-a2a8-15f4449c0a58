import {
    <PERSON>,
    But<PERSON>,
    <PERSON>erB<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>lex,
    Input,
    Text,
} from "@chakra-ui/react";
import { UploadIcon } from "@radix-ui/react-icons";
import { useRef, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import { HiOutlinePlus } from "react-icons/hi";
import { toast } from "sonner";
import { queryClient } from "../context/CustomProvider";
import { capitalizeFirst } from "../utils";
import uploadDocumentHandler from "../utils/uploadClass";
import CustomDrawer from "./CustomDrawer";

interface UploadFileDrawerProps {
  title?: string;
  uploadUrl: string;
  resourceName: string;
  invalidateQueryKey?: string[];
  onSuccessRedirect?: string;
  uploadType?: string;
}

const FileInput = ({ fileInputRef, onFileChange }: any) => (
  <Input
    display="none"
    multiple={false}
    name="file_input"
    type="file"
    ref={fileInputRef}
    onChange={onFileChange}
    mx="auto"
  />
);

const UploadButton = ({
  onClick,
  buttonText = "Choose File",
}: {
  onClick?: React.MouseEventHandler<HTMLButtonElement> | undefined;
  buttonText?: string;
}) => (
  <Button
    variant="outline"
    color="primary"
    leftIcon={<HiOutlinePlus color="gray.400" />}
    onClick={onClick}
    fontWeight="500"
    size="sm"
    fontSize="small"
  >
    {buttonText}
  </Button>
);

const UploadFileDrawer = ({
  title,
  uploadUrl,
  resourceName,
  onSuccessRedirect = "/dashboard",
  uploadType = "dashboard",
  invalidateQueryKey,
}: UploadFileDrawerProps) => {
  const [selectedFile, setSelectedFile] = useState<File>();
  const [isFilePicked, setIsFilePicked] = useState(false);
  const fileInput = useRef<HTMLInputElement>(null);
  const closeRef = useRef<HTMLButtonElement>(null);
  const { showBoundary } = useErrorBoundary();

  const formId = `upload-form-${resourceName}`;

  const handleFileUpload = () => {
    if (fileInput.current) {
      fileInput.current.click();
    }
  };

  const doc = new uploadDocumentHandler(
    toast,
    uploadUrl,
    uploadType,
    selectedFile,
    isFilePicked,
    setSelectedFile,
    setIsFilePicked,
    showBoundary,
    () => {
      queryClient.invalidateQueries({ queryKey: invalidateQueryKey });
      closeRef?.current?.click?.();
    }
  );

  return (
    <CustomDrawer
      showModalBtnText={"Upload " + resourceName}
      showModalBtnVariant="outline"
      showModalBtnColor="primary"
      leftIcon={<UploadIcon />}
      drawerSize="sm"
    >
      <>
        <DrawerCloseButton ref={closeRef} />
        <DrawerHeader fontWeight="500" fontSize="lg">
          {title ?? `Upload ${capitalizeFirst(resourceName)} In Batch`}
        </DrawerHeader>

        <DrawerBody>
          <Text fontSize="sm" color="gray.500">
            Upload document in .xlsx or .xls format
          </Text>
          <form id={formId} onSubmit={(e) => doc.handleSubmit(e)}>
            <Flex
              w="400px"
              h="150px"
              border="dashed"
              borderColor="gray.400"
              borderRadius="md"
              mt="4"
              align="center"
              justify="center"
            >
              <FileInput
                fileInputRef={fileInput}
                onFileChange={doc.handleFileChange}
              />
              <Flex
                direction="column"
                gap="2"
                alignItems="center"
                justify="center"
              >
                <UploadButton
                  onClick={handleFileUpload}
                  buttonText={"Upload File"}
                />
                <Box as="span" fontSize="sm">
                  {isFilePicked && selectedFile
                    ? selectedFile.name
                    : "No file selected"}
                </Box>
              </Flex>
            </Flex>
          </form>
        </DrawerBody>

        <DrawerFooter>
          <Button
            type="submit"
            form={formId}
            variant="primary"
            w="full"
            fontSize="sm"
            fontWeight="500"
          >
            Upload File
          </Button>
        </DrawerFooter>
      </>
    </CustomDrawer>
  );
};

export default UploadFileDrawer;
