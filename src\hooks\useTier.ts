import { createTier, deleteTier, getAllTierForLevel, getTierById, updateTier } from "../api/tierName";
import { TTier } from "../types/tier";
import {
  useCrudCreate,
  useCrudDelete,
  useCrudQuery,
  useCrudUpdate,
} from "./useCrud";
import { structuralLevelsQueryKey } from "./useStructure";

export const useGetTiers = (page?: number, limit?: number, levelId?: number) =>
  useCrudQuery(
    ["tiers"],
    () => getAllTierForLevel(page, limit, levelId),
    !!levelId,
    1000
  );

export const useGetTierByIdOrName = (id?: number, name?: string) =>
  useCrudQuery(["tiers", name || ""], () => getTierById(id || 1, name));

export const useAddTier = (onClose?: () => void) =>
  useCrudCreate(
    structuralLevelsQueryKey,
    ({ values, resetForm }) =>
      createTier(values as Omit<TTier, "id">, resetForm),
    onClose
  );

export const useUpdateTier = (id: number, onClose?: () => void) =>
  useCrudUpdate(
    structuralLevelsQueryKey,
    ({ values, resetForm }) => updateTier(id, values, resetForm),
    onClose
  );

export const useDeleteTier = () => useCrudDelete(structuralLevelsQueryKey, deleteTier);
