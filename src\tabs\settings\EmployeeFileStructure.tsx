import { Box, Button, Grid, Input, useDisclosure } from "@chakra-ui/react";
import { useCallback, useEffect, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import { RiAddLine, RiDeleteBinLine } from "react-icons/ri";
import { toast } from "sonner";
import { v4 as uuid_v4 } from "uuid";
import axiosInstance from "../../api";
import Preloader from "../../components/Preloader";
import errorMessageGetter from "../../utils/errorMessages";

const EmployeeFileStructure = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { showBoundary } = useErrorBoundary();
  const [requiredNames, setRequiredNames] = useState<
    {
      id: string;
      name: string;
      isSaved: boolean;
    }[]
  >([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [newFileName, setNewFileName] = useState<string>("");

  const orgName = localStorage.getItem("current_organization_short_name");

  const handleAddNewInputRow = useCallback(
    async (value?: string) => {
      if (!orgName || !value) return;

      setIsLoading(true);
      try {
        const resp = await axiosInstance.post(
          `/client/${orgName}/employee-file-name/`,
          { name: value }
        );
        const data = resp.data.data;
        setRequiredNames((prev) => [
          ...prev,
          { id: data.id, name: data.name, isSaved: true },
        ]);
        setNewFileName("");
        toast.success("Created Successfully");
      } catch (err: any) {
        if (err.response.status === 401) {
          showBoundary(err);
        }
        toast.error(errorMessageGetter(err.response) || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    [orgName, showBoundary]
  );

  const handleDelete = useCallback(
    async (id: string) => {
      if (!orgName) return;

      setIsLoading(true);
      try {
        await axiosInstance.delete(
          `/client/${orgName}/employee-file-name/${id}/`
        );
        setRequiredNames((prev) => prev.filter((data) => data.id !== id));
        toast.success("Deleted Successfully");
      } catch (err: any) {
        if (err.response.status === 401) {
          showBoundary(err);
        }
        toast.error(errorMessageGetter(err.response) || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    [orgName, showBoundary]
  );

  const getAllSavedRequiredFileName = useCallback(async () => {
    if (!orgName) return;

    setIsLoading(true);
    try {
      const resp = await axiosInstance.get(
        `/client/${orgName}/employee-file-name/`
      );
      setRequiredNames(
        resp.data.data.map((data: any) => ({
          id: data.id,
          name: data.name,
          isSaved: true,
        }))
      );
    } catch (err: any) {
      if (err.status === 401 || err.response.status === 401) {
        showBoundary(err);
      }
    } finally {
      setIsLoading(false);
    }
  }, [orgName, showBoundary]);

  useEffect(() => {
    getAllSavedRequiredFileName();
  }, [getAllSavedRequiredFileName]);

  return (
    <>
      {isLoading && <Preloader />}
      <Box
        style={{
          width: "60%",
          margin: "0 auto",
          textAlign: "center",
        }}
      >
        <Input
          placeholder="Enter File Name"
          value={newFileName}
          disabled={isLoading}
          onChange={(e) => setNewFileName(e.target.value)}
        />
        <Button
          leftIcon={<RiAddLine />}
          mb="2"
          bg="secondary.200"
          variant="solid"
          onClick={() => handleAddNewInputRow(newFileName)}
        >
          Create File Name
        </Button>
      </Box>
      <br />
      <Grid
        key={uuid_v4()}
        gridTemplateColumns="repeat(3, 1fr)"
        alignItems="end"
        columnGap="3"
        rowGap={"3"}
        mb="3"
        width={"70%"}
        style={{
          margin: "0 auto",
        }}
      >
        {requiredNames.map((data, index) => (
          <Box
            key={data.id}
            style={{
              position: "relative",
            }}
          >
            <Input
              placeholder={"file Name"}
              disabled={data.isSaved}
              value={data.name}
            />
            <Button
              leftIcon={<RiDeleteBinLine />}
              mb="2"
              bg="secondary.200"
              variant="solid"
              style={{
                position: "absolute",
                top: "0",
                right: "0",
                fontSize: ".8rem",
              }}
              onClick={() => handleDelete(data.id)}
            />
          </Box>
        ))}
      </Grid>
    </>
  );
};

export default EmployeeFileStructure;
