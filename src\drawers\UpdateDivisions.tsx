import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FormControl,
  FormLabel,
  Select,
  Text,
  useToast,
} from "@chakra-ui/react";
import { Field, Form, Formik } from "formik";
import { useState } from "react";
import { AiOutlineCloseCircle } from "react-icons/ai";
import { BsPencil } from "react-icons/bs";
import * as yup from "yup";
import SelectAsyncPaginate from "../components/AsyncSelect";
import InputWithLabel from "../components/InputWithLabel";

type UpdateCorprate = {
  name: string;
  uuid: string;
  upline_corporate_name: string;
};

const schema = yup.object().shape({
  name: yup.string().required("Name is required"),
});

type UpdateDesignationInputs = {
  name: string;
  level: {
    name: string;
    organisation_short_name: string;
    uuid: string;
    slug: string;
  };
};

type UpdateDesignationProps = {
  designation: UpdateDesignationInputs;
};

type UpdateDivisionProps = {
  division?: UpdateC<PERSON>prate;
  upline_corporate_name: string;
  uuid?: string;
  name?: string;
};

const UpdateDesignation: React.FC<UpdateDesignationProps> = ({
  designation,
}) => {
  const org_name = localStorage.getItem("current_organization_short_name");
  const toast = useToast();
  const [editLevel, setEditLevel] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState<string>("");
  const [currentCorporate, setCurrentCorporate] = useState(designation.level);

  const handleSubmit = (
    values: UpdateDesignationInputs,
    { setSubmitting }: { setSubmitting: (isSubmitting: boolean) => void }
  ) => {
    console.log(values, "Updating Designation");
    setSubmitting(false);
  };

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Designation
      </DrawerHeader>
      <DrawerBody>
        <Formik
          initialValues={{
            name: designation.name || "",
            level: designation.level || {},
          }}
          validationSchema={schema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, errors, touched, setFieldValue }) => (
            <Form id="update-designation-form">
              <Field name="name">
                {({ field }: { field: any }) => (
                  <InputWithLabel
                    id="name"
                    label="Name of Designation"
                    variant="filled"
                    bg="secondary.200"
                    mb="5"
                    formErrorMessage={
                      touched.name && errors.name ? errors.name : ""
                    }
                    {...field}
                  />
                )}
              </Field>

              {editLevel ? (
                <Box>
                  <FormControl mb="5">
                    <FormLabel
                      htmlFor="structure_level"
                      fontSize="xs"
                      fontWeight="500"
                    >
                      Pick a Structure Level
                    </FormLabel>
                    <Select
                      placeholder="Select Structure Level"
                      variant="filled"
                      bg="secondary.200"
                      color="gray.400"
                      id="structure_level"
                      onChange={(e) => setSelectedLevel(e.target.value)}
                    >
                      <option value="corporate-level">Corporate</option>
                      <option value="divisional-level">Division</option>
                      <option value="group-level">Group</option>
                      <option value="departmental-level">Department</option>
                      <option value="unit-level">Unit</option>
                    </Select>
                  </FormControl>

                  <FormControl mb="5">
                    <FormLabel
                      htmlFor="level_id"
                      fontSize="xs"
                      fontWeight="500"
                    >
                      Level Name
                    </FormLabel>
                    <SelectAsyncPaginate
                      key={selectedLevel}
                      url={`/organization/setup/${selectedLevel}/list/${org_name}/?me=1`}
                      value={currentCorporate}
                      onChange={(value: any) => {
                        setFieldValue("level", value);
                        setCurrentCorporate(value);
                      }}
                      SelectLabel={(option: any) => `${option.name}`}
                      SelectValue={(option: any) => `${option.uuid}`}
                      placeholder=""
                    />
                    <Text fontSize="sm" color="crimson">
                      {typeof errors.level === "string" ? errors.level : ""}
                    </Text>
                  </FormControl>
                </Box>
              ) : (
                <Text>
                  {/* {GetLevelName({
                    corporate_level: designation?.level,
                    department: designation?.level,
                    division: designation?.level,
                    group: designation?.level,
                    unit: designation?.level,
                  })} */}
                </Text>
              )}

              <Button size="sm" onClick={() => setEditLevel(!editLevel)}>
                {editLevel ? <AiOutlineCloseCircle /> : <BsPencil />}
              </Button>
            </Form>
          )}
        </Formik>
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          form="update-designation-form"
          variant="primary"
          w="full"
          size="sm"
        >
          Update Designation
        </Button>
      </DrawerFooter>
    </>
  );
};

const UpdateDivision: React.FC<UpdateDivisionProps> = ({ division }) => {
  const toast = useToast();

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Division
      </DrawerHeader>
      <DrawerBody>
        <Formik
          initialValues={{ name: division!.name }}
          validationSchema={schema}
          onSubmit={(values) => console.log(values, "Updating Division")}
        >
          {({ errors, touched }) => (
            <Form id="update-division-form">
              <Field name="name">
                {({ field }: { field: any }) => (
                  <InputWithLabel
                    id="name"
                    label="Name of Division"
                    variant="filled"
                    bg="secondary.200"
                    mb="5"
                    formErrorMessage={
                      touched.name && errors.name ? errors.name : ""
                    }
                    {...field}
                  />
                )}
              </Field>
            </Form>
          )}
        </Formik>
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          form="update-division-form"
          variant="primary"
          w="full"
          size="sm"
        >
          Update Division
        </Button>
      </DrawerFooter>
    </>
  );
};

export { UpdateDesignation, UpdateDivision };

