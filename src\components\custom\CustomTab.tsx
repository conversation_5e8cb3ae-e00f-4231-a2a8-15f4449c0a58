import {
  <PERSON>,
  Tab,
  Tab<PERSON>ist,
  TabPanel,
  TabPanels,
  Tabs,
  useColorModeValue,
} from "@chakra-ui/react";
import { useEffect, useMemo, useState } from "react";
import { HiOutlineBriefcase } from "react-icons/hi";
import { useCheckUserType } from "../../hooks/useCheckUserType";
import { useGetCurrentUser } from "../../hooks/user";
import { blue200 } from "../../theme/colors";
import { TRole } from "../../types/user";

export interface TabItem {
  label: string;
  icon?: React.ReactNode;
  showForAll?: boolean;
  visibleTo?: TRole[];
}

type CustomTabProps = {
  tabList: TabItem[];
  tabPanels: React.ComponentType[];
  tabId: string;
  userType?: TRole[];
  variant?: string;
  hasIcon?: boolean;
};

function CustomTab({
  tabList = [],
  tabPanels = [],
  tabId = "",
  userType = [],
  variant,
  hasIcon = false,
}: CustomTabProps) {
  const [tabIndex, setTabIndex] = useState(0);
  const isUserAllowed = useCheckUserType(userType);
  const currentUser = useGetCurrentUser().data;

  const visibleTabChecks = tabList.map((tab) => {
    if (tab.showForAll) return true;
    if (!tab.visibleTo) return false;
    if (tab.visibleTo.length === 0) return false;
    return tab.visibleTo.some((role) => role.includes(currentUser?.role || ""));
  });

  const visibleTabs = useMemo(() => {
    return tabList
      .map((tab, index) => {
        const PanelComponent = tabPanels[index];
        const canView = tab.showForAll || visibleTabChecks[index];

        // if (!canView) {
        //   return {
        //     ...tab,
        //     PanelComponent: UnauthorizedPage,
        //   };
        // }

        if (canView && PanelComponent) {
          return {
            ...tab,
            PanelComponent,
          };
        }
        return null;
      })
      .filter(Boolean) as (TabItem & { PanelComponent: React.ComponentType })[];
  }, [tabList, tabPanels, isUserAllowed]);

  useEffect(() => {
    const savedIndex = localStorage.getItem(tabId);
    if (savedIndex && visibleTabs.length > 0) {
      const index = Math.min(parseInt(savedIndex), visibleTabs.length - 1);
      setTabIndex(index);
    }
  }, [tabId, visibleTabs.length]);

  const activeTabColor = useColorModeValue(blue200, "primary.300");

  return (
    <Tabs
      isLazy
      colorScheme="primary"
      index={tabIndex}
      variant={variant}
      onChange={(index) => {
        setTabIndex(index);
        localStorage.setItem(tabId, JSON.stringify(index));
      }}
    >
      <TabList overflowY="hidden" overflowX="auto" className="scrollbar-hidden">
        {visibleTabs.map((tab, index) => (
          <Tab
            key={index}
            fontSize="sm"
            fontWeight="medium"
            mr="2"
            px="4"
            pb="2"
            borderBottomWidth={variant === "line" ? "2px" : "0"}
            borderColor="transparent"
            _selected={{
              color: activeTabColor,
              borderColor: activeTabColor,
            }}
            whiteSpace="nowrap"
          >
            {hasIcon && (
              <Box as="span" mr="2">
                {tab.icon || <HiOutlineBriefcase size="18px" />}
              </Box>
            )}
            {tab.label}
          </Tab>
        ))}
      </TabList>

      <TabPanels pt="2" overflowX="auto" className="scrollbar-hidden">
        {visibleTabs.map(({ PanelComponent }, index) => (
          <TabPanel key={index} px="0">
            <PanelComponent />
          </TabPanel>
        ))}
      </TabPanels>
    </Tabs>
  );
}

export default CustomTab;
