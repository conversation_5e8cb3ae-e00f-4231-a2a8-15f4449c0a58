import { Route } from "react-router-dom";
import Preloader from "../components/Preloader";
import Layout from "../components/serviceAccount/Layout";
import { organizationRoutes } from "../routes";

const ProtectedRoutes = () => {
  return organizationRoutes.map(({ component: Component, path }) => (
    <Route
      path={path}
      key={path}
      loader={<Preloader />}
      element={
        path === "/setup" ? (
          <Component />
        ) : (
          <Layout>
            <Component />
          </Layout>
        )
      }
    />
  ));
};

export default ProtectedRoutes;
