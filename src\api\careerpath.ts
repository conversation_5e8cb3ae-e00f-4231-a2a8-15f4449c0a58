import { TCareerPath } from "../types/careerpath";
import { CrudService } from "./crud";


const careerPathCrud = new CrudService<TCareerPath>("career-path", {includeTenantAndUser:false});

export const getAllCareerPaths = async (page?: number, limit?: number) => {
  return await careerPathCrud.getAll(page, limit);
};

export const getCareerPathById = async (id: number) => {
  return await careerPathCrud.getById(id);
};

export const createCareerPath = async (
  careerPath: Omit<TCareerPath, "id">,
  resetForm: () => void
) => {
  return await careerPathCrud.create(careerPath, resetForm);
};

export const updateCareerPath = async (
  id: number,
  careerPath: Partial<Omit<TCareerPath, "id">>,
  resetForm?: () => void
) => {
  return await careerPathCrud.update(id, careerPath, resetForm);
};

export const deleteCareerPath = async (id: number) => {
  return await careerPathCrud.delete(id);
};

export const bulkCreateCareerPaths = async (formData: FormData) => {
  return await careerPathCrud.bulkCreate(formData);
};
