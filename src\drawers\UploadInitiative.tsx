import {
  <PERSON>,
  But<PERSON>,
  <PERSON>erBody,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lex,
  Input,
  Text,
} from "@chakra-ui/react";
import { useRef, useState } from "react";
import { HiOutlinePlus } from "react-icons/hi";
import { toast } from "sonner";
import uploadDocumentHand<PERSON> from "../utils/uploadClass";

const UploadInitiative = () => {
  const [selectedFile, setSelectedFile] = useState<File>();
  const [isFilePicked, setIsFilePicked] = useState(false);
  const fileInput = useRef<HTMLInputElement>(null);

  const handleFileUpload = () => {
    fileInput.current?.click();
  };

  const doc = new uploadDocumentHandler(
    toast,
    "/initiative/bulk-upload",
    "initiative",
    selectedFile,
    isFilePicked,
    setSelectedFile,
    setIsFilePicked
  );

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader>Upload KPI/Initiative In Batch</DrawerHeader>
      <DrawerBody>
        <Text>Upload document in .xlsx or .xls format</Text>
        <form id="upload-employee-form" onSubmit={(e) => doc.handleSubmit(e)}>
          <Flex
            w="400px"
            h="150px"
            border="dashed"
            borderColor="gray.400"
            borderRadius="md"
            mt="4"
            align="center"
            justify="center"
          >
            <Input
              display="none"
              multiple={false}
              name="employee_template_file"
              type="file"
              ref={fileInput}
              onChange={(e) => doc.handleFileChange(e)}
            />
            <Box>
              <Button
                variant="outline"
                color="primary"
                leftIcon={<HiOutlinePlus color="gray.400" />}
                onClick={handleFileUpload}
              >
                Choose File
              </Button>
              <Box as="span">
                {isFilePicked && selectedFile ? selectedFile.name : null}
              </Box>
            </Box>
          </Flex>
        </form>
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          form="upload-employee-form"
          variant="primary"
          w="full"
        >
          Upload File
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UploadInitiative;
