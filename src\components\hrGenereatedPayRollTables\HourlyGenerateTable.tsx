import {
  Select,
  Table,
  TableCaption,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
} from "@chakra-ui/react";
import { useEffect, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import { toast } from "sonner";
import axiosInstance from "../../api";
import Preloader from "../Preloader";

const HourlyGenerateTable = (): React.ReactElement => {
  const [availableDate, setAvailableDate] = useState<string[]>([]);
  const { showBoundary } = useErrorBoundary();
  const orgName = localStorage.getItem("current_organization_short_name");
  const [isLoading, setIsLoading] = useState(false);
  const [formonthly, setFormonthly] = useState([]);

  const uniqueElements = (key: string) =>
    Array.from(
      new Set(
        formonthly
          .map((d: any) => d[key].map((r: any) => r[key]))
          .flatMap((value) => value)
      )
    );

  const fixedReceivablesElement = uniqueElements("saved_employee_receivables");
  const employeeRegulatoryReceivables = uniqueElements(
    "saved_employee_regulatory_recievables"
  );
  const employeeRegulatoryDeductables = uniqueElements(
    "saved_employee_regulatory_deductables"
  );
  const employeeOtherDeductables = uniqueElements(
    "saved_employee_other_deductables"
  );
  const otherReceivablesElement = uniqueElements(
    "saved_employee_other_recievables"
  );

  const getAvailableDate = async () => {
    if (!orgName) return;
    try {
      setIsLoading(true);
      const resp = await axiosInstance.get(
        `/client/${orgName}/payroll/monthly_generate/get_available_generated_dates/?structure_type=hourly`
      );

      setAvailableDate(
        Array.from(new Set(resp.data.map((d: any) => d.generated_for)))
      );
      setIsLoading(false);
    } catch (err: any) {
      if (err.response.status === 401) {
        showBoundary(err);
      }
      toast.error(
        "Something went wrong please re-login and check your internet"
      );
    }
  };

  useEffect(() => {
    getAvailableDate();
  }, []);

  const renderTable = (
    caption: string,
    elements: string[],
    dataKey: string,
    extraColumns: any[] = []
  ) => (
    <Table
      size="sm"
      variant="striped"
      overflow="hidden"
      style={{ borderRight: "1px solid black" }}
    >
      <TableCaption>{caption}</TableCaption>
      <Thead bg="gray.200">
        <Tr>
          {elements.map((title, index) => (
            <Th py="2" key={index} style={{ textTransform: "capitalize" }}>
              {title}
            </Th>
          ))}
          {extraColumns.map((col, index) => (
            <Th py="2" key={index} style={{ textTransform: "capitalize" }}>
              {col}
            </Th>
          ))}
        </Tr>
      </Thead>
      <Tbody>
        {formonthly.map((data: any, index) => (
          <Tr key={index}>
            {elements.map((head) => (
              <Td key={head}>
                {data[dataKey].map((s: any) =>
                  s[dataKey] === head ? s.value : ""
                )}
              </Td>
            ))}
            {extraColumns.map((col) => (
              <Td key={col}>{data[col]}</Td>
            ))}
          </Tr>
        ))}
      </Tbody>
    </Table>
  );

  return (
    <div>
      {status === "pending" && <Preloader />}
      {isLoading && <Preloader />}
      <Select
        placeholder="Select option"
        width={"20%"}
        onChange={(e) => {
          if (orgName) {
            console.log("org name");
          }
        }}
      >
        {availableDate.map((data: string, index: number) => (
          <option value={data} key={index}>
            Search by {data}
          </option>
        ))}
      </Select>
      <br />
      {formonthly.length !== 0 && (
        <div style={{ display: "flex" }}>
          {renderTable(
            "Staff Info",
            ["Name", "Grade Levels"],
            "employee_full_name"
          )}
          {renderTable(
            "Fixed Receivables Element",
            fixedReceivablesElement,
            "saved_employee_receivables"
          )}
          {renderTable(
            "Other Receivables Element",
            otherReceivablesElement,
            "saved_employee_other_recievables"
          )}
          {renderTable(
            "Employee Regulatory Receivables",
            employeeRegulatoryReceivables,
            "saved_employee_regulatory_recievables",
            ["total_gross", "annual_gross"]
          )}
          {renderTable(
            "Employee Regulatory Deductables",
            employeeRegulatoryDeductables,
            "saved_employee_regulatory_deductables"
          )}
          {renderTable(
            "Employee Other Deductables",
            employeeOtherDeductables,
            "saved_employee_other_deductables",
            ["net_salary"]
          )}
        </div>
      )}
    </div>
  );
};

export default HourlyGenerateTable;
