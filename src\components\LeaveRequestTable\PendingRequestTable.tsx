import React, { useState } from "react";
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Checkbox,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Button,
  Text,
  Flex,
  Badge,
} from "@chakra-ui/react";
import { SearchIcon } from "@chakra-ui/icons";
import LeaveRequestReviewModal from "./LeaveRequestReviewModal";

type LeaveRequest = {
  id: string | number;
  employee: string;
  role: string;
  leaveType: string;
  requestDate: string;
  status: string;
};

interface LeaveRequestTableProps {
  data?: LeaveRequest[];
  showCheckboxes?: boolean;
  showSearch?: boolean;
  showFilter?: boolean;
  title?: string;
  onRowAction?: (item: LeaveRequest) => void;
  actionLabel?: string;
}

const PendingRequestTable: React.FC<LeaveRequestTableProps> = ({
  data = [],
  showCheckboxes = true,
  showSearch = true,
  showFilter = true,
  title = "Your Previous Leave Request",
  onRowAction = () => {},
  actionLabel = "View",
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("All");
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<any>(null);

  // Filter data based on search and status filter
  const filteredData = data.filter((item) => {
    const matchesSearch =
      item.employee.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.role.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter =
      filterStatus === "All" || item.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  // Handle individual checkbox selection
  const handleRowSelect = (id: any) => {
    setSelectedRows((prev) =>
      prev.includes(id) ? prev.filter((rowId) => rowId !== id) : [...prev, id]
    );
  };

  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectedRows.length === filteredData.length) {
      setSelectedRows([]);
    } else {
      setSelectedRows(filteredData.map((item) => item.id));
    }
  };

  // Handle view request action
  const handleViewRequest = (item: PendingRequest) => {
    setSelectedRequest(item);
    setIsReviewModalOpen(true);
    // Call the original onRowAction if needed
    onRowAction(item);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsReviewModalOpen(false);
    setSelectedRequest(null);
  };

  // Get status badge color
  const getStatusColor = (status: any) => {
    switch (status.toLowerCase()) {
      case "pending":
        return "#FFE4C6";
      case "approved":
        return "green";
      case "rejected":
        return "red";
      default:
        return "gray";
    }
  };

  return (
    <Box>
      {/* Header Section */}
      <Flex justifyContent="space-between" alignItems="center" mb={4}>
        <Text fontWeight={600} fontSize="1rem" color="#333" fontFamily="Inter">
          {title}
        </Text>
        <Flex gap={4} alignItems="center">
          {showSearch && (
            <InputGroup width="500px">
              <InputLeftElement pointerEvents="none">
                <SearchIcon color="gray.300" />
              </InputLeftElement>
              <Input
                placeholder="Search Employee"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                fontSize="14px"
                borderRadius="50px" // Apply borderRadius to Input
                fontFamily="Inter"
              />
            </InputGroup>
          )}
          {showFilter && (
            <>
              <Flex gap={"1rem"} alignItems={"center"}>
                <Box>
                  <Text
                    fontSize={".7rem"}
                    fontWeight={"bold"}
                    fontFamily="Inter"
                  >
                    Filter By
                  </Text>
                </Box>
                <Select
                  placeholder="Filter By: All"
                  maxW="100px"
                  fontSize="14px"
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  fontFamily="Inter"
                >
                  <option value="All">All</option>
                  <option value="Pending">Pending</option>
                  <option value="Approved">Approved</option>
                  <option value="Rejected">Rejected</option>
                </Select>
              </Flex>
            </>
          )}
        </Flex>
      </Flex>

      {/* Table */}
      <Box overflowX="auto">
        <Table variant="simple">
          <Thead borderTop={"2px solid black"} borderBottom={"2px solid black"}>
            <Tr>
              {showCheckboxes && (
                <Th width="50px" py={2}>
                  <Checkbox
                    isChecked={
                      selectedRows.length === filteredData.length &&
                      filteredData.length > 0
                    }
                    isIndeterminate={
                      selectedRows.length > 0 &&
                      selectedRows.length < filteredData.length
                    }
                    onChange={handleSelectAll}
                  />
                </Th>
              )}
              <Th
                fontSize="12px"
                fontFamily="Inter"
                fontWeight={600}
                color="black"
                py={5}
              >
                Employee
              </Th>
              <Th
                fontSize="12px"
                fontFamily="Inter"
                fontWeight={600}
                color="black"
                py={5}
              >
                Role
              </Th>
              <Th
                fontSize="12px"
                fontFamily="Inter"
                fontWeight={600}
                color="black"
                py={5}
              >
                Leave Type
              </Th>
              <Th
                fontSize="12px"
                fontFamily="Inter"
                fontWeight={600}
                color="black"
                py={5}
              >
                Request Date
              </Th>
              <Th
                fontSize="12px"
                fontFamily="Inter"
                fontWeight={600}
                color="black"
                py={5}
              >
                Status
              </Th>
              <Th py={5}></Th>
            </Tr>
          </Thead>
          <Tbody>
            {filteredData.length === 0 ? (
              <Tr>
                <Td colSpan={showCheckboxes ? 7 : 6} textAlign="center" py={1}>
                  <Text color="gray.500" fontFamily="Inter">
                    No leave requests found
                  </Text>
                </Td>
              </Tr>
            ) : (
              filteredData.map((item) => (
                <Tr key={item.id} _hover={{ bg: "#F9F9F9" }}>
                  {showCheckboxes && (
                    <Td px={4} py={1}>
                      <Checkbox
                        isChecked={selectedRows.includes(item.id)}
                        onChange={() => handleRowSelect(item.id)}
                      />
                    </Td>
                  )}
                  <Td
                    px={4}
                    fontSize="14px"
                    fontWeight={600}
                    py={4}
                    fontFamily="Inter"
                  >
                    {item.employee}
                  </Td>
                  <Td
                    px={4}
                    fontSize="14px"
                    color="#666"
                    py={4}
                    fontFamily="Inter"
                  >
                    {item.role}
                  </Td>
                  <Td
                    px={4}
                    fontSize="14px"
                    color="#666"
                    py={4}
                    fontFamily="Inter"
                  >
                    {item.leaveType}
                  </Td>
                  <Td
                    px={4}
                    fontSize="14px"
                    color="#666"
                    py={4}
                    fontFamily="Inter"
                  >
                    {item.requestDate}
                  </Td>
                  <Td px={4} py={4}>
                    <Badge
                      bg={getStatusColor(item.status)}
                      px={3}
                      py={1}
                      borderRadius="50px"
                      fontSize="12px"
                      textTransform="capitalize"
                      color={
                        item.status.toLowerCase() === "pending"
                          ? "#FF8600"
                          : "white"
                      }
                      fontFamily="Inter"
                    >
                      {item.status}
                    </Badge>
                  </Td>
                  <Td px={4} py={1}>
                    <Button
                      size="sm"
                      variant="ghost"
                      color="#0B3178"
                      textDecor={"underline"}
                      fontSize="12px"
                      fontWeight={500}
                      onClick={() => handleViewRequest(item)}
                      _hover={{ bg: "#E6F3FF" }}
                      fontFamily="Inter"
                    >
                      {actionLabel} →
                    </Button>
                  </Td>
                </Tr>
              ))
            )}
          </Tbody>
        </Table>
      </Box>

      {/* Selected items info */}
      {showCheckboxes && selectedRows.length > 0 && (
        <Box mt={4}>
          <Text fontSize="14px" color="#666" fontFamily="Inter">
            {selectedRows.length} item(s) selected
          </Text>
        </Box>
      )}

      {/* Leave Request Review Modal */}
      <LeaveRequestReviewModal
        isOpen={isReviewModalOpen}
        onClose={handleModalClose}
        requestData={selectedRequest}
      />
    </Box>
  );
};

export default PendingRequestTable;
