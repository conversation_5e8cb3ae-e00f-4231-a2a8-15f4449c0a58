import { Box, Flex, Text, Link, Image, Icon, Tooltip } from "@chakra-ui/react";
import { FiFileText, FiDownload, FiImage, FiPaperclip } from "react-icons/fi";
import {
  AiOutlineFilePdf,
  AiOutlineFileExcel,
  AiOutlineFileWord,
  AiOutlineFilePpt,
  AiOutlineFileZip,
} from "react-icons/ai";

export const DocumentLink = ({
  url,
  fileName = "Document",
}: {
  url: string;
  fileName: string;
}) => {
  // Get file extension from URL
  const getFileExtension = (url: string) => {
    if (!url) return "";
    const parts = url.split(".");
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : "";
  };

  // Get file name from URL if not provided
  const getFileName = (url: string) => {
    if (!url) return fileName;
    const parts = url.split("/");
    return parts.length > 0 ? parts[parts.length - 1] : fileName;
  };

  const displayName = fileName || getFileName(url);
  const extension = getFileExtension(url);

  // Determine if it's an image
  const isImage = ["jpg", "jpeg", "png", "gif", "svg", "webp"].includes(
    extension
  );

  // Determine appropriate icon based on file type
  const getFileIcon = () => {
    switch (extension) {
      case "pdf":
        return AiOutlineFilePdf;
      case "xls":
      case "xlsx":
      case "csv":
        return AiOutlineFileExcel;
      case "doc":
      case "docx":
        return AiOutlineFileWord;
      case "ppt":
      case "pptx":
        return AiOutlineFilePpt;
      case "zip":
      case "rar":
      case "7z":
        return AiOutlineFileZip;
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
      case "svg":
      case "webp":
        return FiImage;
      default:
        return FiFileText;
    }
  };

  const FileIcon = getFileIcon();

  return (
    <Box mb={4}>
      <Link
        href={url}
        isExternal
        _hover={{ textDecoration: "none" }}
        display="block"
      >
        <Box
          borderWidth="1px"
          borderRadius="md"
          p={3}
          transition="all 0.2s"
          _hover={{
            shadow: "md",
            borderColor: "blue.300",
            bg: "gray.50",
          }}
        >
          {isImage ? (
            <Box>
              <Flex mb={2} alignItems="center">
                <Icon as={FileIcon} mr={2} color="blue.500" boxSize={5} />
                <Text fontWeight="medium" isTruncated>
                  {displayName}
                </Text>
              </Flex>
              <Box
                borderRadius="md"
                overflow="hidden"
                maxH="200px"
                position="relative"
              >
                <Image
                  src={url}
                  alt={displayName}
                  objectFit="cover"
                  width="100%"
                  fallback={
                    <Flex
                      height="120px"
                      alignItems="center"
                      justifyContent="center"
                      bg="gray.100"
                    >
                      <Text color="gray.500">Image preview unavailable</Text>
                    </Flex>
                  }
                />
                <Flex
                  position="absolute"
                  bottom={0}
                  right={0}
                  bg="blackAlpha.600"
                  color="white"
                  p={1}
                  borderTopLeftRadius="md"
                  alignItems="center"
                >
                  <Icon as={FiDownload} mr={1} boxSize={3} />
                  <Text fontSize="xs">View full image</Text>
                </Flex>
              </Box>
            </Box>
          ) : (
            <Flex alignItems="center">
              <Tooltip
                label={`Open ${extension.toUpperCase()} file`}
                placement="top"
              >
                <Box p={2} mr={3} borderRadius="md" bg="gray.50">
                  <Icon as={FileIcon} boxSize={6} color="blue.500" />
                </Box>
              </Tooltip>
              <Box flex="1">
                <Text fontWeight="medium" isTruncated>
                  {displayName}
                </Text>
                <Text fontSize="xs" color="gray.500" textTransform="uppercase">
                  {extension || "File"}
                </Text>
              </Box>
              <Icon
                as={FiPaperclip}
                color="gray.400"
                transform="rotate(45deg)"
              />
            </Flex>
          )}
        </Box>
      </Link>
    </Box>
  );
};
