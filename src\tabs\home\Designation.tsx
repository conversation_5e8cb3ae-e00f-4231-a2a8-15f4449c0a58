import { UploadIcon } from "@radix-ui/react-icons";
import { HiOutlinePlus } from "react-icons/hi";
import { RiSendPlaneFill } from "react-icons/ri";
import { getAllDesignations } from "../../api/designation";
import CrudUI, { CrudRow } from "../../components/CrudUi";
import AddDesignation from "../../drawers/AddDesignation";
import CustomDrawer from "../../drawers/CustomDrawer";
import UpdateDesignation from "../../drawers/UpdateDesignation";
import UploadDesignation from "../../drawers/UploadDesignation";
import { useDeleteDesignation } from "../../hooks/useDesignation";
import { TDesignation } from "../../types/designations";
import { capitalizeFirst } from "../../utils";

const Designation = () => {
  const deleteDesignationMutation = useDeleteDesignation();

  const renderRow = (designation: TDesignation) => {
    const fields = [
      { label: "Designation", value: capitalizeFirst(designation.name) },
      { label: "Connected To", value: designation.structuralLevel[0].name },
      { label: "Description", value: designation.description || "" },
    ];

    return (
      <CrudRow
        item={designation}
        itemName="Designation"
        fields={fields}
        renderDrawer={
          <>
            <CustomDrawer
              showModalBtnText=""
              showModalBtnVariant="primary"
              showModalBtnColor="white"
              leftIcon={<RiSendPlaneFill />}
              drawerSize="xs"
            >
              <UpdateDesignation designation={designation} />
            </CustomDrawer>
          </>
        }
        onDelete={() => deleteDesignationMutation.mutate(designation.id)}
        tableDataFields={fields.map(({ value }) => value as string)}
      />
    );
  };

  return (
    <CrudUI<TDesignation>
      title="Designations"
      queryKey="designations"
      queryFn={(page) => getAllDesignations(page)}
      columns={["Designation", "Connected To", "Description", "Actions"]}
      renderRow={renderRow}
      actions={
        <>
          <CustomDrawer
            showModalBtnText="Upload Designation"
            showModalBtnVariant="outline"
            showModalBtnColor="primary"
            leftIcon={<UploadIcon />}
            drawerSize="sm"
          >
            <UploadDesignation />
          </CustomDrawer>

          <CustomDrawer
            showModalBtnText="Add Designation"
            showModalBtnVariant="primary"
            showModalBtnColor="white"
            leftIcon={<HiOutlinePlus />}
            drawerSize="sm"
          >
            <AddDesignation />
          </CustomDrawer>
        </>
      }
    />
  );
};

export default Designation;
