import { FormControl, FormLabel, InputProps, Text } from "@chakra-ui/react";
import { Field, FieldProps } from "formik";
import Select from "react-select";
import { allTimeZone } from "../utils/allTimeZone";

type SelectWithLabelProps = InputProps & {
  id: string;
  bg?: string;
  label: string;
  disabled?: boolean;
  formErrorMessage?: string;
  name: string; // Changed from Name to name (Formik convention)
  placeholder?: string;
  variant?: string;
};

const SelectWithLabel: React.FC<SelectWithLabelProps> = ({
  id,
  variant,
  placeholder,
  label,
  bg,
  disabled,
  formErrorMessage,
  name,
  ...rest
}) => {
  const styles = {
    option: (provided: any, state: any) => ({
      ...provided,
    }),
    control: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: "#f2f2f2",
      outline: "transparent",
    }),
  };

  return (
    <Field name={name}>
      {({ field, form }: FieldProps) => (
        <FormControl {...rest}>
          <FormLabel fontSize="xs" htmlFor={id} fontWeight="500">
            {label}
          </FormLabel>
          <Select
            id={id}
            name={field.name}
            value={allTimeZone.find((option) => option.value === field.value)}
            onChange={(option) => form.setFieldValue(name, option?.value)}
            onBlur={field.onBlur}
            placeholder={placeholder}
            styles={styles}
            isDisabled={disabled}
            isSearchable={true}
            options={allTimeZone}
          />
          <Text fontSize="xs" color="crimson">
            {formErrorMessage}
          </Text>
        </FormControl>
      )}
    </Field>
  );
};

export default SelectWithLabel;
