import {
  <PERSON>,
  Button,
  <PERSON>lex,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>are<PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@chakra-ui/react";
import { QuestionMarkCircledIcon } from "@radix-ui/react-icons";
import { FormikProps } from "formik";
import { ReactNode } from "react";
import { blue200 } from "../../../theme/colors";
import CommaSeparatedInput from "../CommaSeparatedField";
import CustomInput, {
  AddMore,
  AddMoreOption,
  CustomMultiSelect,
  CustomSelect,
  CustomTextArea,
  PhoneNumberInput,
  UploadFileButton,
} from "../customInput";
import FormikErrorResponse from "../formError";

export interface InputField {
  name: string;
  type: string;
  label?: string;
  placeholder?: string;
  options?: string[];
  disabled?: boolean;
  validate?: boolean;
  helpText?: string;
  isGridInput?: boolean;
  gridInputs?: InputField[];
  addMoreOptions?: AddMoreOption[];
  selected?: AddMoreOption[];
  selectedChip?: string[];
  gridCol?: number;
  addMore?: boolean;
  header?: string;
  visible?: boolean;
  onChangeCallback?: (val: any) => void;
}

interface ButtonProps {
  type?: "submit" | "button" | "reset";
  text: string;
  icon?: ReactNode;
  buttonLoadingIcon?: ReactNode;
  buttonLoadingText?: string;
  loading?: boolean;
}

interface FormProps {
  formik: FormikProps<any>;
  name?: string;

  inputArray: InputField[];
  children?: ReactNode;
  button?: ButtonProps;
  className?: string;
  topCustomComponents?: ReactNode;
  bottomCustomComponents?: ReactNode;
  hasButton?: boolean;
}

export function ReuseableForm({
  formik,
  inputArray,
  children,
  button,
  className,
  topCustomComponents,
  bottomCustomComponents,
  name,
  hasButton = true,
}: FormProps) {
  const renderInput = ({
    name,
    type,
    label,
    placeholder,
    options,
    addMoreOptions,
    addMore = true,
    selected,
    disabled,
    visible = true,
    validate,
    helpText,
    isGridInput,
    selectedChip,
    onChangeCallback,
  }: InputField) => {
    return (
      <Box w="100%" my={isGridInput ? "2" : "6"} key={name} hidden={!visible}>
        <FormLabel
          htmlFor={name}
          fontWeight="medium"
          textTransform="capitalize"
          display="flex"
          w="fit-content"
          gap="1"
          fontSize={{ base: "medium", sm: "sm" }}
          alignItems="center"
        >
          <span>{label}</span>
          {helpText && (
            <Box ml="1">
              <Tooltip
                label={helpText}
                bg="white"
                color="black"
                fontSize="small"
                padding="4"
                border="1px solid gray.400"
                borderRadius="md"
                hasArrow
              >
                <Button
                  variant="unstyled"
                  m="0"
                  p="0"
                  width="fit-content"
                  h="fit-content"
                  color="gray.500"
                  _hover={{ color: "gray.700" }}
                  transition="colors 0.35s"
                >
                  <QuestionMarkCircledIcon />
                </Button>
              </Tooltip>
            </Box>
          )}
        </FormLabel>
        {type === "upload" ? (
          <UploadFileButton
            formik={formik}
            name={name}
            placeholder={placeholder}
            label={label}
          />
        ) : type === "textarea" ? (
          <Textarea
            autoComplete="true"
            name={name}
            id={name}
            bg="gray.50"
            placeholder={placeholder}
            isDisabled={disabled}
            onChange={(e) => {
              localStorage.setItem(name, e.currentTarget.value);
              formik.handleChange(e);
            }}
            onBlur={formik.handleBlur}
            value={formik.values[name]}
            minH="150px"
            resize="none"
            w="100%"
            fontSize="sm"
          />
        ) : type === "textarea-md" ? (
          <CustomTextArea
            formik={formik}
            name={name}
            placeholder={placeholder}
          />
        ) : type === "select" ? (
          <CustomSelect
            formik={formik}
            name={name}
            placeholder={placeholder}
            options={options || []}
            addMoreOptions={addMoreOptions}
            selected={selected}
            onChangeCallback={onChangeCallback}
          />
        ) : type === "multiselect" ? (
          <CustomMultiSelect
            formik={formik}
            name={name}
            placeholder={placeholder}
            options={options || []}
          />
        ) : type === "phone-number" ? (
          <PhoneNumberInput
            type={type}
            name={name}
            placeholder={placeholder}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values[`${name}`]}
            validate={formik.touched[name] && validate}
            error={formik.touched[name] && Boolean(formik.errors[name])}
            formik={formik}
          />
        ) : type === "add-more" ? (
          <AddMore
            options={addMoreOptions}
            addMore={addMore}
            selected={selected}
            placeholder={placeholder}
            name={name}
            formik={formik}
          />
        ) : type === "selection" ? (
          <CommaSeparatedInput
            selected={selectedChip}
            placeholder={placeholder}
            name={name}
            formik={formik}
          />
        ) : (
          <CustomInput
            type={type}
            name={name}
            placeholder={placeholder}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values[`${name}`]}
            validate={formik.touched[name] && validate}
            error={formik.touched[name] && Boolean(formik.errors[name])}
          />
        )}
        <FormikErrorResponse
          errors={formik.errors}
          touched={formik.touched}
          name={name}
          validate={validate || false}
        />
      </Box>
    );
  };

  const renderGridInputs = (gridInputs: InputField[], disabled?: boolean) =>
    gridInputs.map((input) =>
      renderInput({ ...input, disabled, isGridInput: true })
    );

  const renderField = (field: InputField, index: number) => {
    const { type, gridInputs, disabled, gridCol, header } = field;

    return (
      <Box key={index} w="100%">
        {header && (
          <Heading as="h3" fontWeight="500" fontSize="x-large" mt="4">
            {header}
          </Heading>
        )}
        {type === "grid" ? (
          <>
            <Grid
              templateColumns={
                gridCol === 2
                  ? "repeat(2, 1fr)"
                  : {
                      base: "1fr",
                      md: "repeat(2, 1fr)",
                      xl:
                        (gridInputs || []).length > 2
                          ? "repeat(3, 1fr)"
                          : "repeat(2, 1fr)",
                    }
              }
              gap={3}
            >
              {renderGridInputs(gridInputs || [], disabled)}
            </Grid>
          </>
        ) : (
          renderInput(field)
        )}
      </Box>
    );
  };

  const isLoading = formik.isSubmitting;

  return (
    <Box
      as="form"
      className={className}
      id={name}
      mx="auto"
      w="100%"
      onSubmit={formik.handleSubmit}
    >
      {topCustomComponents}

      {inputArray.map(renderField)}

      {bottomCustomComponents}

      {hasButton && button && (
        <Flex alignItems="center" mt={4}>
          <Button
            type={button.type || "submit"}
            w="full"
            fontWeight={600}
            fontSize="sm"
            maxW="320px"
            mx="auto"
            bg={blue200}
            color="white"
            rounded="10"
            px={3}
            py={2.5}
            display="flex"
            gap="2"
            alignItems="center"
            justifyContent="center"
            transition="all 0.5s"
            _hover={{ opacity: 0.7, rounded: "8" }}
            _active={{ transform: "scale(0.98)" }}
            _disabled={{ cursor: "not-allowed", opacity: 0.65 }}
            isDisabled={isLoading}
            leftIcon={isLoading ? <Spinner size="sm" mr="-8px" /> : undefined}
          >
            {
              <>
                <span>
                  {isLoading ? button.buttonLoadingText : button.text}
                </span>
                {!isLoading && button.icon}
              </>
            }
          </Button>
        </Flex>
      )}

      {children}
    </Box>
  );
}
