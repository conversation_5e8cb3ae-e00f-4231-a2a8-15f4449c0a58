import { useEffect, useState } from "react";
import { getCurrentUser } from "../api/user";
import { TRole } from "../types/user";

export const useCheckUserType = (allowedRoles: TRole[]) => {
  const [isAllowed, setIsAllowed] = useState<boolean | null>(null);

  useEffect(() => {
    let isMounted = true;

    const check = async () => {
      const user = await getCurrentUser();
      if (isMounted) {
        setIsAllowed(allowedRoles.includes(user?.role));
      }
    };

    check();

    return () => {
      isMounted = false;
    };
  }, [allowedRoles.join(",")]);

  return isAllowed;
};
