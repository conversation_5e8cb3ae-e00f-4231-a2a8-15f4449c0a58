import React, { useState } from "react";
import {
  Box,
  Container,
  Flex,
  Heading,
  Text,
  Button,
  Select,
  Input,
  Textarea,
  Grid,
  GridItem,
  VStack,
  HStack,
  FormControl,
  FormLabel,
  IconButton,
  Link,
} from "@chakra-ui/react";
import {
  ChevronDownIcon,
  CalendarIcon,
  AttachmentIcon,
} from "@chakra-ui/icons";
import LeaveRequestSuccessModal from "./LeaveRequestSuccessModal";

const LeaveRequestForm = () => {
  const [formData, setFormData] = useState({
    leaveType: "Sick Leave",
    startDate: "",
    endDate: "",
    notes: "",
  });

  const [selectedDates, setSelectedDates] = useState<string[]>([]);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);

  const leaveTypes = [
    "Sick Leave",
    "Annual Leave",
    "Maternity Leave",
    "Paternity Leave",
    "Study Leave",
    "Emergency Leave",
  ];

  // Generate calendar days for April 2025
  const generateCalendarDays = () => {
    const days = [];
    const firstDay = new Date(2025, 3, 1).getDay(); // April 1, 2025 (0 = Sunday)
    const daysInMonth = new Date(2025, 3, 30).getDate(); // April has 30 days

    // Adjust for Monday start (subtract 1, but handle Sunday as 6)
    const adjustedFirstDay = firstDay === 0 ? 6 : firstDay - 1;

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < adjustedFirstDay; i++) {
      days.push(null);
    }

    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }

    return days;
  };

  const handleDateClick = (day: any) => {
    if (!day) return;

    const dateStr = `2025-04-${day.toString().padStart(2, "0")}`;
    setSelectedDates((prev) => {
      if (prev.includes(dateStr)) {
        return prev.filter((d) => d !== dateStr);
      } else {
        return [...prev, dateStr].sort();
      }
    });
  };

  const handleInputChange = (field: any, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = () => {
    console.log("Form submitted:", { ...formData, selectedDates });
    // Handle form submission
    setIsSuccessModalOpen(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsSuccessModalOpen(false);
  };

  const calendarDays = generateCalendarDays();
  const weekDays = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

  // Check if day is in the pre-selected range (15-19)
  const isPreSelectedDay = (day: any) => {
    return day && day >= 15 && day <= 19;
  };

  return (
    <Box>
      {/* Header Section */}
      <VStack align="start" spacing={4} mb={8}>
        <Heading
          as="h2"
          fontSize="xl"
          fontWeight="medium"
          color="#0B3178"
          fontFamily="Inter"
        >
          Request Leave
        </Heading>
        <Text fontSize="sm" color="gray.600" maxW="600px" fontFamily="Inter">
          Submit your leave request by selecting the leave type, dates, and
          optionally adding a reason or document. You'll be notified once it's
          reviewed!
        </Text>
      </VStack>

      <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={8}>
        {/* Left Column - Form Fields */}
        <GridItem>
          <VStack spacing={6} align="stretch">
            {/* Leave Type Selection */}
            <FormControl w="277px" h="79px">
              <FormLabel
                fontSize="sm"
                fontWeight="medium"
                color="gray.700"
                mb={1}
                fontFamily="Inter"
              >
                Select Leave Type
              </FormLabel>
              <Select
                value={formData.leaveType}
                onChange={(e) => handleInputChange("leaveType", e.target.value)}
                bg="white"
                border="1px solid"
                borderColor="gray.300"
                h="40px"
                _focus={{
                  borderColor: "#0B3178",
                  boxShadow: "0 0 0 1px #0B3178",
                }}
                icon={<ChevronDownIcon />}
              >
                {leaveTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </Select>
            </FormControl>

            {/* Date Selection */}
            <FormControl>
              <FormLabel
                fontSize="sm"
                fontWeight="medium"
                color="gray.700"
                mb={2}
                fontFamily="Inter"
              >
                Select Leave Period
              </FormLabel>
              <Grid templateColumns="1fr 1fr" gap={4}>
                <Box position="relative">
                  <Input
                    type="date"
                    value={formData.startDate}
                    onChange={(e) =>
                      handleInputChange("startDate", e.target.value)
                    }
                    border="1px solid"
                    borderColor="gray.300"
                    _focus={{
                      borderColor: "#0B3178",
                      boxShadow: "0 0 0 1px #0B3178",
                    }}
                  />
                </Box>
                <Box position="relative">
                  <Input
                    type="date"
                    value={formData.endDate}
                    onChange={(e) =>
                      handleInputChange("endDate", e.target.value)
                    }
                    border="1px solid"
                    borderColor="gray.300"
                    _focus={{
                      borderColor: "#0B3178",
                      boxShadow: "0 0 0 1px #0B3178",
                    }}
                  />
                </Box>
              </Grid>
            </FormControl>

            {/* Notes Section */}
            <FormControl>
              <Flex justify="space-between" align="center" mb={2}>
                <FormLabel
                  fontSize="sm"
                  fontWeight="medium"
                  color="gray.700"
                  mb={0}
                  fontFamily="Inter"
                >
                  Note (Optional)
                </FormLabel>
                <Link
                  color="#0B3178"
                  fontSize="sm"
                  textDecoration="underline"
                  _hover={{ color: "#0B3178" }}
                  fontFamily="Inter"
                >
                  <Flex align="center" gap={1}>
                    <AttachmentIcon boxSize={3} />
                    Upload Doc (PDF, PNG, JPEG)
                  </Flex>
                </Link>
              </Flex>
              <Textarea
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                placeholder="Notes..."
                rows={6}
                border="1px solid"
                borderColor="gray.300"
                _focus={{
                  borderColor: "#0B3178",
                  boxShadow: "0 0 0 1px #0B3178",
                }}
                resize="none"
                fontFamily="Inter"
              />
            </FormControl>
          </VStack>
        </GridItem>

        {/* Right Column - Calendar, Legend, and Submit Button */}
        <GridItem ml="147px">
          <VStack spacing={2} align="stretch">
            <Box
              border="1px solid"
              borderColor="gray.200"
              borderRadius="lg"
              p={2}
              bg="white"
              w="294px"
              h="240px"
              display="flex"
              flexDirection="column"
            >
              {/* Calendar Header */}
              <Heading
                as="h3"
                size="xs"
                fontSize="10px"
                textAlign="center"
                mb={0.5}
                color="gray.700"
                fontFamily="Inter"
              >
                April 2025
              </Heading>

              {/* Week Days Header */}
              <Grid templateColumns="repeat(7, 1fr)" gap={0.5} mb={0.5}>
                {weekDays.map((day) => (
                  <Box
                    key={day}
                    textAlign="center"
                    fontSize="10px"
                    fontWeight="medium"
                    color="gray.500"
                    py={0.5}
                    fontFamily="Inter"
                  >
                    {day}
                  </Box>
                ))}
              </Grid>

              {/* Calendar Grid */}
              <Grid templateColumns="repeat(7, 1fr)" gap={0.5} flex="1">
                {calendarDays.map((day, index) => (
                  <Button
                    key={index}
                    onClick={() => handleDateClick(day)}
                    isDisabled={!day}
                    variant="ghost"
                    size="xs"
                    h="28px"
                    w="100%"
                    fontSize="10px"
                    borderRadius="sm"
                    bg={
                      !day
                        ? "transparent"
                        : selectedDates.includes(
                            `2025-04-${day?.toString().padStart(2, "0")}`
                          )
                        ? "orange.400"
                        : isPreSelectedDay(day)
                        ? "gray.200"
                        : "transparent"
                    }
                    color={
                      !day
                        ? "transparent"
                        : selectedDates.includes(
                            `2025-04-${day?.toString().padStart(2, "0")}`
                          )
                        ? "white"
                        : isPreSelectedDay(day)
                        ? "gray.500"
                        : "gray.700"
                    }
                    _hover={{
                      bg: !day
                        ? "transparent"
                        : selectedDates.includes(
                            `2025-04-${day?.toString().padStart(2, "0")}`
                          )
                        ? "orange.500"
                        : isPreSelectedDay(day)
                        ? "gray.200"
                        : "gray.100",
                    }}
                    _disabled={{
                      opacity: 0,
                      cursor: "default",
                    }}
                    fontWeight={
                      selectedDates.includes(
                        `2025-04-${day?.toString().padStart(2, "0")}`
                      )
                        ? "medium"
                        : "normal"
                    }
                  >
                    {day}
                  </Button>
                ))}
              </Grid>
            </Box>

            {/* Legend */}
            <VStack spacing={0.5} align="start">
              <HStack spacing={1}>
                <Box w={2} h={2} bg="orange.400" borderRadius="sm" />
                <Text fontSize="10px" color="gray.600" fontFamily="Inter">
                  Selected Leave days
                </Text>
              </HStack>
              <HStack spacing={1}>
                <Box w={2} h={2} bg="gray.200" borderRadius="sm" />
                <Text fontSize="10px" color="gray.600" fontFamily="Inter">
                  Already Selected Leave days
                </Text>
              </HStack>
            </VStack>

            {/* Submit Button */}
            <Button
              marginTop={"3rem"}
              onClick={handleSubmit}
              bg="#0B3178"
              color="white"
              _hover={{ bg: "#0B3178" }}
              size="md"
              w="294px"
              fontWeight="medium"
              fontFamily="Inter"
            >
              Submit Request
            </Button>
          </VStack>
        </GridItem>
      </Grid>

      {/* Success Modal */}
      <LeaveRequestSuccessModal
        isOpen={isSuccessModalOpen}
        onClose={handleModalClose}
      />
    </Box>
  );
};

export default LeaveRequestForm;
