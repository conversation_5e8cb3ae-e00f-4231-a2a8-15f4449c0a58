import {
    createDesignation,
    deleteDesignation,
    getAllDesignations,
    updateDesignation,
} from "../api/designation";
import { TDesignation } from "../types/designations";
import {
    useCrudCreate,
    useCrudDelete,
    useCrudQuery,
    useCrudUpdate,
} from "./useCrud";

export const designationsQueryKey = ["designations"];

export const useGetDesignations = (page?: number, limit?: number) =>
  useCrudQuery(designationsQueryKey, () => getAllDesignations(page, limit));

export const useAddDesignation = (onClose: () => void) =>
  useCrudCreate(
    designationsQueryKey,
    ({ values, resetForm }) =>
      createDesignation(values as Omit<TDesignation, "id">, resetForm),
    onClose
  );

export const useUpdateDesignation = (id: number, onClose?: () => void) =>
  useCrudUpdate(
    designationsQueryKey,
    ({ values, resetForm }) => updateDesignation(id, values, resetForm),
    onClose
  );

export const useDeleteDesignation = () =>
  useCrudDelete(designationsQueryKey, deleteDesignation);
