import {
  Button,
  Checkbox,
  FormControl,
  FormLabel,
  Grid,
  Heading,
  HStack,
  IconButton,
  Select,
  useToast,
} from "@chakra-ui/react";
import { Field, FieldArray, Form, Formik } from "formik";
import { useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import { RiAddLine, RiDeleteBinLine } from "react-icons/ri";
import * as yup from "yup";
import SelectAsyncPaginate from "../components/AsyncSelect";
import InputWithLabel from "../components/InputWithLabel";
import { get_percent_by_amount } from "../services/extraFunctions";

const validationSchema = yup.object().shape({
  structure_type: yup.string().default("monthly"),
  rate: yup.number().default(0.0),
  number_of_work: yup.number().default(0),
  gross_money: yup.number().required("Gross money is required"),
  grade_level: yup.string().required("Grade level is required"),
  employee_receivables: yup.array().of(
    yup.object().shape({
      fixed_receivables_element: yup.string().required("Element is required"),
      fixed_receivables_element_gross_percent: yup
        .number()
        .required("Percentage is required"),
      regulatory_rates: yup.number().default(0),
    })
  ),
  employee_regulatory_recievables: yup.array().of(
    yup.object().shape({
      regulatory_receivables: yup.string().required("Receivable is required"),
      regulatory_receivables_gross_percent: yup
        .number()
        .required("Percentage is required"),
      regulatory_rates: yup.number().required("Rate is required"),
    })
  ),
  employee_regulatory_deductables: yup.array().of(
    yup.object().shape({
      regulatory_deductables: yup.string().required("Deductable is required"),
      regulatory_deductables_gross_percent: yup
        .number()
        .required("Percentage is required"),
      regulatory_rates: yup.number().default(0),
    })
  ),
  employee_other_deductables: yup.array().of(
    yup.object().shape({
      other_deductables: yup.string().required("Deductable is required"),
      other_deductables_gross_percent: yup
        .number()
        .required("Percentage is required"),
    })
  ),
  employee_other_receivables: yup.array().of(
    yup.object().shape({
      other_receivables_element: yup.string().required("Element is required"),
      other_receivables_element_gross_percent: yup
        .number()
        .required("Percentage is required"),
    })
  ),
});

const CreateMonthlyPayRollDrawer = (): React.ReactElement => {
  const org_name = localStorage.getItem("current_organization_short_name");
  const toast = useToast();
  const [careerPaths, setCareerPaths] = useState<any>();
  const [usePercent, setUsePercent] = useState(true);
  const { showBoundary } = useErrorBoundary();

  const onSubmit = (values: any) => {
    if (!org_name) return;
    console.log("Submitting:", values);
    // Add your submission logic here
  };

  return (
    <Formik
      initialValues={{
        structure_type: "monthly",
        rate: 0,
        number_of_work: 0,
        gross_money: 0,
        grade_level: "",
        employee_receivables: [
          {
            fixed_receivables_element: "",
            fixed_receivables_element_gross_percent: 0,
            regulatory_rates: 0,
          },
        ],
        employee_regulatory_recievables: [
          {
            regulatory_receivables: "",
            regulatory_receivables_gross_percent: 0,
            regulatory_rates: 0,
          },
        ],
        employee_regulatory_deductables: [
          {
            regulatory_deductables: "",
            regulatory_deductables_gross_percent: 0,
            regulatory_rates: 0,
          },
        ],
        employee_other_deductables: [
          {
            other_deductables: "",
            other_deductables_gross_percent: 0,
          },
        ],
        employee_other_receivables: [
          {
            other_receivables_element: "",
            other_receivables_element_gross_percent: 0,
          },
        ],
      }}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      {({ values, errors, touched, setFieldValue, isSubmitting }) => (
        <Form>
          <Checkbox
            isChecked={usePercent}
            onChange={(e) => setUsePercent(e.target.checked)}
          >
            Use Percentage
          </Checkbox>
          <br />
          <br />
          <Field
            as={Select}
            name="structure_type"
            placeholder="Select Structure Type"
          >
            <option value="monthly">Monthly</option>
            <option value="daily">Daily</option>
            <option value="hourly">Hourly</option>
          </Field>
          <br />

          {values.structure_type === "monthly" ? (
            <HStack mb="5">
              <Field name="gross_money">
                {({ field }: any) => (
                  <InputWithLabel
                    id="Gross-Amount"
                    label="Gross Amount"
                    variant="filled"
                    bg="secondary.200"
                    {...field}
                  />
                )}
              </Field>
            </HStack>
          ) : (
            <Grid
              gridTemplateColumns="1fr 1fr 1fr"
              alignItems="end"
              columnGap="2"
              mb="3"
            >
              <Field name="rate">
                {({ field }: any) => (
                  <InputWithLabel
                    id="rate"
                    label={`${
                      values.structure_type === "hourly" ? "Hourly" : "Daily"
                    } Rate`}
                    variant="filled"
                    bg="secondary.200"
                    {...field}
                  />
                )}
              </Field>
              <Field name="number_of_work">
                {({ field }: any) => (
                  <InputWithLabel
                    id="number_of_work"
                    label={`Numbers of ${
                      values.structure_type === "hourly" ? "Hour" : "Days"
                    }`}
                    variant="filled"
                    bg="secondary.200"
                    type="number"
                    {...field}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      const value = e.target.value;
                      setFieldValue("number_of_work", value);
                      if (values.rate) {
                        setFieldValue(
                          "gross_money",
                          Number(value) * values.rate
                        );
                      }
                    }}
                  />
                )}
              </Field>
              <Field name="gross_money">
                {({ field }: any) => (
                  <InputWithLabel
                    id="gross_money"
                    label="Gross Money"
                    variant="filled"
                    bg="secondary.200"
                    type="number"
                    isReadOnly
                    {...field}
                  />
                )}
              </Field>
            </Grid>
          )}

          <HStack mb="5" align="baseline">
            <FormControl mb="5">
              <FormLabel htmlFor="level_id" fontSize="xs" fontWeight="500">
                Career Path Level
              </FormLabel>
              <SelectAsyncPaginate
                onPointChange={null}
                url={`/client/${org_name}/career-path?me=1`}
                value={careerPaths}
                onChange={(value: any) => {
                  setFieldValue("grade_level", value?.career_path_id);
                  setCareerPaths(value);
                }}
                SelectLabel={(option: any) =>
                  `${option.name},  Level:${option?.level}`
                }
                SelectValue={(option: any) => `${option.level}`}
                placeholder=""
              />
            </FormControl>
          </HStack>

          <Heading as="h3" size="md">
            Receivables
          </Heading>
          <br />

          {/* Employee Receivables */}
          <FieldArray name="employee_receivables">
            {({ push, remove }) => (
              <>
                {values.employee_receivables.map((_, index) => (
                  <Grid
                    key={index}
                    gridTemplateColumns={
                      !usePercent ? "1fr 1fr 1fr 40px" : "1fr 1fr 40px"
                    }
                    alignItems="end"
                    columnGap="2"
                    mb="3"
                  >
                    <Field
                      name={`employee_receivables.${index}.fixed_receivables_element`}
                    >
                      {({ field }: any) => (
                        <InputWithLabel
                          label="Employee Fixed Recievable Elements"
                          variant="filled"
                          bg="secondary.200"
                          {...field}
                        />
                      )}
                    </Field>
                    {!usePercent && (
                      <InputWithLabel
                        id={`employee_receivables_${index}_gross_amount`}
                        label="Percentage of Gross(amount)"
                        variant="filled"
                        bg="secondary.200"
                        type="number"
                        placeholder="should be in amount e.g 100"
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          const value = e.target.value;
                          setFieldValue(
                            `employee_receivables.${index}.fixed_receivables_element_gross_percent`,
                            get_percent_by_amount(
                              Number(value),
                              values.gross_money
                            )
                          );
                        }}
                      />
                    )}
                    <Field
                      name={`employee_receivables.${index}.fixed_receivables_element_gross_percent`}
                    >
                      {({ field }: any) => (
                        <InputWithLabel
                          label="Percentage of Gross(%)"
                          variant="filled"
                          bg="secondary.200"
                          type="number"
                          placeholder="%"
                          disabled={!usePercent}
                          {...field}
                        />
                      )}
                    </Field>
                    <IconButton
                      aria-label="Delete Employee Fixed Recievable Elements"
                      icon={<RiDeleteBinLine />}
                      onClick={() => remove(index)}
                    />
                  </Grid>
                ))}
                <Button
                  leftIcon={<RiAddLine />}
                  mb="2"
                  bg="secondary.200"
                  variant="solid"
                  onClick={() =>
                    push({
                      fixed_receivables_element: "",
                      fixed_receivables_element_gross_percent: 0,
                      regulatory_rates: 0,
                    })
                  }
                >
                  Add More
                </Button>
              </>
            )}
          </FieldArray>

          {/* Employee Regulatory Receivables */}
          <FieldArray name="employee_regulatory_recievables">
            {({ push, remove }) => (
              <>
                {values.employee_regulatory_recievables.map((_, index) => (
                  <Grid
                    key={index}
                    gridTemplateColumns={
                      !usePercent ? "1fr 1fr 1fr 1fr 40px" : "1fr 1fr 1fr 40px"
                    }
                    alignItems="end"
                    columnGap="2"
                    mb="3"
                  >
                    <Field
                      name={`employee_regulatory_recievables.${index}.regulatory_receivables`}
                    >
                      {({ field }: any) => (
                        <InputWithLabel
                          label="Regulatory Receivables"
                          variant="filled"
                          bg="secondary.200"
                          {...field}
                        />
                      )}
                    </Field>
                    {!usePercent && (
                      <InputWithLabel
                        id={`regulatory_receivables_${index}_gross_amount`}
                        variant="filled"
                        type="number"
                        label="Regulatory Receivables Gross(amount)"
                        bg="secondary.200"
                        placeholder="in amount e.g 300"
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          const value = e.target.value;
                          setFieldValue(
                            `employee_regulatory_recievables.${index}.regulatory_receivables_gross_percent`,
                            get_percent_by_amount(
                              Number(value),
                              values.gross_money
                            )
                          );
                        }}
                      />
                    )}
                    <Field
                      name={`employee_regulatory_recievables.${index}.regulatory_receivables_gross_percent`}
                    >
                      {({ field }: any) => (
                        <InputWithLabel
                          label="Regulatory Receivables Gross"
                          variant="filled"
                          bg="secondary.200"
                          type="number"
                          placeholder="%"
                          disabled={!usePercent}
                          {...field}
                        />
                      )}
                    </Field>
                    <Field
                      name={`employee_regulatory_recievables.${index}.regulatory_rates`}
                    >
                      {({ field }: any) => (
                        <InputWithLabel
                          label="Regulatory Rates"
                          variant="filled"
                          bg="secondary.200"
                          type="number"
                          placeholder="state the rate amount"
                          {...field}
                        />
                      )}
                    </Field>
                    <IconButton
                      aria-label="Delete Employee Fixed Recievable Elements"
                      icon={<RiDeleteBinLine />}
                      onClick={() => remove(index)}
                    />
                  </Grid>
                ))}
                <Button
                  leftIcon={<RiAddLine />}
                  mb="2"
                  bg="secondary.200"
                  variant="solid"
                  onClick={() =>
                    push({
                      regulatory_receivables: "",
                      regulatory_receivables_gross_percent: 0,
                      regulatory_rates: 0,
                    })
                  }
                >
                  Add More
                </Button>
              </>
            )}
          </FieldArray>

          {/* Employee Other Receivables */}
          <FieldArray name="employee_other_receivables">
            {({ push, remove }) => (
              <>
                {values.employee_other_receivables.map((_, index) => (
                  <Grid
                    key={index}
                    gridTemplateColumns={
                      !usePercent ? "1fr 1fr 1fr 40px" : "1fr 1fr 40px"
                    }
                    alignItems="end"
                    columnGap="2"
                    mb="3"
                  >
                    <Field
                      name={`employee_other_receivables.${index}.other_receivables_element`}
                    >
                      {({ field }: any) => (
                        <InputWithLabel
                          label="Other receivables element"
                          variant="filled"
                          bg="secondary.200"
                          {...field}
                        />
                      )}
                    </Field>
                    {!usePercent && (
                      <InputWithLabel
                        id={`percentage_${index}_gross_amount`}
                        variant="filled"
                        label="Percentage of Gross(amount)"
                        bg="secondary.200"
                        type="number"
                        placeholder="should be in amount e.g 100"
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          const value = e.target.value;
                          setFieldValue(
                            `employee_other_receivables.${index}.other_receivables_element_gross_percent`,
                            get_percent_by_amount(
                              Number(value),
                              values.gross_money
                            )
                          );
                        }}
                      />
                    )}
                    <Field
                      name={`employee_other_receivables.${index}.other_receivables_element_gross_percent`}
                    >
                      {({ field }: any) => (
                        <InputWithLabel
                          label="Percentage of Gross"
                          variant="filled"
                          bg="secondary.200"
                          type="number"
                          placeholder="%"
                          disabled={!usePercent}
                          {...field}
                        />
                      )}
                    </Field>
                    <IconButton
                      aria-label="Delete Employee Fixed Recievable Elements"
                      icon={<RiDeleteBinLine />}
                      onClick={() => remove(index)}
                    />
                  </Grid>
                ))}
                <Button
                  leftIcon={<RiAddLine />}
                  mb="2"
                  bg="secondary.200"
                  variant="solid"
                  onClick={() =>
                    push({
                      other_receivables_element: "",
                      other_receivables_element_gross_percent: 0,
                    })
                  }
                >
                  Add More
                </Button>
              </>
            )}
          </FieldArray>

          <Heading as="h3" size="md">
            Deductables
          </Heading>
          <br />

          {/* Employee Regulatory Deductables */}
          <FieldArray name="employee_regulatory_deductables">
            {({ push, remove }) => (
              <>
                {values.employee_regulatory_deductables.map((_, index) => (
                  <Grid
                    key={index}
                    gridTemplateColumns="1fr 1fr 40px"
                    alignItems="end"
                    columnGap="2"
                    mb="3"
                  >
                    <Field
                      name={`employee_regulatory_deductables.${index}.regulatory_deductables`}
                    >
                      {({ field }: any) => (
                        <InputWithLabel
                          label="Regulatory Deductables"
                          variant="filled"
                          bg="secondary.200"
                          {...field}
                        />
                      )}
                    </Field>
                    <Field
                      name={`employee_regulatory_deductables.${index}.regulatory_deductables_gross_percent`}
                    >
                      {({ field }: any) => (
                        <InputWithLabel
                          label="Regulatory Deductables Gross"
                          variant="filled"
                          bg="secondary.200"
                          type="number"
                          placeholder="%"
                          {...field}
                        />
                      )}
                    </Field>
                    <IconButton
                      aria-label="Delete Employee Fixed Recievable Elements"
                      icon={<RiDeleteBinLine />}
                      onClick={() => remove(index)}
                    />
                  </Grid>
                ))}
                <Button
                  leftIcon={<RiAddLine />}
                  mb="2"
                  bg="secondary.200"
                  variant="solid"
                  onClick={() =>
                    push({
                      regulatory_deductables: "",
                      regulatory_deductables_gross_percent: 0,
                      regulatory_rates: 0,
                    })
                  }
                >
                  Add More
                </Button>
              </>
            )}
          </FieldArray>

          {/* Employee Other Deductables */}
          <FieldArray name="employee_other_deductables">
            {({ push, remove }) => (
              <>
                {values.employee_other_deductables.map((_, index) => (
                  <Grid
                    key={index}
                    gridTemplateColumns="1fr 1fr 40px"
                    alignItems="end"
                    columnGap="2"
                    mb="3"
                  >
                    <Field
                      name={`employee_other_deductables.${index}.other_deductables`}
                    >
                      {({ field }: any) => (
                        <InputWithLabel
                          label="Other Deductables"
                          variant="filled"
                          bg="secondary.200"
                          {...field}
                        />
                      )}
                    </Field>
                    <Field
                      name={`employee_other_deductables.${index}.other_deductables_gross_percent`}
                    >
                      {({ field }: any) => (
                        <InputWithLabel
                          label="Other Deductables Gross"
                          variant="filled"
                          bg="secondary.200"
                          type="number"
                          placeholder="%"
                          {...field}
                        />
                      )}
                    </Field>
                    <IconButton
                      aria-label="Delete Employee Fixed Recievable Elements"
                      icon={<RiDeleteBinLine />}
                      onClick={() => remove(index)}
                    />
                  </Grid>
                ))}
                <Button
                  leftIcon={<RiAddLine />}
                  mb="2"
                  bg="secondary.200"
                  variant="solid"
                  onClick={() =>
                    push({
                      other_deductables: "",
                      other_deductables_gross_percent: 0,
                    })
                  }
                >
                  Add More
                </Button>
              </>
            )}
          </FieldArray>

          <br />
          <Button
            type="submit"
            variant="primary"
            w="md"
            style={{ margin: "0 auto", display: "block", textAlign: "center" }}
            isLoading={isSubmitting}
            loadingText="Creating..."
          >
            Create
          </Button>
        </Form>
      )}
    </Formik>
  );
};

export default CreateMonthlyPayRollDrawer;
