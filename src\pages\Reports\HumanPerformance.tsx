import AppBar from "../../components/AppBar";
import CustomTab, { TabItem } from "../../components/custom/CustomTab";
import IndividualInitiativeReport from "../../tabs/reports/IndividualInitiativeReport";
import IndividualTeamMemberReport from "../../tabs/reports/IndividualTeamMemberReport";
import TeamInitiative from "../../tabs/reports/TeamInitiative";
import TeamReport from "../../tabs/reports/TeamReport";
import UserPerformance from "../../tabs/reports/userPerformance";
import TaskCalendar from "../TaskCalendar";

const HumanPerformance = () => {
  const tabList: TabItem[] = [
    {
      label: "My Task Report",
      visibleTo: ["employee", "teamLead"],
    },
    {
      label: "My Time Report",
      visibleTo: ["employee", "teamLead"],
    },
    {
      label: "My KPI Report",
      visibleTo: ["teamLead"],
    },
    {
      label: "My Team Task",
      visibleTo: ["teamLead"],
    },
    {
      label: "My Team KPI Report",
      visibleTo: ["teamLead"],
    },
    {
      label: "Individual Team Member Task Report",
      visibleTo: ["teamLead", "admin", "employer"],
    },
    {
      label: "Individual Team Member KPI Report",
      visibleTo: ["teamLead", "admin", "employer"],
    },
  ];

  const tabPanels = [
    TaskCalendar,
    UserPerformance,
    TeamInitiative,
    TeamReport,
    IndividualInitiativeReport,
    IndividualTeamMemberReport,
    IndividualInitiativeReport,
  ];

  return (
    <>
      <AppBar heading="Human Performance Management" />
      <CustomTab
        tabList={tabList}
        tabPanels={tabPanels}
        tabId="CPMPageTab"
        userType={["employee", "teamLead", "admin", "employer"]}
        variant="line"
      />
    </>
  );
};

export default HumanPerformance;
