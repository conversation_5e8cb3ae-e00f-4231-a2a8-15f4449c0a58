import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormLabel,
  Image,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dalHeader,
  ModalOverlay,
  Text,
  useToast,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import * as yup from "yup";
import bagIcon from "../../assets/icons/bag-frame.svg";
import InputWithLabel from "../../components/InputWithLabel";

export interface SubmitTaskModalInput {
  task_id: string;
  submission_file: any;
  quantity_target_unit_achieved?: string;
  is_qualitative: boolean;
}

const schema = yup.object().shape({
  submission_file: yup.mixed(),
  quantity_target_unit_achieved: yup.mixed().when("is_qualitative", {
    is: true,
    then: (schema) =>
      yup
        .number()
        .required("Quantity achieved is required")
        .typeError("Quantity achieved must be a number"),
  }),
  is_qualitative: yup.boolean(),
});

const SubmitTaskModal: React.FC<{
  isOpen: any;
  onOpen: () => void;
  onClose: () => void;
  task_id: string;
  task_type: string;
}> = ({ isOpen, onOpen, onClose, task_id, task_type }) => {
  const toast = useToast();
  const { showBoundary } = useErrorBoundary();
  const [isUploadFile, setIsUploadFile] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const formik = useFormik({
    initialValues: {
      submission_file: null,
      quantity_target_unit_achieved: "",
      is_qualitative:
        task_type === "quantitative" ||
        task_type === "quantitative_and_qualitative",
    },
    validationSchema: schema,
    onSubmit: async (values) => {
      setIsSubmitting(true);
      const formdata = new FormData();
      formdata.append("task[task_id]", task_id);

      if (
        task_type === "quantitative" ||
        task_type === "quantitative_and_qualitative"
      ) {
        if (isUploadFile === true && values.submission_file) {
          formdata.append("submission", values.submission_file);
        }
      } else {
        if (values.submission_file) {
          formdata.append("submission", values.submission_file);
        }
      }

      if (
        task_type === "quantitative" &&
        values.quantity_target_unit_achieved
      ) {
        formdata.append(
          "quantity_target_unit_achieved",
          JSON.stringify(+values.quantity_target_unit_achieved)
        );
      }

      try {
        // Submit formdata to your API
        console.log({ formdata });
        // const response = await axiosInstance.post('/your-endpoint', formdata);

        toast({
          title: "Task Submission has been added successfully",
          status: "success",
          position: "top",
          duration: 5000,
          isClosable: true,
        });
      } catch (error) {
        showBoundary(error);
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  useEffect(() => {
    formik.setFieldValue(
      "is_qualitative",
      task_type === "quantitative" ||
        task_type === "quantitative_and_qualitative"
    );
  }, [task_type]);

  return (
    <Modal onClose={onClose} size="sm" isOpen={isOpen} isCentered>
      <Text fontWeight="500" color="secondary.900" mb="4">
        Submit Your Task
      </Text>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <Flex my="3" ml="0">
            <Image src={bagIcon} ml={2} />
            <Box ml={2}>
              <Text as="h1" fontWeight="500">
                Submit your task
              </Text>
              <Text as="h2" fontSize="md" color="gray.600">
                {/* Start to set up your organisation structure here */}
              </Text>
            </Box>
          </Flex>
        </ModalHeader>
        <ModalCloseButton size="xs" />
        <ModalBody>
          <form id="corporate-level" onSubmit={formik.handleSubmit}>
            {(isUploadFile ||
              task_type === "qualitative" ||
              task_type === "quantitative_and_qualitative") && (
              <Box>
                <FormLabel
                  fontSize="xs"
                  htmlFor={"submission_file"}
                  fontWeight="500"
                >
                  Upload Task File
                </FormLabel>
                <Input
                  type="file"
                  required
                  onChange={(event) => {
                    if (event.currentTarget.files) {
                      formik.setFieldValue(
                        "submission_file",
                        event.currentTarget.files[0]
                      );
                    }
                  }}
                  variant="filled"
                  bg="transparent"
                />
              </Box>
            )}

            {task_type !== "qualitative" &&
              task_type !== "quantitative_and_qualitative" && (
                <Text>
                  <small>remove file upload?</small>
                  <Checkbox
                    onChange={() => setIsUploadFile((prevValue) => !prevValue)}
                  />
                </Text>
              )}

            {(task_type === "quantitative" ||
              task_type === "quantitative_and_qualitative") && (
              <InputWithLabel
                id="quantity_target_unit_achieved"
                label="Qty unit achieved"
                size="lg"
                variant="filled"
                placeholder=""
                bg="secondary.200"
                name="quantity_target_unit_achieved"
                value={formik.values.quantity_target_unit_achieved}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                formErrorMessage={formik.errors.quantity_target_unit_achieved}
              />
            )}
          </form>
        </ModalBody>
        <ModalFooter>
          <Button
            type="submit"
            form="corporate-level"
            variant="primary"
            w="full"
            isLoading={isSubmitting}
            loadingText="Submitting"
          >
            Submit
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default SubmitTaskModal;
