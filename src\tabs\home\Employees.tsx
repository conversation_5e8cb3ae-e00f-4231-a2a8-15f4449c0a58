import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Input,
  InputGroup,
  Select,
  Stack,
  Td,
  Text,
  Tr,
} from "@chakra-ui/react";
import { useState } from "react";
import { BsFillTrash2Fill } from "react-icons/bs";
import { FaFilter } from "react-icons/fa";
import { HiOutlinePlus } from "react-icons/hi";
import { IoIosMailUnread } from "react-icons/io";
import {
  RiDeleteBinLine,
  RiSendPlaneFill,
  RiUserSearchLine,
} from "react-icons/ri";
import { toast } from "sonner";
import DataTable from "../../components/custom/Table";
import CustomPopOver from "../../components/PopOver/PopOver";
import AddEmployee from "../../drawers/AddEmployee";
import CustomDrawer from "../../drawers/CustomDrawer";
import EmployeeDrawer from "../../drawers/Employee";
import UpdateEmployee from "../../drawers/UpdateEmployee";
import UploadEmployees from "../../drawers/UploadEmployees";

const Employees = () => {
  const [listOfSelectedUsersid, setListOfSelectedUsersId] = useState<string[]>(
    []
  );
  const [page, setPage] = useState(1);

  // if (isLoading) {
  //   return <Preloader />;
  // }

  const handleCheckBox = (user_id: string) => {
    setListOfSelectedUsersId((prev) =>
      prev.includes(user_id)
        ? prev.filter((id) => id !== user_id)
        : [...prev, user_id]
    );
  };

  const sendUserIdsForReActivation = () => {
    if (listOfSelectedUsersid.length === 0) {
      toast.error("Please Select at least one employee");
    } else {
      const org_name = localStorage.getItem("current_organization_short_name");
      if (org_name) {
        console.log("org name");
      }
    }
  };

  const handleClickSearch = () => {
    // if (searchText) {
    //   if (ORG_NAME) {
    //     console.log('org name')
    //   }
    // } else {
    //   toast.error("Please fill the search box with words");
    // }
  };

  const handleSearch = (searchText: string) => {
    // if (ORG_NAME) {
    //   console.log('org name')
    // }
  };

  const renderRow = (singleEmployee: any) => (
    <Tr key={singleEmployee.uuid} style={{ textTransform: "capitalize" }}>
      <Td>
        <Checkbox
          onChange={() => handleCheckBox(singleEmployee.user.user_id)}
        />
      </Td>
      <Td>
        <Flex direction="column">
          <Text fontSize="xs">
            {singleEmployee?.user?.first_name} {singleEmployee?.user?.last_name}
          </Text>
          <Text as="small" color="gray.500">
            {singleEmployee.corporate_level && "Corporate Level"}
            {singleEmployee.division && "Division Level"}
            {singleEmployee.group && "Group Level"}
            {singleEmployee.department && "Department Level"}
            {singleEmployee.unit && "Unit Level"}
          </Text>
        </Flex>
      </Td>
      <Td fontSize="xs">
        {singleEmployee.corporate_level?.name}
        {singleEmployee.division?.name}
        {singleEmployee.group?.name}
        {singleEmployee.department?.name}
        {singleEmployee.unit?.name}
      </Td>
      <Td fontSize="xs">
        {singleEmployee?.employee_basic_information?.designation?.name}
      </Td>
      <Td fontSize="xs">
        {singleEmployee?.employee_employment_information?.date_employed}
      </Td>
      <Td>
        <Text as="small">active</Text>
      </Td>
      <Td>
        <CustomDrawer showModalBtnText="View" drawerSize="xl">
          <EmployeeDrawer {...singleEmployee} />
        </CustomDrawer>
      </Td>
      <Td>
        <CustomDrawer
          leftIcon={<RiSendPlaneFill />}
          showModalBtnText=""
          drawerSize="xl"
        >
          <UpdateEmployee
            first_name={singleEmployee?.user?.first_name}
            last_name={singleEmployee?.user?.last_name}
            phone_number={singleEmployee?.user?.phone_number}
            email={singleEmployee?.user?.email}
            role={singleEmployee?.user?.user_role}
            date_of_birth={
              singleEmployee?.employee_basic_information?.date_of_birth
            }
            brief_description={
              singleEmployee?.employee_basic_information?.brief_description
            }
            personal_email={
              singleEmployee?.employee_contact_information?.personal_email
            }
            address={singleEmployee?.employee_contact_information?.address}
            guarantor_one_first_name={
              singleEmployee?.employee_contact_information
                ?.guarantor_one_first_name
            }
            guarantor_one_last_name={
              singleEmployee?.employee_contact_information
                ?.guarantor_one_last_name
            }
            guarantor_one_address={
              singleEmployee?.employee_contact_information
                ?.guarantor_one_address
            }
            guarantor_one_occupation={
              singleEmployee?.employee_contact_information
                ?.guarantor_one_occupation
            }
            guarantor_one_age={parseInt(
              singleEmployee?.employee_contact_information?.guarantor_one_age
            )}
            guarantor_one_id_card={
              singleEmployee?.employee_contact_information
                ?.guarantor_one_id_card
            }
            guarantor_one_passport={
              singleEmployee?.employee_contact_information
                ?.guarantor_one_passport
            }
            guarantor_two_first_name={
              singleEmployee?.employee_contact_information
                ?.guarantor_two_first_name
            }
            guarantor_two_last_name={
              singleEmployee?.employee_contact_information
                ?.guarantor_two_last_name
            }
            guarantor_two_address={
              singleEmployee?.employee_contact_information
                ?.guarantor_two_address
            }
            guarantor_two_occupation={
              singleEmployee?.employee_contact_information
                ?.guarantor_two_occupation
            }
            guarantor_two_age={parseInt(
              singleEmployee?.employee_contact_information?.guarantor_two_age
            )}
            guarantor_two_id_card={
              singleEmployee?.employee_contact_information
                ?.guarantor_two_id_card
            }
            guarantor_two_passport={
              singleEmployee?.employee_contact_information
                ?.guarantor_two_passport
            }
            level={
              singleEmployee?.corporate_level
                ? "corporate_level"
                : singleEmployee?.group
                ? "group"
                : singleEmployee?.unit
                ? "unit"
                : singleEmployee?.division
                ? "division"
                : "error finding level"
            }
            level_id={
              singleEmployee?.corporate_level?.uuid ||
              singleEmployee?.group?.uuid ||
              singleEmployee?.unit?.uuid ||
              singleEmployee?.division?.uuid ||
              "error finding level"
            }
            designation_name={
              singleEmployee?.employee_basic_information?.designation?.name
            }
            career_path_level={
              singleEmployee?.career_path?.level
                ? JSON.stringify(singleEmployee?.career_path.level)
                : ""
            }
            education_details={
              singleEmployee?.employee_basic_information?.education_details || [
                { institution: "", year: 2020, qualification: "" },
              ]
            }
            date_employed={
              singleEmployee.employee_employment_information?.date_employed ||
              ""
            }
            employee_uuid={singleEmployee?.uuid}
          />
        </CustomDrawer>
      </Td>
      <Td>
        <Button
          leftIcon={<RiDeleteBinLine />}
          onClick={() => {
            // if (!ORG_NAME) return;
            // if (window.confirm("Are you sure you want to delete?")) {
            //   console.log('org name')
            // }
          }}
        ></Button>
      </Td>
    </Tr>
  );

  return (
    <Box>
      <Flex
        direction={{ base: "column", md: "row" }}
        justifyContent={{ base: "center", md: "space-between" }}
        alignItems="center"
        gap="4"
        mb="4"
      >
        {[].length > 0 && (
          <Text
            as="small"
            display="block"
            fontWeight="500"
            color="gray.700"
            flexShrink={0}
          >
            Page {0} of {0} for {[].length} Employees
          </Text>
        )}
        <Stack
          direction={{ base: "column", md: "row" }}
          w={{ base: "full", md: "fit-content" }}
          spacing={4}
        >
          <CustomDrawer
            showModalBtnText="Add Employee"
            showModalBtnVariant="primary"
            showModalBtnColor="white"
            leftIcon={<HiOutlinePlus />}
            drawerSize="md"
          >
            <AddEmployee />
          </CustomDrawer>
          <CustomDrawer
            showModalBtnText="Upload Employees"
            showModalBtnVariant="outline"
            showModalBtnColor="primary"
            leftIcon={<HiOutlinePlus />}
            drawerSize="sm"
          >
            <UploadEmployees />
          </CustomDrawer>
          {[].length > 0 && (
            <>
              <Button
                leftIcon={<IoIosMailUnread />}
                variant={"outline"}
                color={"primary"}
                mr="2"
                size="sm"
                fontWeight="500"
                onClick={sendUserIdsForReActivation}
              >
                Resend Activation mail
              </Button>
              <Button
                leftIcon={<BsFillTrash2Fill />}
                color={"white"}
                backgroundColor="red.600"
                mr="2"
                size="sm"
                fontWeight="500"
              >
                Delete Selected
              </Button>
            </>
          )}
          {[].length > 0 && (
            <Flex gap="4">
              <CustomPopOver triggericon={<RiUserSearchLine />}>
                <InputGroup>
                  <Input
                    type="text"
                    placeholder="search"
                    onChange={(e) => {}}
                  />
                  <Button
                    variant="primary"
                    color="white"
                    loadingText="searching.."
                    onClick={handleClickSearch}
                  >
                    <RiUserSearchLine />
                  </Button>
                </InputGroup>
              </CustomPopOver>
              <CustomPopOver triggericon={<FaFilter />}>
                <FormControl mb="5">
                  <FormLabel
                    htmlFor="structure_level"
                    fontSize="xs"
                    fontWeight="500"
                  >
                    Pick a Structure Level
                  </FormLabel>
                  <Select
                    placeholder="Select Structure Level"
                    variant="filled"
                    bg="secondary.200"
                    color="gray.400"
                    id="structure_level"
                    onChange={(e) => {}}
                  >
                    <option value="corporate-level">Corporate</option>
                    <option value="divisional-level">Division</option>
                    <option value="group-level">Group</option>
                    <option value="departmental-level">Department</option>
                    <option value="unit-level">Unit</option>
                  </Select>
                </FormControl>
                <FormControl mb="5">
                  <FormLabel htmlFor="level_id" fontSize="xs" fontWeight="500">
                    Level Name
                  </FormLabel>
                  {/* <SelectAsyncPaginate
                    onPointChange={null}
                    key={selectedLevel}
                    url={`/organization/setup/${selectedLevel}/list/${ORG_NAME}/?me=1`}
                    value={currentCorporate}
                    onChange={(value: any) => {
                      handleSearch(value?.name);
                      setCurrentCorporate(value);
                    }}
                    SelectLabel={(option: any) => `${option.name}`}
                    SelectValue={(option: any) => `${option.uuid}`}
                    placeholder={""}
                  /> */}
                </FormControl>
              </CustomPopOver>
            </Flex>
          )}
        </Stack>
      </Flex>
      <DataTable
        headers={[
          "",
          "EMPLOYEE",
          "TEAM",
          "DESIGNATION",
          "DATE EMPLOYED",
          "Status",
          "",
          "Actions",
          "",
        ]}
        data={[]}
        isLoading={false}
        onPageChange={(pageNumberClick: any) => {
          // if (!ORG_NAME) return;
          // setPageNum(pageNumberClick);
        }}
        pageNum={0}
        pageCount={0}
        renderRow={renderRow}
      />
    </Box>
  );
};

export default Employees;
