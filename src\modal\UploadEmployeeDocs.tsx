import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Select,
  Skeleton,
  Text,
  useToast,
} from "@chakra-ui/react";
import { Field, Form, Formik } from "formik";
import { useEffect, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import * as yup from "yup";
import axiosInstance from "../api";
import InputWithLabel from "../components/InputWithLabel";

const schema = yup.object().shape({
  name: yup.string().required("File type is required"),
  file: yup.mixed().required("File is required"),
});

const UploadEmployeeDocs: React.FC<{
  isOpen: any;
  onOpen: () => void;
  onClose: () => void;
  employeeUuid: string;
}> = ({ isOpen, onClose, employeeUuid }) => {
  const toast = useToast();
  const { showBoundary } = useErrorBoundary();
  const [fileNames, setFileNames] = useState<
    { id: string | number; name: string }[]
  >([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [writeManually, setWriteManually] = useState(false);

  useEffect(() => {
    const getAllSavedRequiredFileName = async () => {
      const orgName = localStorage.getItem("currentOrganizationShortName");
      if (!orgName) return;
      setIsLoading(true);
      try {
        const resp = await axiosInstance.get(
          `/client/${orgName}/employee-file-name/`
        );
        setFileNames(resp.data.data);
      } catch (err: any) {
        if (err.status === 401 || err.response.status === 401) {
          showBoundary(err);
        }
      }
      setIsLoading(false);
    };
    getAllSavedRequiredFileName();
  }, []);

  const handleSubmit = async (values: any, { setSubmitting }: any) => {
    if (Math.floor(values.file.size / 1000) > 600) {
      toast({
        title: "Large File Size",
        description:
          "Your File Size should be minimum of 500kb and maximum should be 600kb",
        status: "error",
        position: "top",
        duration: 5000,
        isClosable: true,
      });
      setSubmitting(false);
      return;
    }

    const orgName = localStorage.getItem("currentOrganizationShortName");
    if (!orgName) return;
    setIsLoading(true);
    const form = new FormData();
    form.append("name", values.name);
    form.append("file", values.file);
    form.append("employee", employeeUuid);

    try {
      await axiosInstance.post(`/client/${orgName}/employee-file/`, form);
      toast({
        title: "Uploaded Successfully",
        status: "success",
        position: "top",
        duration: 5000,
        isClosable: true,
      });
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (err: any) {
      if (err.status === 401 || err.response.status === 401) {
        showBoundary(err);
      }
    }
    setIsLoading(false);
    setSubmitting(false);
  };

  return (
    <Modal onClose={onClose} size="sm" isOpen={isOpen} isCentered>
      <Text fontWeight="500" color="secondary.900" mb="4">
        Upload Employee File
      </Text>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <Flex my="3" ml="0">
            <Box ml={2}>
              <Text as="h1" fontWeight="500">
                Submit Employee File
              </Text>
            </Box>
          </Flex>
        </ModalHeader>
        <ModalCloseButton size="xs" />
        <ModalBody>
          <Formik
            initialValues={{ name: "", file: null }}
            validationSchema={schema}
            onSubmit={handleSubmit}
          >
            {({ setFieldValue, isSubmitting, errors, touched }) => (
              <Form id="upload-form">
                {writeManually ? (
                  <Field name="name">
                    {({ field }: any) => (
                      <InputWithLabel
                        id="taskfile"
                        label="File Type"
                        variant="filled"
                        bg="secondary.200"
                        formErrorMessage={
                          touched.name && errors.name ? errors.name : ""
                        }
                        {...field}
                      />
                    )}
                  </Field>
                ) : (
                  <FormControl mb="4">
                    <FormLabel fontSize="xs" fontWeight="500">
                      File Type
                    </FormLabel>
                    <Skeleton isLoaded={!isLoading}>
                      <Field
                        as={Select}
                        name="name"
                        variant="filled"
                        bg="secondary.200"
                      >
                        <option value="">Select File Name</option>
                        {fileNames.map((name) => (
                          <option key={name.id} value={name.name}>
                            {name.name}
                          </option>
                        ))}
                      </Field>
                    </Skeleton>
                    {touched.name && errors.name && (
                      <Text fontSize="sm" color="crimson">
                        {errors.name}
                      </Text>
                    )}
                  </FormControl>
                )}

                <Text>
                  <small>
                    Is File type not showing up? Click to write
                    <Checkbox
                      isChecked={writeManually}
                      onChange={() => setWriteManually(!writeManually)}
                    />
                  </small>
                </Text>

                <FormControl>
                  <FormLabel fontSize="xs" fontWeight="500">
                    Upload File
                  </FormLabel>
                  <Input
                    type="file"
                    accept="image/png, image/gif, image/jpeg"
                    onChange={(event) => {
                      if (event.target.files && event.target.files[0]) {
                        setFieldValue("file", event.target.files[0]);
                      }
                    }}
                    variant="filled"
                    bg="transparent"
                  />
                  {touched.file && errors.file && (
                    <Text fontSize="sm" color="crimson">
                      {errors.file}
                    </Text>
                  )}
                </FormControl>
              </Form>
            )}
          </Formik>
        </ModalBody>
        <ModalFooter>
          <Button
            type="submit"
            form="upload-form"
            variant="primary"
            w="full"
            isLoading={isLoading}
          >
            Submit
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default UploadEmployeeDocs;