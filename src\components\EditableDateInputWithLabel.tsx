import { Box, FormControl, FormLabel, Text } from "@chakra-ui/react";
import { Field, FieldProps } from "formik";
import moment from "moment";
import { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

interface EditableExcludeDaysInCalendarProp {
  days_array: number[];
  name: string;
  label: string;
  id: string;
  formErrorMessage?: string;
  dateFormat?: string;
  disabled?: boolean;
}

export const EditableExcludeDaysInCalendar: React.FC<
  EditableExcludeDaysInCalendarProp
> = ({
  days_array = [],
  name,
  label,
  id,
  formErrorMessage,
  dateFormat = "MM/DD/YYYY",
  disabled = false,
  ...rest
}) => {
  const [startDate, setStartDate] = useState<Date | null>(null);

  const isWeekday = (date: Date) => {
    const day = date.getDay();
    return days_array.includes(day);
  };

  return (
    <Box>
      <Field name={name}>
        {({ field, form }: FieldProps) => (
          <FormControl {...rest}>
            <FormLabel fontSize="xs" htmlFor={id} fontWeight="500">
              {label}
            </FormLabel>
            <DatePicker
              selected={
                field.value ? moment(field.value, "MM/DD/YYYY").toDate() : null
              }
              onChange={(date: Date | null) => {
                if (date) {
                  form.setFieldValue(name, moment(date).format("MM/DD/YYYY"));
                  setStartDate(date);
                } else {
                  form.setFieldValue(name, null);
                  setStartDate(null);
                }
              }}
              filterDate={isWeekday}
              dateFormat={dateFormat}
              className="chakra-input css-gzp9gs"
              disabled={disabled}
            />
            <Text fontSize="xs" color="crimson">
              {formErrorMessage}
            </Text>
          </FormControl>
        )}
      </Field>
    </Box>
  );
};
