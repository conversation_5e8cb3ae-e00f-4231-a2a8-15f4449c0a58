import { HiOutlineClipboardList } from "react-icons/hi";
import AppBar from "../components/AppBar";
import CustomTab, { TabItem } from "../components/custom/CustomTab";
import { useCheckUserType } from "../hooks/useCheckUserType";
import AdminViewEmployeeTask from "../tabs/tasks/AdminViewEmployeeTask";
import AdminViewTeamTask from "../tabs/tasks/AdminViewTeamTask";
import MyTask from "../tabs/tasks/MyTask";
import TeamTask from "../tabs/tasks/TeamTask";
import { TRole } from "../types/user";
import OrganizationTasks from "../tabs/tasks/OrganizationTask";

const Tasks = () => {
  const adminRoles: TRole[] = ["admin", "employer", "hr"];
  const otherRoles: TRole[] = ["contractor", "teamMember", "teamLead"];

  const tabList: TabItem[] = [
    {
      label: "My Tasks",
      showForAll: true,
      icon: <HiOutlineClipboardList size="18px" />,
    },
    {
      label: "Organization Tasks",
      showForAll: false,
      visibleTo: adminRoles,
      icon: <HiOutlineClipboardList size="18px" />,
    },
    {
      label: "Team Task",
      showForAll: false,
      visibleTo: otherRoles,
      icon: <HiOutlineClipboardList size="18px" />,
    },
    {
      label: "Employee Task",
      showForAll: false,
      visibleTo: adminRoles,
      icon: <HiOutlineClipboardList size="18px" />,
    },
    {
      label: "Team Task (admin)",
      showForAll: false,
      visibleTo: adminRoles,
      icon: <HiOutlineClipboardList size="18px" />,
    },
  ];

  const tabPanels = [
    MyTask,
    OrganizationTasks,
    TeamTask,
    AdminViewEmployeeTask,
    AdminViewTeamTask,
  ];

  return (
    <>
      <AppBar heading="Tasks" />

      <CustomTab
        tabId="tasksTab"
        tabList={tabList}
        tabPanels={tabPanels}
        hasIcon
        userType={adminRoles}
        variant="line"
      />
    </>
  );
};

export default Tasks;
