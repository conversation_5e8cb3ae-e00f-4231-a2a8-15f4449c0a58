export type TRole =
  | "teamLead"
  | "admin"
  | "teamMember"
  | "employer"
  | "hr"
  | "employee"
  | "contractor";

  export enum UserRole {
    STAFF = "employee",
    ADMIN = "hr",
    SUPER_ADMIN = "employer",
    TEAM_MEMBER = "teamMember",
    TEAM_LEAD = "teamLead",
    CORPORATE_CONTRACTOR = "corporateContractor",
    INDIVIDUAL_CONTRACTOR = "individualContractor",
  }

export interface TUser {
  id: string;
  name: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  role: TRole;
  tenantId: string;
  email: string;
  permissions:string[],
  userType: string;
  isSuperAdmin: boolean;
  isHr: boolean;
  isStaff: boolean;
  hasCompletedCompanyProfile: boolean;
  isNewUser: boolean;
}
