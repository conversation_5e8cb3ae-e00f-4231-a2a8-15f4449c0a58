import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  HStack,
  Image,
  Input,
  MenuItem,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Text,
  Textarea,
  Tooltip,
  useDisclosure,
  useToast,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import * as yup from "yup";
import axiosInstance from "../../api";
import bagIcon from "../../assets/icons/bag-frame.svg";
import InputWithLabel from "../../components/InputWithLabel";
import Preloader from "../../components/Preloader";

export interface SubmitTaskModalType {
  rating_remark: string;
  quality_target_point_achieved?: number;
  quantity_target_unit_achieved?: number;
  submission: any;
  use_owner_submission: boolean;
}

const schema = yup.object().shape({
  rating_remark: yup.string().required("enter your remark"),
  quality_target_point_achieved: yup.number(),
  quantity_target_unit_achieved: yup.number(),
  submission: yup.mixed(),
  use_owner_submission: yup.boolean(),
});

const RateTaskModal: React.FC<{
  task_id: string;
  task_type: string;
  isOwner: boolean;
  isCorporateTeamLead: boolean;
  status: string;
  quantity_target_point?: string;
  quality_target_point: number;
}> = ({
  task_id,
  task_type,
  isOwner,
  isCorporateTeamLead,
  status,
  quantity_target_point,
  quality_target_point,
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { showBoundary } = useErrorBoundary();
  const toast = useToast();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [useSubmmisonFile, setUseSubmmisonFile] = useState<boolean>(false);
  const [useOwnerQtySubmmision, setUseOwnerQtySubmmision] = useState(false);
  const [taskType, setTaskType] = useState(task_type);

  const formik = useFormik<SubmitTaskModalType>({
    initialValues: {
      rating_remark: "",
      quality_target_point_achieved: quality_target_point || 0,
      quantity_target_unit_achieved: 0,
      submission: null,
      use_owner_submission: false,
    },
    validationSchema: schema,
    onSubmit: async (values) => {
      setIsLoading(true);
      const form = new FormData();
      form.append("rating_remark", values.rating_remark);

      if (
        taskType === "qualitative" ||
        taskType === "quantitative_and_qualitative"
      ) {
        if (values.quality_target_point_achieved) {
          form.append(
            "quality_target_point_achieved",
            JSON.stringify(+values.quality_target_point_achieved)
          );
        }
      }

      if (values.use_owner_submission === false && values.submission) {
        form.append("submission", values.submission);
      }

      if (useSubmmisonFile === false && values.quantity_target_unit_achieved) {
        form.append(
          "quantity_target_unit_achieved",
          JSON.stringify(+values.quantity_target_unit_achieved)
        );
      }

      form.append(
        "use_owner_submission",
        JSON.stringify(values.use_owner_submission)
      );

      await SubmitTaskApi(form);
    },
  });

  const SubmitTaskApi = async (form: FormData) => {
    const org_name = localStorage.getItem("current_organization_short_name");

    if (org_name) {
      try {
        const resp = await axiosInstance.put(
          `client/${org_name}/task/${task_id}/rate/`,
          form
        );
        setIsLoading(false);

        toast({
          title: "Rated Successfully!",
          status: "success",
          position: "top",
          duration: 5000,
          isClosable: true,
        });

        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } catch (err: any) {
        setIsLoading(false);
        if (err.response.status === 401) {
          showBoundary(err);
        } else if (err.response.status === 403) {
          toast({
            title: err.response.data.message,
            status: "error",
            position: "top",
            duration: 5000,
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong",
            status: "error",
            position: "top",
            duration: 5000,
            isClosable: true,
          });
        }
      }
    }
  };

  const handleAbleToRate = (): boolean => {
    // if (TypeVerifierUserChecker(["team_lead"], "client_tokens")) {
    //   if (isCorporateTeamLead) return true;
    //   return !isOwner;
    // }
    return false;
  };

  useEffect(() => {
    formik.setFieldValue("use_owner_submission", useSubmmisonFile);
  }, [useSubmmisonFile]);

  useEffect(() => {
    if (quality_target_point) {
      formik.setFieldValue(
        "quality_target_point_achieved",
        +quality_target_point
      );
    }
  }, [quality_target_point]);

  return (
    <>
      {(taskType === "qualitative" ||
        taskType === "quantitative_and_qualitative" ||
        taskType === "quantitative") && (
        <MenuItem
          isDisabled={
            !(handleAbleToRate() && ["awaiting_rating"].includes(status))
          }
          onClick={onOpen}
        >
          Rate
        </MenuItem>
      )}

      <Box>
        <Modal onClose={onClose} size="sm" isOpen={isOpen} isCentered>
          <Text fontWeight="500" color="secondary.900" mb="4">
            Rate Task
          </Text>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>
              <Flex my="3" ml="0">
                <Image src={bagIcon} ml={2} />
                <Box ml={2}>
                  <Text as="h1" fontWeight="500">
                    Rate Task
                  </Text>
                  <Text as="h2" fontSize="md" color="gray.600">
                    {/* Start to su up your organisation structure here */}
                  </Text>
                </Box>
              </Flex>
            </ModalHeader>
            <ModalCloseButton size="xs" />
            <ModalBody>
              {false && <Preloader />}

              <form id="rate-task" onSubmit={formik.handleSubmit}>
                {(taskType === "qualitative" ||
                  taskType === "quantitative_and_qualitative") && (
                  <InputWithLabel
                    id="quality_target_point_achieved"
                    label="Quality Target Point Achieved"
                    size="lg"
                    variant="filled"
                    placeholder=""
                    bg="secondary.200"
                    name="quality_target_point_achieved"
                    value={formik.values.quality_target_point_achieved}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                )}

                {(taskType === "quantitative" ||
                  taskType === "quantitative_and_qualitative") && (
                  <>
                    {!useOwnerQtySubmmision && (
                      <InputWithLabel
                        id="quantity_target_unit_achieved"
                        label="Quantity Target Point Achieved"
                        size="lg"
                        variant="filled"
                        placeholder=""
                        bg="secondary.200"
                        name="quantity_target_unit_achieved"
                        value={formik.values.quantity_target_unit_achieved}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                      />
                    )}
                    <Tooltip
                      label="Using the task owner quantity submission, means the assiggnor will be required to just add his or her remark. 
                      and the system uses the quantity submitted by the task owner for the rating"
                    >
                      <Box>
                        <Checkbox
                          onChange={(e) =>
                            setUseOwnerQtySubmmision(!useOwnerQtySubmmision)
                          }
                          isChecked={useOwnerQtySubmmision}
                        >
                          use the quantity submitted by the task owner
                        </Checkbox>
                      </Box>
                    </Tooltip>
                  </>
                )}

                <br />
                <FormControl>
                  <FormLabel htmlFor="rating_remark">Rating Remark</FormLabel>
                  <Textarea
                    id="rating_remark"
                    size="lg"
                    variant="filled"
                    placeholder="enter your remark here"
                    bg="secondary.200"
                    name="rating_remark"
                    value={formik.values.rating_remark}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                </FormControl>

                {!useSubmmisonFile && (
                  <Box>
                    <Text fontWeight="500" fontSize="sm" mb="4">
                      Upload corrected file
                    </Text>
                    <HStack mb="5">
                      <FormControl id="submission">
                        <Input
                          type="file"
                          onChange={(event) => {
                            if (event.currentTarget.files) {
                              formik.setFieldValue(
                                "submission",
                                event.currentTarget.files[0]
                              );
                            }
                          }}
                          variant="filled"
                          bg="transparent"
                        />
                      </FormControl>
                      <Text color={"crimson"}>
                        {formik.errors.use_owner_submission}
                      </Text>
                    </HStack>
                  </Box>
                )}

                <Tooltip label="Are you adopting the submitted file (report) as the final copy for report generation?">
                  <Box>
                    <Checkbox
                      onChange={(e) => setUseSubmmisonFile(!useSubmmisonFile)}
                      isChecked={useSubmmisonFile}
                    >
                      adopt Submitted File
                    </Checkbox>
                  </Box>
                </Tooltip>

                <br />
                <br />

                {false && [].length !== 0 && (
                  <Text
                    as="a"
                    colorScheme={"blue"}
                    href={
                      ([] as any[])[0].submission
                        ? ([] as any[])[0].submission
                        : "#"
                    }
                    rel="nofollow noreferrer"
                    download
                    target={"_blank"}
                  >
                    <Button fontSize={".8rem"}>
                      {([] as any[])[0].submission
                        ? "download employee uploaded file"
                        : "employee did not upload any data"}
                    </Button>
                  </Text>
                )}
              </form>
            </ModalBody>
            <ModalFooter>
              <Flex width={"100%"} justifyContent={"space-between"}>
                <Button
                  type="submit"
                  form="rate-task"
                  variant="primary"
                  w="full"
                  isLoading={isLoading}
                  loadingText="Submitting"
                  marginRight={"10px"}
                >
                  Submit
                </Button>
              </Flex>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </Box>
    </>
  );
};

export default RateTaskModal;
