import { <PERSON>, Tab, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tabs } from '@chakra-ui/react';
import { HiOutlineUserCircle } from 'react-icons/hi';
import AppBar from '../components/AppBar';
import JDIntitiativesSpread from '../tabs/cpm/JDInitiativesSpread';
import JDIntitiatives from "../tabs/cpm/JDInititatives";
import ObjectiveAndJDInitiativeSpread from "../tabs/cpm/ObjectiveAndJDInitiative";
import ObjectivePerspectiveSpread from "../tabs/cpm/ObjectivePerspective";
import Perspective from "../tabs/cpm/Perspective";

const Cpm = () => {
  return (
    <>
      <AppBar heading="Corporate Performance Management" />

      <Tabs colorScheme="primary" isLazy>
        <TabList>
          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
            <Box as="span" mr="2">
              <HiOutlineUserCircle size="22px" />
            </Box>
            Perspective
          </Tab>
          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
            <Box as="span" mr="2">
              <HiOutlineUserCircle size="22px" />
            </Box>
            Objective - Perspective Spread
          </Tab>
          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
            <Box as="span" mr="2">
              <HiOutlineUserCircle size="22px" />
            </Box>
            Objective KPI Spread Report
          </Tab>
          <Tab fontWeight="500" fontSize="sm" color="gray.600">
            <Box as="span" mr="2">
              <HiOutlineUserCircle size="22px" />
            </Box>
            KPI
          </Tab>
          <Tab fontWeight="500" fontSize="sm" color="gray.600">
            <Box as="span" mr="2">
              <HiOutlineUserCircle size="22px" />
            </Box>
            KPI Spread
          </Tab>
        </TabList>

        <TabPanels pt="3">
          <TabPanel px="0">
            <Perspective />
          </TabPanel>
          <TabPanel px="0">
            <ObjectivePerspectiveSpread />
          </TabPanel>
          <TabPanel px="0">
            <ObjectiveAndJDInitiativeSpread />
          </TabPanel>
          <TabPanel px="0">
            <JDIntitiatives />
          </TabPanel>
          <TabPanel px="0">
            <JDIntitiativesSpread />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </>
  );
};

export default Cpm;
