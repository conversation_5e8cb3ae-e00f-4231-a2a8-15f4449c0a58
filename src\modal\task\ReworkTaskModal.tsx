import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Image,
  MenuItem,
  Modal,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Select,
  Text,
  Textarea,
  useDisclosure,
  useToast,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import moment from "moment";
import { useEffect, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import * as yup from "yup";
import axiosInstance from "../../api";
import bagIcon from "../../assets/icons/bag-frame.svg";
import { ExcludeDaysInCalendar } from "../../components/DateInput";
import { CurrentOrgnisationSettingsType } from "../../services/list.service";

export interface ReworkTaskModal {
  rework_remark: string;
  rework_end_date: string;
  rework_end_time: string;
}

const schema = yup.object({
  rework_remark: yup.string().required("enter your remark"),
  rework_end_date: yup.string().required("enter end date"),
  rework_end_time: yup.string().required("enter end time"),
});

const ReworkTaskModal: React.FC<{
  task_id: string;
  task_type: string;
  rework_num: number;
  isOwner: boolean;
  isCorporateTeamLead: boolean;
  status: string;
}> = (props) => {
  const {
    isOpen: isReworkOpen,
    onOpen: onReworkOpen,
    onClose: onReworkClose,
  } = useDisclosure();

  const [IsLoading, setIsLoading] = useState<boolean>(false);
  const toast = useToast();
  const { showBoundary } = useErrorBoundary();
  const [CurrentOrgnisationSettings, setCurrentOrgnisationSettings] =
    useState<CurrentOrgnisationSettingsType>({
      company_name: "",
      ownerEmail: "",
      owner_first_name: "",
      owner_last_name: "",
      company_short_name: "",
      owner_phone_number: "",
      work_start_time: "",
      work_stop_time: "",
      work_break_start_time: "",
      work_break_stop_time: "",
      work_days: [],
      timezone: "",
    });

  const List_of_start_time = [
    "00:00",
    "00:30",
    "01:00",
    "01:30",
    "02:00",
    "02:30",
    "03:00",
    "03:30",
    "04:00",
    "03:30",
    "05:00",
    "05:30",
    "06:00",
    "06:30",
    "07:00",
    "07:30",
    "08:00",
    "08:30",
    "09:00",
    "09:30",
    "10:00",
    "10:30",
    "11:00",
    "11:30",
    "12:00",
    "12:30",
    "13:00",
    "13:30",
    "14:00",
    "14:30",
    "15:00",
    "15:30",
    "16:00",
    "16:30",
    "17:00",
    "17:30",
    "18:00",
    "18:30",
    "19:00",
    "19:30",
    "20:00",
    "20:30",
    "21:00",
    "21:30",
    "22:00",
    "22:30",
    "23:00",
    "23:30",
    "24:00",
    "24:30",
  ];

  const formik = useFormik<ReworkTaskModal>({
    initialValues: {
      rework_remark: "",
      rework_end_date: "",
      rework_end_time: "",
    },
    validationSchema: schema,
    onSubmit: async (values) => {
      const dataToBeSubmited = {
        rework_remark: values.rework_remark,
        rework_end_date: moment(values.rework_end_date).format("YYYY-MM-DD"),
        rework_end_time: values.rework_end_time,
      };
      await SubmitTaskApi(dataToBeSubmited);
    },
  });

  useEffect(() => {
    let org_settings = localStorage.getItem("org_info");
    if (org_settings) {
      setCurrentOrgnisationSettings(JSON.parse(org_settings));
    }
  }, []);

  const SubmitTaskApi = async (data: ReworkTaskModal) => {
    const org_name = localStorage.getItem("current_organization_short_name");

    if (org_name) {
      setIsLoading(true);
      try {
        const resp = await axiosInstance.put(
          `client/${org_name}/task/${props.task_id}/rework/`,
          data
        );

        toast({
          title: "Rework Submitted Successfully",
          status: "success",
          position: "top",
          duration: 5000,
          isClosable: true,
        });

        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } catch (err: any) {
        setIsLoading(false);
        if (err.response.status === 401) {
          showBoundary(err);
        } else if (err.response.data.status === 403) {
          toast({
            title: err.response.data.message,
            status: "error",
            position: "top",
            duration: 5000,
            isClosable: true,
          });
        } else if (err.response.status === 400) {
          toast({
            title: "Validation error occurred",
            status: "error",
            position: "top",
            duration: 5000,
            isClosable: true,
          });
        }
      }
      setIsLoading(false);
    }
  };

  const handleAbleToRework = (): boolean => {
    // if (TypeVerifierUserChecker(["team_lead"], "client_tokens")) {
    //   if (props.isCorporateTeamLead) return true;
    //   return !props.isOwner;
    // }
    // return false;
    return false;
  };

  return (
    <Box>
      <MenuItem
        w="full"
        isDisabled={
          !(
            handleAbleToRework() &&
            props.rework_num !== 0 &&
            ["awaiting_rating"].includes(props.status)
          )
        }
        onClick={onReworkOpen}
      >
        Submit Rework
      </MenuItem>

      <Modal onClose={onReworkClose} size="sm" isOpen={isReworkOpen} isCentered>
        <Text fontWeight="500" color="secondary.900" mb="4">
          Rework Task
        </Text>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <Flex my="3" ml="0">
              <Image src={bagIcon} ml={2} />
              <Box ml={2}>
                <Text as="h1" fontWeight="500">
                  Rework Task
                </Text>
                <Text as="h2" fontSize="md" color="gray.600">
                  {/* Start to set up your organisation structure here */}
                </Text>
              </Box>
            </Flex>
          </ModalHeader>
          <ModalCloseButton size="xs" />
          <ModalBody>
            <form id="corporate-level" onSubmit={formik.handleSubmit}>
              <FormControl>
                <FormLabel htmlFor="rework_remark">Rework Remark</FormLabel>
                <Textarea
                  id="rework_remark"
                  required
                  size="lg"
                  variant="filled"
                  placeholder="enter your remark here"
                  bg="secondary.200"
                  name="rework_remark"
                  value={formik.values.rework_remark}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
              </FormControl>
              {formik.errors.rework_remark && (
                <Text color="crimson" fontSize="sm">
                  {formik.errors.rework_remark}
                </Text>
              )}

              <br />
              <br />

              <FormControl>
                <FormLabel
                  htmlFor="structure_level"
                  fontSize="xs"
                  fontWeight="500"
                >
                  Rework End Date
                </FormLabel>
                <ExcludeDaysInCalendar
                  days_array={[...CurrentOrgnisationSettings.work_days].map(
                    (num) => num + 1
                  )}
                  disabled={false}
                  name="rework_end_date"
                  value={formik.values.rework_end_date}
                  onChange={async (date: string) => {
                    await formik.setFieldValue("rework_end_date", date);
                    return Promise.resolve();
                  }}
                  placeholder="Enter End Date"
                  formErrorMessage={
                    formik.errors.rework_end_date ? "End Date is required" : ""
                  }
                  dateFormat="yyyy/MM/dd"
                />
              </FormControl>

              <br />

              <FormControl>
                <FormLabel fontSize="xs" fontWeight="500">
                  Rework End Time
                </FormLabel>
                <Select
                  id="rework_end_time"
                  variant="filled"
                  placeholder="Rework End Time"
                  name="rework_end_time"
                  value={formik.values.rework_end_time}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                >
                  {List_of_start_time.map((start_time_data, index) => (
                    <option value={start_time_data} key={index}>
                      {start_time_data}
                    </option>
                  ))}
                </Select>
                {formik.errors.rework_end_time && (
                  <Text fontSize="xs" color="crimson">
                    {formik.errors.rework_end_time}
                  </Text>
                )}
              </FormControl>
            </form>
          </ModalBody>
          <ModalFooter>
            <Flex width={"100%"} justifyContent={"space-between"}>
              <Button
                type="submit"
                form="corporate-level"
                variant="primary"
                w="full"
                isLoading={IsLoading}
                loadingText="Submitting"
              >
                Submit
              </Button>
            </Flex>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default ReworkTaskModal;
