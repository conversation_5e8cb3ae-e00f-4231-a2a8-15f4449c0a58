import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Flex,
  Input,
  Text,
  useToast,
} from "@chakra-ui/react";
import { useRef, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import { HiOutlinePlus } from "react-icons/hi";
import uploadDocument<PERSON>and<PERSON> from "../utils/uploadClass";

const UploadLeaveBatchTemplate = () => {
  const [selectedFile, setSelectedFile] = useState<File>();
  const [isFilePicked, setIsFilePicked] = useState(false);
  const toast = useToast();
  const fileInput = useRef<HTMLInputElement>(null);
  const showBoundary = useErrorBoundary();
  const handleFileUpload = () => {
    // trigger a click event on the file input
    if (fileInput.current !== null) {
      fileInput.current.click();
    }
  };

  const doc = new uploadDocumentHandler(
    toast,
    "/leave-management/hr-leave-management/bulk_create_leave/",
    "leave-managment",
    selectedFile,
    isFilePicked,
    setSelectedFile,
    setIsFilePicked,
    showBoundary
  );

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader>Upload Leave In Batch</DrawerHeader>
      <DrawerBody>
        <Text>Upload document in .xlsx or .xls format</Text>
        <form id="upload-employee-form" onSubmit={(e) => doc.handleSubmit(e)}>
          <Flex
            w="400px"
            h="150px"
            border="dashed"
            borderColor="gray.400"
            borderRadius="md"
            // p="10"
            mt="4"
            align="center"
            justify="center"
          >
            <Input
              display="none"
              multiple={false}
              name="employee_template_file"
              type="file"
              ref={fileInput}
              onChange={(e) => doc.handleFileChange(e)}
            />
            <Box>
              <Button
                variant="outline"
                color="primary"
                leftIcon={<HiOutlinePlus color="gray.400" />}
                onClick={handleFileUpload}
              >
                Choose File
              </Button>
              <Box as="span">
                {isFilePicked && selectedFile !== undefined
                  ? selectedFile.name
                  : null}
              </Box>
            </Box>
          </Flex>
        </form>
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          form="upload-employee-form"
          variant="primary"
          w="full"
        >
          Upload File
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UploadLeaveBatchTemplate;
