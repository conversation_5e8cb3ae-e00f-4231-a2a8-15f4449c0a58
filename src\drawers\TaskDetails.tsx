import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Text,
  SimpleGrid,
  <PERSON>ack,
  <PERSON><PERSON>r,
  Flex,
  Drawer<PERSON>loseButton,
} from "@chakra-ui/react";
import { TTasks } from "../types/tasks";
import { HiOutlineChevronLeft } from "react-icons/hi";

interface TaskDetailsProps {
  task: TTasks;
}

const TaskDetails: React.FC<TaskDetailsProps> = ({ task }) => {
  return (
    <>
      <DrawerHeader fontWeight="500" w="95%" mx="auto" mt="5">
        <Flex justify="space-between" alignItems="center">
          <Flex alignItems="baseline">
            <DrawerCloseButton
              as={HiOutlineChevronLeft}
              display="block"
              position="relative"
            />
            <Text mt={-1} ml={5} fontSize="lg">
              Task Details
            </Text>
          </Flex>
        </Flex>
      </DrawerHeader>
      <DrawerBody w="100%" mx="auto">
        <Box px={1} py={4}>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacingY={6} spacingX={10}>
            <Detail label="Name" value={task.name} />
            <Detail label="Upline KPI" value={task.uplineKpi} />
            <Detail label="Owner Email" value={task.ownerEmail} />
            <Detail label="Created By" value={task.createdBy} />
            <Detail label="Task Type" value={task.taskType} />
            <Detail label="Routine Type" value={task.routineType} />
            <Detail label="Start Date" value={task.startDate} />
            <Detail label="Start Time" value={task.startTime} />
            <Detail label="Duration" value={task.duration} />
            <Detail label="Repeat Every" value={task.repeatEvery} />
            <Detail
              label="Occurs On Days (Weekly)"
              value={task.occursOnDaysWeekly}
            />
            <Detail
              label="Occurs On Day Number (Monthly)"
              value={task.occursOnDayNumberMonthly}
            />
            <Detail
              label="Occurs Day Position (Monthly)"
              value={task.occursDayPositionMonthly}
            />
            <Detail
              label="Occurs On Day (Monthly)"
              value={task.occursOnDayMonthly}
            />
            <Detail label="End Date" value={task.endDate} />
            <Detail label="After Occurrence" value={task.afterOccurrence} />
            <Detail label="Rework Limit" value={task.reworkLimit} />
            <Detail
              label="Quality Target Point"
              value={task.qualityTargetPoint}
            />
            <Detail
              label="Quantity Target Point"
              value={task.quantityTargetPoint}
            />
            <Detail
              label="Quantity Target Unit"
              value={task.quantityTargetUnit}
            />
            <Detail
              label="Turnaround Time Target Point"
              value={task.turnAroundTimeTargetPoint}
            />
          </SimpleGrid>
        </Box>
      </DrawerBody>
    </>
  );
};

const Detail: React.FC<{ label: string; value: string | number }> = ({
  label,
  value,
}) => (
  <Stack spacing={0}>
    <Text fontSize="sm" color="gray.500">
      {label}
    </Text>
    <Text fontWeight="medium">{value || "—"}</Text>
  </Stack>
);

export default TaskDetails;
