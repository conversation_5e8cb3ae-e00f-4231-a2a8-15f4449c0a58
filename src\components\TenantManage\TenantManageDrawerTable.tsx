import {
  <PERSON>,
  <PERSON>er<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lex,
  Text,
} from "@chakra-ui/react";
import { HiOutlineChevronLeft } from "react-icons/hi";

type Prop = {
  data: any;
};
const TenantManageDrawerTable = ({ data }: Prop): React.ReactElement => {
  return (
    <DrawerBody w="85%" mx="auto">
      <Flex alignItems="baseline">
        <DrawerCloseButton
          as={HiOutlineChevronLeft}
          display="block"
          position="relative"
        />
        <Text mt={-1} ml={5} fontSize="lg"></Text>
      </Flex>
      <br />
      <br />

      <Box>
        <Text mb={1} fontWeight="500">
          Name of Account Manager (Client)
        </Text>
        <Text as="small" color="gray.500">
          {data.name_of_account_manager ? data.name_of_account_manager : "NiL"}
        </Text>
      </Box>
      <br />
      <Box>
        <Text mb={1} fontWeight="500">
          Tel of Account Manager
        </Text>
        <Text as="small" color="gray.500">
          {data.tel_of_account_manager ? data.tel_of_account_manager : "NiL"}
        </Text>
      </Box>

      <br />
      <Box>
        <Text mb={1} fontWeight="500">
          Email of Account Manager
        </Text>
        <Text as="small" color="gray.500">
          {data.email_of_account_manager
            ? data.email_of_account_manager
            : "NiL"}
        </Text>
      </Box>

      <br />
      <Box>
        <Text mb={1} fontWeight="500">
          Number of Users
        </Text>
        <Text as="small" color="gray.500">
          {data.employee_limit ? data.employee_limit : "NiL"}
        </Text>
      </Box>

      <br />
      <Box>
        <Text mb={1} fontWeight="500">
          Name of HR Admin
        </Text>
        <Text as="small" color="gray.500">
          {data.name_of_account_HRmanager
            ? data.name_of_account_HRmanager
            : "NiL"}
        </Text>
      </Box>

      <Box>
        <Text mb={1} fontWeight="500">
          Phone Numbers of HR Admin
        </Text>
        <Text as="small" color="gray.500">
          {data.tel_of_account_HRmanager
            ? data.tel_of_account_HRmanager
            : "NiL"}
        </Text>
      </Box>
      <br />
      <Box>
        <Text mb={1} fontWeight="500">
          Email address of HR Admin
        </Text>
        <Text as="small" color="gray.500">
          {data.email_of_account_HRmanager
            ? data.email_of_account_HRmanager
            : "NiL"}
        </Text>
      </Box>
      <br />

      <Box>
        <Text mb={1} fontWeight="500">
          Name of Super Admin
        </Text>
        <Text as="small" color="gray.500">
          {data.owner_first_name} {data.owner_last_name}
        </Text>
      </Box>

      <Box>
        <Text mb={1} fontWeight="500">
          Email address of Super Admin
        </Text>
        <Text as="small" color="gray.500">
          {data.ownerEmail} {data.ownerEmail}
        </Text>
      </Box>
      <br />
    </DrawerBody>
  );
};

export default TenantManageDrawerTable;
