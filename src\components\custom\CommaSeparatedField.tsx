import {
  Box,
  Flex,
  Input,
  Tag,
  TagClose<PERSON>utton,
  TagLabel,
} from "@chakra-ui/react";
import { useRef, useState } from "react";
import { toast } from "sonner";

const CommaSeparatedInput = ({
  name,
  formik,
  placeholder = "Enter comma-separated values...",
  selected,
}: {
  formik: any;
  placeholder?: string;
  name: string;
  selected?: string[];
}) => {
  const [input, setInput] = useState(selected?.join(", ") || "");
  const value: string[] = formik?.values[name] || [];
  const inputRef = useRef<HTMLInputElement>(null);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      const newValue = input.trim();
      if (!newValue) return;

      if (value.includes(newValue.toLowerCase())) {
        toast.info(newValue + " Already added");
        return;
      }
      formik?.setFieldValue(name, [...value, newValue.toLowerCase()]);
      localStorage.setItem(
        name,
        JSON.stringify([...value, newValue.toLowerCase()])
      );

      setInput("");
    } else if (e.key === "Backspace" && input === "") {
      formik?.setFieldValue(name, value.slice(0, -1));
      localStorage.setItem(name, JSON.stringify(value.slice(0, -1)));
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData("text");

    const pastedValues = pasteData
      .split(",")
      .map((v) => v.trim().toLowerCase())
      .filter((v) => v.length > 0);

    if (pastedValues.length === 0) return;

    const uniqueNewValues = pastedValues.filter(
      (item) => !value.includes(item)
    );

    if (uniqueNewValues.length > 0) {
      const updatedValues = [...value, ...uniqueNewValues];
      formik?.setFieldValue(name, updatedValues);
      localStorage.setItem(name, JSON.stringify(updatedValues));
    } else {
      toast.info("All pasted values already exist");
    }

    setInput(""); // Clear input after pasting
  };

  const handleDelete = (val: string) => {
    formik.setFieldValue(
      name,
      value.filter((v) => v !== val)
    );
    localStorage.setItem(name, JSON.stringify(value.filter((v) => v !== val)));
  };

  return (
    <Box
      borderWidth="1px"
      borderRadius="xl"
      p="2"
      bg="gray.50"
      minH="10"
      onClick={() => inputRef.current?.focus()}
    >
      <Flex wrap="wrap" gap="2">
        {value.map((val) => (
          <Tag key={val} borderRadius="full" variant="outline">
            <TagLabel fontSize="small">{val}</TagLabel>
            <TagCloseButton onClick={() => handleDelete(val)} />
          </Tag>
        ))}
        <Input
          ref={inputRef}
          value={input}
          onPaste={handlePaste}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          variant="unstyled"
          placeholder={placeholder}
          fontSize="sm"
          flex="1"
          minW="120px"
        />
      </Flex>
    </Box>
  );
};

export default CommaSeparatedInput;
