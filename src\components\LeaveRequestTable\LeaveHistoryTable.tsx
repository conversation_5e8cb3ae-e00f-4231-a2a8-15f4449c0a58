import React, { useState } from "react";
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Checkbox,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Button,
  Text,
  Flex,
  Badge,
} from "@chakra-ui/react";
import { SearchIcon } from "@chakra-ui/icons";

type LeaveHistory = {
  id: string | number;
  employee: string;
  role: string;
  leaveType: string;
  requestDate: string;
  status: string;
};

interface LeaveHistoryTableProps {
  data?: LeaveHistory[];
  showCheckboxes?: boolean;
  showSearch?: boolean;
  showFilter?: boolean;
  title?: string;
  onRowAction?: (item: LeaveHistory) => void;
  actionLabel?: string;
}

const LeaveHistoryTable: React.FC<LeaveHistoryTableProps> = ({
  data = [],
  showCheckboxes = true,
  showSearch = true,
  showFilter = true,
  title = "Leave History Records",
  onRowAction = () => {},
  actionLabel = "View Details",
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("All");
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);

  // Filter data based on search and status filter
  const filteredData = data.filter((item) => {
    const matchesSearch =
      item.employee.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.role.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter =
      filterStatus === "All" || item.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  // Handle individual checkbox selection
  const handleRowSelect = (id: any) => {
    setSelectedRows((prev) =>
      prev.includes(id) ? prev.filter((rowId) => rowId !== id) : [...prev, id]
    );
  };

  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectedRows.length === filteredData.length) {
      setSelectedRows([]);
    } else {
      setSelectedRows(filteredData.map((item) => item.id));
    }
  };

  // Get status badge color
  const getStatusColor = (status: any) => {
    switch (status.toLowerCase()) {
      case "pending":
        return "#FFE4C6";
      case "approved":
        return "#E1FFD3";
      case "declined":
        return "#FE0000";
      default:
        return "gray";
    }
  };

  return (
    <Box>
      {/* Header Section */}
      <Flex justifyContent="space-between" alignItems="center" mb={6}>
        <Text
          fontWeight={500}
          fontSize="1.2rem"
          color="#333"
          fontFamily="Inter"
        >
          {title}
        </Text>
        <Flex gap={4} alignItems="center">
          {showSearch && (
            <InputGroup width="500px">
              <InputLeftElement pointerEvents="none">
                <SearchIcon color="gray.300" />
              </InputLeftElement>
              <Input
                placeholder="Search Employee"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                fontSize="14px"
                borderRadius="50px"
                fontFamily="Inter"
              />
            </InputGroup>
          )}
          {showFilter && (
            <>
              <Flex gap={"1rem"} alignItems={"center"}>
                <Box>
                  <Text
                    fontSize={".7rem"}
                    fontWeight={"bold"}
                    fontFamily="Inter"
                  >
                    Filter By
                  </Text>
                </Box>
                <Select
                  placeholder="Filter By: All"
                  maxW="100px"
                  fontSize="14px"
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  fontFamily="Inter"
                >
                  <option value="All">All</option>
                  <option value="Pending">Pending</option>
                  <option value="Approved">Approved</option>
                  <option value="Cancelled">Cancelled</option>
                </Select>
              </Flex>
            </>
          )}
        </Flex>
      </Flex>

      {/* Table */}
      <Box overflowX="auto">
        <Table variant="simple">
          <Thead borderTop={"2px solid black"} borderBottom={"2px solid black"}>
            <Tr>
              {showCheckboxes && (
                <Th width="50px" py={2}>
                  <Checkbox
                    isChecked={
                      selectedRows.length === filteredData.length &&
                      filteredData.length > 0
                    }
                    isIndeterminate={
                      selectedRows.length > 0 &&
                      selectedRows.length < filteredData.length
                    }
                    onChange={handleSelectAll}
                  />
                </Th>
              )}
              <Th
                fontSize="12px"
                fontFamily="Inter"
                fontWeight={600}
                color="black"
                py={4}
              >
                Employee
              </Th>
              <Th
                fontSize="12px"
                fontFamily="Inter"
                fontWeight={600}
                color="black"
                py={4}
              >
                Role
              </Th>
              <Th
                fontSize="12px"
                fontFamily="Inter"
                fontWeight={600}
                color="black"
                py={4}
              >
                Leave Type
              </Th>
              <Th
                fontSize="12px"
                fontFamily="Inter"
                fontWeight={600}
                color="black"
                py={4}
              >
                Request Date
              </Th>
              <Th
                fontSize="12px"
                fontFamily="Inter"
                fontWeight={600}
                color="black"
                py={4}
              >
                Status
              </Th>
              <Th py={4}></Th>
            </Tr>
          </Thead>
          <Tbody>
            {filteredData.length === 0 ? (
              <Tr>
                <Td colSpan={showCheckboxes ? 7 : 6} textAlign="center" py={4}>
                  <Text color="gray.500" fontFamily="Inter">
                    No leave history records found
                  </Text>
                </Td>
              </Tr>
            ) : (
              filteredData.map((item) => (
                <Tr key={item.id} _hover={{ bg: "#F9F9F9" }}>
                  {showCheckboxes && (
                    <Td px={4} py={4}>
                      <Checkbox
                        isChecked={selectedRows.includes(item.id)}
                        onChange={() => handleRowSelect(item.id)}
                      />
                    </Td>
                  )}
                  <Td
                    px={4}
                    fontSize="12px"
                    fontWeight={600}
                    py={4}
                    fontFamily="Inter"
                  >
                    {item.employee}
                  </Td>
                  <Td
                    px={4}
                    fontSize="12px"
                    color="#666"
                    py={4}
                    fontFamily="Inter"
                  >
                    {item.role}
                  </Td>
                  <Td
                    px={4}
                    fontSize="12px"
                    color="#666"
                    py={4}
                    fontFamily="Inter"
                  >
                    {item.leaveType}
                  </Td>
                  <Td
                    px={4}
                    fontSize="12px"
                    color="#666"
                    py={4}
                    fontFamily="Inter"
                  >
                    {item.requestDate}
                  </Td>
                  <Td px={4} py={4}>
                    <Badge
                      bg={getStatusColor(item.status)}
                      px={2}
                      py={2}
                      borderRadius="50px"
                      fontSize="12px"
                      textTransform="capitalize"
                      color={
                        item.status.toLowerCase() === "pending"
                          ? "#FF8600"
                          : item.status.toLowerCase() === "approved"
                          ? "#37A600"
                          : "white"
                      }
                      fontFamily="Inter"
                    >
                      {item.status}
                    </Badge>
                  </Td>
                  <Td px={4} py={4}>
                    <Button
                      size="sm"
                      variant="ghost"
                      color="#0B3178"
                      textDecor={"underline"}
                      fontSize="12px"
                      fontWeight={500}
                      onClick={() => onRowAction(item)}
                      _hover={{ bg: "#E6F3FF" }}
                      fontFamily="Inter"
                    >
                      {actionLabel} →
                    </Button>
                  </Td>
                </Tr>
              ))
            )}
          </Tbody>
        </Table>
      </Box>

      {/* Selected items info */}
      {showCheckboxes && selectedRows.length > 0 && (
        <Box mt={4}>
          <Text fontSize="14px" color="#666" fontFamily="Inter">
            {selectedRows.length} item(s) selected
          </Text>
        </Box>
      )}
    </Box>
  );
};

export default LeaveHistoryTable;
