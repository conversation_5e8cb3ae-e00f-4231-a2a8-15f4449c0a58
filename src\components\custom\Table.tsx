import {
  Box,
  Button,
  Checkbox,
  Flex,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useBreakpointValue,
} from "@chakra-ui/react";
import { TrashIcon } from "@radix-ui/react-icons";
import { motion } from "framer-motion";
import React, { useMemo, useState } from "react";
import { GrFormNextLink, GrFormPreviousLink } from "react-icons/gr";
import { blue200 } from "../../theme/colors";
import Preloader from "../Preloader";
import CustomInput from "./customInput";

export interface TableProps<T> {
  headers: string[];
  data: T[];
  isLoading: boolean;
  onPageChange: (page: number) => void;
  pageNum: number;
  pageCount?: number;
  hasNextPage?: boolean;
  hasPreviousPage?: boolean;
  renderRow: (item: T) => React.ReactNode;
  onRowSelect?: (selectedRows: T[]) => void;
  onFilterChange?: (filter: string) => void;
  onHideRow?: (item: T) => void;
}

const MotionText = motion.create(Text);

function DataTable<T>({
  headers,
  data,
  isLoading,
  onPageChange,
  pageNum,
  renderRow,
  hasNextPage,
  hasPreviousPage,
  onRowSelect,
  onFilterChange,
  onHideRow,
}: TableProps<T>) {
  const isMobile = useBreakpointValue({ base: true, md: false });
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());
  const [filter, setFilter] = useState("");
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc";
  } | null>(null);

  const selectedArray = Array.from(selectedRows);

  const handleRowSelect = (index: number) => {
    const updatedSelection = new Set(selectedRows);
    if (updatedSelection.has(index)) {
      updatedSelection.delete(index);
    } else {
      updatedSelection.add(index);
    }
    setSelectedRows(updatedSelection);
    if (onRowSelect) {
      onRowSelect(Array.from(updatedSelection).map((i) => data[i]));
    }
  };

  const handleSelectAll = () => {
    if (selectedRows.size === data.length) {
      setSelectedRows(new Set());
      if (onRowSelect) onRowSelect([]);
    } else {
      const allSelected = new Set(data.map((_, idx) => idx));
      setSelectedRows(allSelected);
      if (onRowSelect) onRowSelect(data);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFilter(value);
    if (onFilterChange) onFilterChange(value);
  };

  const handleSort = (key: string) => {
    setSortConfig((prev) => {
      if (!prev || prev.key !== key) return { key, direction: "asc" };
      if (prev.direction === "asc") return { key, direction: "desc" };
      return null;
    });
  };

  const filteredData = useMemo(() => {
    let filtered =
      data?.filter((item) =>
        JSON.stringify(item).toLowerCase().includes(filter.toLowerCase())
      ) || [];

    if (sortConfig) {
      filtered = [...filtered].sort((a, b) => {
        console.log(filtered);
        const aVal = a[sortConfig.key as keyof T];
        const bVal = b[sortConfig.key as keyof T];

        if (typeof aVal === "string" && typeof bVal === "string") {
          return sortConfig.direction === "asc"
            ? aVal.localeCompare(bVal)
            : bVal.localeCompare(aVal);
        }

        if (typeof aVal === "number" && typeof bVal === "number") {
          return sortConfig.direction === "asc" ? aVal - bVal : bVal - aVal;
        }

        return 0;
      });
    }

    return filtered;
  }, [data, filter, sortConfig]);

  const renderMobileLayout = () => (
    <Box mt="2">
      <Checkbox
        isChecked={selectedRows.size === data?.length}
        onChange={handleSelectAll}
        fontSize="xs"
        color="gray.500"
        fontWeight="500"
      >
        Select All
      </Checkbox>
      {filteredData?.map((item, idx) => (
        <Box
          key={idx}
          borderWidth="1px"
          borderRadius="md"
          p="4"
          mb="4"
          boxShadow="sm"
        >
          <Checkbox
            isChecked={selectedRows.has(idx)}
            onChange={() => handleRowSelect(idx)}
            mb="2"
            fontSize="x-small"
            color="gray.500"
            fontWeight="500"
          >
            Select
          </Checkbox>
          {renderRow(item)}
          {onHideRow && (
            <Button
              size="small"
              mt="4"
              onClick={() => onHideRow(item)}
              colorScheme="gray.600"
            >
              Hide
            </Button>
          )}
        </Box>
      ))}
    </Box>
  );

  const renderDesktopLayout = () => (
    <Box overflowX="auto" pb="4">
      <Table size="sm" overflowX="auto" mt="4">
        <Thead>
          <Tr>
            <Th py="3">
              <Checkbox
                isChecked={selectedRows.size === data?.length}
                onChange={handleSelectAll}
              ></Checkbox>
            </Th>
            {headers.map((header, index) => {
              const key = header.toLowerCase().replace(/\s+/g, "_");

              const isSorted = sortConfig?.key === key;
              const sortIcon = isSorted
                ? sortConfig?.direction === "asc"
                  ? " 🔼"
                  : " 🔽"
                : "";
              return (
                <Th
                  key={index}
                  py="2"
                  flexShrink="0"
                  fontWeight="600"
                  textTransform="capitalize"
                  isTruncated
                  color={blue200}
                  cursor="pointer"
                  onClick={() => handleSort(key)}
                >
                  {header}
                  <Text as="span">{sortIcon}</Text>
                </Th>
              );
            })}
            {onHideRow && <Th>Actions</Th>}
          </Tr>
        </Thead>
        <Tbody>
          {filteredData.map((item, idx) => (
            <Tr key={idx}>
              <Td fontSize="xs">
                <Checkbox
                  isChecked={selectedRows.has(idx)}
                  onChange={() => handleRowSelect(idx)}
                  fontSize="xs"
                  fontWeight="500"
                />
              </Td>
              {renderRow(item)}
              {onHideRow && (
                <Td>
                  <Button
                    size="xs"
                    onClick={() => onHideRow(item)}
                    colorScheme="red"
                  >
                    Hide
                  </Button>
                </Td>
              )}
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );

  return (
    <Flex flexDirection="column" gap="2" w="100%">
      {
        <Flex
          mb="2"
          gap={{ base: "2", md: "4" }}
          mr={{ base: "o", md: "0.5" }}
          w={{ base: "98%", md: "400px" }}
          alignSelf={{ base: "center", md: "flex-end" }}
          alignItems="center"
        >
          {data?.length > 0 && (
            <CustomInput
              placeholder="search..."
              value={filter}
              onChange={handleFilterChange}
              name="search"
              onBlur={handleFilterChange}
              type="search"
            />
          )}

          {selectedArray?.length > 0 && (
            <Button
              bg="red"
              color="white"
              size="sm"
              variant="plain"
              _hover={{ opacity: 0.5 }}
              transition="all 0.35s"
              fontWeight="500"
              onClick={() => {}}
              leftIcon={<TrashIcon />}
              flexShrink="0"
              w="fit-content"
            >
              <Text fontSize="xs">
                Delete selected{" "}
                <MotionText
                  as="span"
                  key={selectedArray?.length}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4 }}
                >
                  {selectedArray?.length > 20 ? "20+" : selectedArray?.length}
                </MotionText>
              </Text>
            </Button>
          )}
        </Flex>
      }
      {isLoading ? (
        <Preloader />
      ) : filteredData?.length <= 0 ? (
        <MotionText
          textAlign="center"
          color="gray.400"
          my="10"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          No data available
        </MotionText>
      ) : (
        <>
          {isMobile ? renderMobileLayout() : renderDesktopLayout()}
          {/* Pagination Controls */}
          {filteredData?.length > 0 && (
            <Flex justifyContent="center" gap="4" mt="8" wrap="wrap">
              {hasPreviousPage && (
                <Button
                  onClick={() => onPageChange(pageNum - 1)}
                  leftIcon={<GrFormPreviousLink />}
                  fontSize="small"
                >
                  Previous
                </Button>
              )}
              {hasNextPage && (
                <Button
                  onClick={() => onPageChange(pageNum + 1)}
                  rightIcon={<GrFormNextLink />}
                  fontSize="small"
                >
                  Next
                </Button>
              )}
            </Flex>
          )}
        </>
      )}
    </Flex>
  );
}

export default DataTable;
