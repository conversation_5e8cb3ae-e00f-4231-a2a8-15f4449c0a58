import { useState } from "react"; // Add useState import
import {
  Box,
  Flex,
  Heading,
  Image,
  ListIcon,
  ListItem,
  Text,
  UnorderedList,
} from "@chakra-ui/react";
import { CalendarIcon, HomeIcon } from "@radix-ui/react-icons";
import { motion } from "framer-motion";
import {
  HiOutlineArrowSmRight,
  HiOutlineChartSquareBar,
  HiOutlineChatAlt2,
  HiOutlineClipboardList,
  HiOutlineCog,
  HiOutlineCollection,
  HiOutlineLogout,
  HiUserGroup,
} from "react-icons/hi";
import { RiAdminLine, RiProfileLine } from "react-icons/ri";
import { SiGoogleanalytics } from "react-icons/si";
import { useMediaQuery } from "react-responsive";
import { useLocation, useNavigate } from "react-router-dom";
import EmetricsLogo from "../assets/images/logo.png";
import { useCurrentOrganization } from "../hooks/organization";
import { useGetCurrentUser } from "../hooks/user";
import { deepBlue } from "../theme/colors";
import { TRole } from "../types/user";
import { removeSession } from "../utils/session";
import NavItem from "./NavItem";
import { CustomTooltip } from "./custom/Tooltip";

const MotionBox = motion.create(Box);
const MotionFlex = motion.create(Flex);
const MotionUnorderedList = motion.create(UnorderedList);

const Sidebar = ({ closeSidebar }: { closeSidebar: () => void }) => {
  const navigate = useNavigate();
  const location = useLocation();

  let { data: currentOrganization } = useCurrentOrganization();
  currentOrganization = currentOrganization?.data;

  const { data: currentUser } = useGetCurrentUser();

  const isTabletView = useMediaQuery({ minWidth: 768, maxWidth: 1536 });

  // State to manage the open/closed state of Leave Management menu
  const [isLeaveManagementOpen, setIsLeaveManagementOpen] = useState(false);

  const handleLogout = () => {
    closeSidebar();
    removeSession();
    navigate("/logout");
  };

  const routes: {
    roles: TRole[];
    path: string;
    icon: any;
    label: string;
    children?: { path: string; label: string }[]; // Add children property
  }[] = [
    {
      roles: ["employer", "hr"],
      path: "/organization",
      icon: HomeIcon,
      label: "Organization Structure",
    },
    {
      roles: ["employer", "hr"],
      path: "/organization/structure",
      icon: RiAdminLine,
      label: "Admin Panel",
    },
    {
      roles: ["employer", "hr"],
      path: "/organization/people",
      icon: HiUserGroup,
      label: "People",
    },
    {
      roles: ["employer", "hr"],
      path: "/dashboard",
      icon: HiOutlineChartSquareBar,
      label: "Dashboard",
    },
    {
      roles: ["employer", "hr", "employee", "teamLead"],
      path: "/dashboard/strategy-deck",
      icon: HiOutlineCollection,
      label: "Strategy Deck",
    },
    {
      roles: ["employer", "teamLead"],
      path: "/dashboard/team-lead-kpi",
      icon: SiGoogleanalytics,
      label: "KPI",
    },
    {
      roles: ["admin", "hr", "employer"],
      path: "/dashboard/tasks",
      icon: HiOutlineClipboardList,
      label: "Tasks",
    },
    {
      roles: [],
      path: "/dashboard/task-calendar",
      icon: CalendarIcon,
      label: "Time Sheet",
    },
    {
      roles: ["admin", "employer"],
      path: "/dashboard/corporate-report",
      icon: HiOutlineChartSquareBar,
      label: "CPM",
    },
    {
      roles: ["admin", "employer"],
      path: "/dashboard/leave-management",
      icon: HiOutlineArrowSmRight,
      label: "Leave Management",
      children: [
        {
          path: "/dashboard/leave-management/request-leave",
          label: "Request Leave",
        },
        {
          path: "/dashboard/leave-management/pending-leave",
          label: "Pending Leave",
        },
        {
          path: "/dashboard/leave-management/trigger-leave-payment",
          label: "Trigger Leave Payment",
        },
        {
          path: "/dashboard/leave-management/leave-history",
          label: "Leave History",
        },
      ],
    },
    {
      roles: ["admin", "hr", "employer"],
      path: "/dashboard/human-performance-management",
      icon: HiOutlineChartSquareBar,
      label: "HPM",
    },
    {
      roles: ["admin", "hr", "employer"],
      path: "/dashboard/messages",
      icon: HiOutlineChatAlt2,
      label: "Messages",
    },
    {
      roles: ["admin", "hr", "employer"],
      path: "/dashboard/settings",
      icon: HiOutlineCog,
      label: "Settings",
    },
    {
      roles: ["admin", "hr", "employer"],
      path: "/dashboard/profile",
      icon: RiProfileLine,
      label: "Profile",
    },
  ];

  return (
    <>
      <MotionFlex
        direction="row"
        alignItems="center"
        gap={2}
        flexShrink={0}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        viewport={{ once: true }}
      >
        <CustomTooltip
          tip={isTabletView && currentOrganization?.name}
          placement="right"
        >
          <Image
            src={
              currentOrganization?.logo
                ? currentOrganization?.logo
                : EmetricsLogo
            }
            height={8}
            width={currentOrganization?.logo ? 8 : "auto"}
            aspectRatio="auto"
            borderRadius="8"
            alt={currentOrganization?.name || "E-Metrics Suite"}
          />
        </CustomTooltip>

        {!isTabletView && (
          <Heading
            as="h1"
            fontWeight="500"
            fontSize="large"
            mt=".1rem"
            textTransform="capitalize"
            color={deepBlue}
            isTruncated
          >
            {currentOrganization?.name || ""}
          </Heading>
        )}
      </MotionFlex>

      <MotionBox flex={1} overflowY="auto">
        <MotionUnorderedList
          styleType="none"
          display="flex"
          w="100%"
          ml="0"
          pb="8"
          flexDirection="column"
          overflowY="auto"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.4 }}
        >
          {routes.map(({ roles, path, icon, label, children }, index) =>
            roles.includes(currentUser?.role as TRole) || roles.length === 0 ? (
              <MotionBox
                key={path}
                m="0"
                w="100%"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.05 * index }}
              >
                <CustomTooltip
                  tip={isTabletView ? label : ""}
                  placement="right"
                  w="100%"
                >
                  <Box
                    onClick={
                      path === "/dashboard/leave-management"
                        ? () => setIsLeaveManagementOpen(!isLeaveManagementOpen)
                        : undefined
                    }
                  >
                    <NavItem
                      to={path}
                      icon={icon}
                      isActive={location.pathname === path}
                      toggleSidebar={closeSidebar}
                      isVisible={!isTabletView}
                    >
                      {label}
                    </NavItem>
                  </Box>
                </CustomTooltip>
                {/* Render child links for Leave Management */}
                {children && path === "/dashboard/leave-management" && isLeaveManagementOpen && (
                  <MotionUnorderedList
                    styleType="none"
                    ml={isTabletView ? 6 : 8} // Indent child links
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    {children.map((child, childIndex) => (
                      <MotionBox
                        key={child.path}
                        initial={{ opacity: 0, x: 10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: 0.1 * childIndex }}
                      >
                        <CustomTooltip
                          tip={isTabletView ? child.label : ""}
                          placement="right"
                          w="100%"
                        >
                          <NavItem
                            to={child.path}
                            icon={HiOutlineArrowSmRight} // Use a consistent icon or customize as needed
                            isActive={location.pathname === child.path}
                            toggleSidebar={closeSidebar}
                            isVisible={!isTabletView}
                          >
                            {child.label}
                          </NavItem>
                        </CustomTooltip>
                      </MotionBox>
                    ))}
                  </MotionUnorderedList>
                )}
              </MotionBox>
            ) : null
          )}
          <ListItem bg="transparent" py="1">
            <CustomTooltip
              tip={isTabletView ? "Logout" : ""}
              placement="right"
              w="100%"
            >
              <Flex
                alignItems="center"
                justify="start"
                w="fit-content"
                onClick={handleLogout}
                cursor="pointer"
                color="red"
                fontSize="small"
                fontWeight="400"
                mr="8"
                px="2"
                py="4"
              >
                <ListIcon as={HiOutlineLogout} fontSize="large" />
                {!isTabletView && <Text as="span">Logout</Text>}
              </Flex>
            </CustomTooltip>
          </ListItem>
        </MotionUnorderedList>
      </MotionBox>

      <Flex alignItems="center" flexShrink={0} pt="1" gap="2">
        <Image src={EmetricsLogo} h="20px" alt="e-metric suite's logo" />
        {!isTabletView && (
          <Text fontSize="x-small" color="gray.500" isTruncated>
            Powered by E-Metrics Suite
          </Text>
        )}
      </Flex>
    </>
  );
};

export default Sidebar;