import { useQuery } from "@tanstack/react-query";
import { useEffect, useMemo, useState } from "react";
import { getCurrentOrganization, setupOrganization } from "../api/organization";
import {
  OrganizationStructureType,
  TOrganization,
} from "../types/organization";
import { TStructuralLevel } from "../types/structuralLevel";
import {
  getCustomStructureLevel,
  getDefaultStructureLevel,
} from "../utils/structuralLevel";
import { useCrudUpdate } from "./useCrud";
import { useGetStructuralLevels } from "./useStructure";

export const currentOrganizationKey = ["current-organization"];

export const useCurrentOrganization = () => {
  return useQuery({
    queryKey: currentOrganizationKey,
    queryFn: getCurrentOrganization,
    // placeholderData: (previousData) => previousData,
    // staleTime: 5 * 60 * 1000,
  });
};

export const useToggleOrganizationStructureType = () => {
  return (x: any, y: any, z: any, sl: any) =>
    useCrudUpdate(currentOrganizationKey, () => setupOrganization(x, y, z, sl));
};

export const useGetOrganizationStructures = () => {
  const { data: structuresResponse } = useGetStructuralLevels();


  const structuralLevels = useMemo(() => {
    return (structuresResponse?.data?.data as TStructuralLevel[]) || [];
  }, [structuresResponse]);

  const currentOrganizationStructure = useMemo(() => {
    return {
      defaultStructures: getDefaultStructureLevel(structuralLevels),
      customStructures: getCustomStructureLevel(structuralLevels),
      allStructures: structuralLevels,
    };
  }, [structuralLevels]);

  return currentOrganizationStructure;
};

export const useOrganizationStructure = (): TStructuralLevel[] => {
  const { data: organizationResponse } = useCurrentOrganization();
  const { customStructures, defaultStructures } =
    useGetOrganizationStructures();

  const currentOrganization = useMemo(() => {
    return organizationResponse?.data as TOrganization | undefined;
  }, [organizationResponse]);

  const currentOrganizationStructure = useMemo(() => {
    if (!currentOrganization) return [];

    return currentOrganization.structureType ===
      OrganizationStructureType.default
      ? defaultStructures
      : customStructures;
  }, [currentOrganization]);

  return currentOrganizationStructure;
};



export const useGetOrgStructureAndTiers = (
  formik: any,
  tierFieldName = "tierName"
) => {
  const [selectedStructureLevel, setSelectedStructureLevel] = useState("");
  const [currentTiers, setCurrentTiers] = useState([]);

  const structureLevelData = useOrganizationStructure();
  const structureLevels = useMemo(() => {
    const levels = structureLevelData;

    const structureLevelNames = levels?.map((item: any) => ({
      name: item.name,
      id: item.id,
    }));
    const structureLevelTiersMap = levels?.reduce((acc: any, item: any) => {
      acc[item?.name] = item?.tiers;
      return acc;
    }, {} as Record<string, string[]>);

    return { levels: structureLevelNames, tiers: structureLevelTiersMap };
  }, [structureLevelData]);

  useEffect(() => {
    if (selectedStructureLevel) {
      const tiers = structureLevels?.tiers[selectedStructureLevel] || [];
      setCurrentTiers(tiers); // Update the current tiers state

      // Manually set the Formik value for tiers, if available
      if (tiers.length > 0) {
        formik.setFieldValue(tierFieldName, tiers[0]); // Set default tier value if any
      } else {
        formik.setFieldValue(tierFieldName, ""); // Set empty value if no tiers
      }
    }
  }, [selectedStructureLevel, structureLevels?.tiers]);

  return {
    structureLevels: structureLevels.levels,
    tiers: structureLevels.tiers,
    setSelectedLevel: setSelectedStructureLevel,
    currentTiers,
  };
};
