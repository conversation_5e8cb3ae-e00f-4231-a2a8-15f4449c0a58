import { Box, Button } from "@chakra-ui/react";
import DOMPurify from "dompurify";
import { useState } from "react";

export const MarkdownComponent = ({
  markdownContent = "",
  markdownTitle = "",
  isDescription = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const sanitizedDescriptionMarkdown = DOMPurify.sanitize(markdownContent);

  return (
    <Box as="section">
      <Box
        sx={{
          overflow: "hidden",
          display: "-webkit-box",
          WebkitBoxOrient: "vertical",
          WebkitLineClamp: isDescription ? 5 : "unset",
        }}
        dangerouslySetInnerHTML={{
          __html: sanitizedDescriptionMarkdown,
        }}
      />
      {isDescription && sanitizedDescriptionMarkdown.length >= 80 && (
        <Button size="sm" variant="link" mt={2} onClick={() => setIsOpen(true)}>
          See more
        </Button>
      )}

      {/* <ReusableModal
        isOpen={isOpen}
        size="4xl"
        onClose={() => setIsOpen(false)}
        title={markdownTitle}
      >
        <Box
          dangerouslySetInnerHTML={{
            __html: sanitizedDescriptionMarkdown,
          }}
        />
      </ReusableModal> */}
    </Box>
  );
};
