import {
  Box,
  Checkbox,
  Grid,
  IconButton,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Tab,
  Table,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useToast,
} from "@chakra-ui/react";
import { useEffect, useLayoutEffect, useMemo, useState } from "react";
import { HiDotsHorizontal } from "react-icons/hi";
import { Link as ReactRouterLink } from "react-router-dom";
import axiosInstance from "../../../api";
import CardList from "../../../components/CardList";
import PaginatedItems from "../../../components/Pagination/Pagination";
import Preloader from "../../../components/Preloader";
import CustomDrawer from "../../../drawers/CustomDrawer";
import TaskDetailDrawers from "../../../drawers/TaskDetailDrawers";
import { useCurrentOrganization } from "../../../hooks/organization";
import { useCheckUserType } from "../../../hooks/useCheckUserType";
import RateTaskModal from "../../../modal/task/RateTaskModal";
import ReworkTaskModl from "../../../modal/task/ReworkTaskModal";
import { getMyInfo } from "../../../services/auth.service";
import { setTimeFilter } from "../../../services/extraFunctions";

const TaskTable = ({
  tasks,
  currentTabNumber = 0,
  setPage,
  page_count,
}: {
  tasks: any;
  currentTabNumber: number;
  LoadingTask: boolean;
  page: number;
  nextPage?: boolean;
  previousPage?: boolean;
  setPage: (e: number) => void;
  page_count: number;
}) => {
  const tabsStatusObj: any = {
    0: "pending",
    1: "active",
    2: "over_due",
    3: "awaiting_rating",
    4: "rework",
    5: "rework_over_due",
    6: "closed",
  };
  type statusType =
    | "pending"
    | "active"
    | "over_due"
    | "awaiting_rating"
    | "rework"
    | "rework_over_due"
    | "closed";
  const [status, setStatus] = useState<statusType>(
    tabsStatusObj[currentTabNumber]
  );
  const [isLoading, setIsLoading] = useState(false);
  const logginUserData: any = useMemo(() => getMyInfo(), []);
  const toast = useToast();

  // console.log({"logginUserData":logginUserData.uuid})
  // console.log({
  //   "tasks":tasks
  // })
  // useEffect(()=>{

  // setStatus(tabsStatusObj[currentTabNumber])
  // },[currentTabNumber])
  // console

  const handleDelete = async (data: {
    routine_option: string;
    taskID: string;
  }) => {
    const ORG_NAME = localStorage.getItem("current_organization_short_name");
    let is_recurring = false;
    if (!ORG_NAME) return;
    if (window.confirm("Are You Sure You want to delete")) {
      // if(window.confirm(''))
      if (data.routine_option !== "once") {
        //if it not once ask the user if you only want to delete this item or following re-coocuring item
        if (
          window.confirm(
            "Do you only want to delete this item and following re-coocuring item "
          )
        ) {
          console.log("deleted task", data, "delete re-occuring");
          is_recurring = true;
        } else {
          //i just want to delete one out of the re-occuring
          console.log("deleted task", data, "delete once");
        }
      } else {
        console.log("deleted task", data, "delete once");
      }
    }
    setIsLoading(true);
    console.log("Renderer");
    try {
      const resp = await axiosInstance.delete(
        `/client/${ORG_NAME}/task/${data.taskID}/${
          is_recurring ? "?recurring=True" : ""
        }`
      );
      console.log({ "deleted data task": resp });
      setIsLoading(false);
      if (resp.status === 204) {
        toast({
          title: "Deleted Successfully",
          status: "success",
          position: "top",
          duration: 3000,
          isClosable: true,
        });

        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (err: any) {
      setIsLoading(false);

      console.log({ err });
      if (err.response.status == 404) {
        toast({
          title: "Deleted Succefully",
          status: "error",
          position: "top",
          duration: 3000,
          isClosable: true,
        });

        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        toast({
          title: "Some Error Occured Successfully",
          status: "error",
          position: "top",
          duration: 3000,
          isClosable: true,
        });
      }
    }
  };
  return (
    <>
      {tasks.length != 0 ? (
        <Table
          size="sm"
          // variant="unstyled"
          borderRadius="lg"
          display="block"
          overflow="scroll"
          // border-collapse={"collapse"}
          variant="striped"
          style={{ width: "100%" }}
        >
          <Thead bg="gray.200">
            <Tr style={{ textTransform: "capitalize" }}>
              <Th></Th>
              <Th
                //  px="5"
                // paddingRight={"30px"}
                px={"0"}
                textTransform={"capitalize"}
              >
                Task Name
              </Th>

              <Th px={"0"} textTransform={"capitalize"}>
                Task Type
              </Th>

              <Th px={"0"} textTransform={"capitalize"}>
                {/* Start Date and Start Time */}
                Start Time
              </Th>
              <Th px={"0"} textTransform={"capitalize"}>
                Routine Option
              </Th>
              {/* <Th fontWeight="500" px="3">
            Routine Round
          </Th> */}
              <Th px={"0"} textTransform={"capitalize"}>
                Brief
              </Th>
              <Th textTransform={"capitalize"}>
                {/* QlyTP */}
                QlyTarget point
              </Th>

              <Th textTransform={"capitalize"}>
                {/* QtyTU */}
                QtyTarget Unit
              </Th>
              <Th textTransform={"capitalize"}>
                {/* QtyTP */}
                QtyTarget Point
              </Th>
              <Th px={"0"} textTransform={"capitalize"}>
                Status
              </Th>

              <Th></Th>
            </Tr>
          </Thead>
          <Tbody>
            {tasks
              //we dont need to filter by status again we handled that already with the getTask function
              .map((task: any) => (
                <Tr>
                  <Td>
                    <Checkbox disabled={status === "closed"} />
                  </Td>
                  <Td>
                    <Text fontSize="xs">
                      {task.name} ({task.routine_round})
                    </Text>
                  </Td>

                  <Td>
                    <Text>
                      {task.task_type === "quantitative_and_qualitative"
                        ? "Quantitative And Qualitative"
                        : ""}
                      {task.task_type === "qualitative" ? "Qualitative" : ""}
                      {task.task_type === "quantitative" ? "Quantitative" : ""}
                    </Text>
                  </Td>

                  <Td>
                    <Text>{task.start_date}</Text>
                    <Text>{task.start_time}</Text>
                  </Td>

                  <Td>
                    <Text>{task.routine_option}</Text>
                  </Td>

                  {/* <Td fontSize="xs" px="3">
            <Text>{task.routine_round?task.routine_round:"No Routine Round"}</Text>
          </Td>    */}

                  <Td>
                    <a
                      // href={encodeURIComponent(createDownloadableFile(task.target_brief))}
                      href={task.target_brief}
                      style={{
                        color: task.target_brief ? "blue" : "gray",
                        cursor: "pointer",
                      }}
                      rel="nofollow noreferrer"
                      download
                    >
                      Download Brief
                    </a>
                  </Td>
                  <Td>{task.quality_target_point}</Td>
                  <Td>{task.quantity_target_unit}</Td>
                  <Td>{task.quantity_target_point}</Td>
                  {/* <Td fontSize="xs" px="3">
            {task.average_system_based_score}
          </Td> */}
                  <Td style={{ textAlign: "center" }}>
                    <Text
                      fontSize={"small"}
                      style={{
                        textAlign: "center",
                        //
                        backgroundColor:
                          task.task_status === "active"
                            ? "#0ba30bd"
                            : task.task_status === "closed"
                            ? "red"
                            : "#D97706",
                        color: "whitesmoke",
                        padding: ".2rem 0",
                        borderRadius: "5px",
                      }}
                    >
                      {task.task_status.replace("_", " ")}
                    </Text>
                  </Td>

                  <Td cursor="pointer" px="3">
                    <Menu>
                      <MenuButton
                        as={IconButton}
                        aria-label="Options"
                        icon={<HiDotsHorizontal />}
                        variant="outline"
                      />
                      <MenuList>
                        <CustomDrawer showModalBtnText="View" drawerSize="md">
                          {/* <p>Hello world</p> */}
                          <TaskDetailDrawers {...task} />
                        </CustomDrawer>

                        {/* {
            logginUserData===false?//this means the user needs to relogin to get this data
            "":

            (
              logginUserData?.user_id===task.upline_initiative.owner.user_id?"u cant submit ur task":

              <MenuItem to={`/dashboard/submit-task/${task.task_id}/${task.rework_limit}/${task.task_type}/${task.name}`} as={ReactRouterLink}>
                Submit
              </MenuItem>
            )   } */}

                        <MenuItem //if it is inside this list allow submssion
                          isDisabled={
                            !(
                              task.upline_initiative.owner?.user_id ===
                                logginUserData.uuid &&
                              [
                                "over_due",
                                "rework",
                                "rework_over_due",
                                "active",
                              ].includes(status)
                            )
                          }
                          to={`/dashboard/submit-task/${JSON.stringify(
                            task.upline_initiative.owner?.user_id ===
                              logginUserData.uuid
                          )}/${task.task_id}/${task.rework_limit}/${
                            task.task_type
                          }/${encodeURIComponent(task.name)}`}
                          as={ReactRouterLink}
                        >
                          Submit
                        </MenuItem>

                        <MenuItem
                          // (task.upline_initiative.owner?.user_id ===logginUserData.uuid)&&                                                                        //if it is inside this list allow submssion
                          isDisabled={
                            ![
                              "over_due",
                              "rework",
                              "rework_over_due",
                              "awaiting_rating",
                              "closed",
                            ].includes(status)
                          }
                          //encodeURIComponent help me fix the URIError-> there was a percentage in the task.name
                          to={`/dashboard/submit-task/${JSON.stringify(
                            task.upline_initiative.owner?.user_id ===
                              logginUserData.uuid
                          )}/${task.task_id}/${task.rework_limit}/${
                            task.task_type
                          }/${encodeURIComponent(task.name)}`}
                          as={ReactRouterLink}
                        >
                          List Of Submitted task
                        </MenuItem>

                        <>
                          {/* <MenuItem 
                  onClick={onOpen}

                  >Rate</MenuItem> */}

                          {/* <Button size="sm"
                  leftIcon={<RiSendPlaneFill />}
                  variant="primary"
                  // disabled={item?.used_submission?false:true}
                  >
                  Submit Task
          </Button> */}

                          <RateTaskModal
                            task_id={task.task_id}
                            task_type={task.task_type}
                            isOwner={
                              task.upline_initiative?.owner?.user_id ===
                              logginUserData.uuid
                            }
                            isCorporateTeamLead={
                              task.upline_initiative.assignor === null
                            }
                            status={status}
                            quantity_target_point={task.quantity_target_point}
                            quality_target_point={task.quality_target_point}
                          />
                        </>
                        <ReworkTaskModl
                          isOwner={
                            task.upline_initiative?.owner?.user_id ===
                            logginUserData.uuid
                          }
                          isCorporateTeamLead={
                            task.upline_initiative.assignor === null
                          }
                          status={status}
                          task_id={task.task_id}
                          task_type={task.task_type}
                          rework_num={task.rework_limit}
                        />

                        <MenuItem
                          // type="submit"
                          // form="rate-task"
                          // @ts-ignore
                          variant="ghost"
                          w="full"
                          isDisabled={true}
                        >
                          Edit
                        </MenuItem>

                        <MenuItem
                          // @ts-ignore
                          variant="ghost"
                          style={{ color: "red" }}
                          w="full"
                          isDisabled={
                            task.upline_initiative?.owner?.user_id ===
                            logginUserData.uuid
                          }
                          onClick={() => {
                            handleDelete({
                              taskID: task.task_id,
                              routine_option: task.routine_option,
                            });
                          }}
                        >
                          Delete
                        </MenuItem>

                        {/* <MenuItem

  disabled={!(task.upline_initiative?.owner?.user_id ===logginUserData.uuid)}
  onClick={()=>{
    let organizationName = localStorage.getItem("current_organization_short_name")
    if(!organizationName) return
    dispatch(deleteTask({"ORG_NAME":organizationName,uuid:task.task_id}))
  }}
  >
    
 
  </MenuItem> */}
                      </MenuList>
                    </Menu>
                  </Td>
                </Tr>
              ))}
          </Tbody>
        </Table>
      ) : (
        <Text></Text>
      )}

      {/* <br />
<Button disabled={!previousPage}
onClick={(e)=>setPage(page==1?1:page-1)}
leftIcon={ <GrFormPreviousLink/>}></Button>

<Button disabled={!nextPage}leftIcon={<GrFormNextLink/>}
onClick={(e)=>setPage(page+1)}></Button> */}
      {/* </Box> */}

      {/* page_count */}
      <br />

      <Box>
        <PaginatedItems
          pageCount={page_count}
          onPageClick={(pageberClicked) => {
            console.log(pageberClicked);
            setPage(pageberClicked);
          }}
        />
      </Box>
    </>
  );
};

type TaskListTableType = {
  ownerEmail: string;
  isInTaskPage?: boolean;
  isGet_allTeamMemberTask?: boolean;
  admin_lookUp?: string;
  useLookup?: boolean;
};

const TaskListTable = ({
  ownerEmail,
  isInTaskPage = true,
  admin_lookUp,
}: TaskListTableType) => {
  const [page, setPage] = useState(1);

  const isAdmin = useCheckUserType(["admin", "employer", "hr"]);

  const [nextPage, setNextPage] = useState(false);
  const [previousPage, setPreviousPage] = useState(false);
  const [pageCount, setPageCount] = useState(0);
  const [TabStatusIndex, setStatusTabIndex] = useState<number>(1);
  const [LoadingTask, setLoadingTask] = useState<boolean>(false);
  const [start_date_before__Future, setStart_date_before__Future] =
    useState<string>("");
  const [start_date_after__Future, setStart_date_after__Future] =
    useState<string>("");

  const [start_date_before__Past, setStart_date_before__Past] =
    useState<string>("");
  const [start_date_after__Past, setStart_date_after__Past] =
    useState<string>("");

  // const [currentTab]
  const [tasks, setTasks] = useState<any[]>([]);
  const currentOrganization = useCurrentOrganization().data.data;
  const organizationName = currentOrganization.name;

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const getTask = async ({ statusNum = 0 }: { statusNum?: number }) => {
    let SafeOwnerEmail = ownerEmail;

    if (isAdmin) {
      if (admin_lookUp) {
        SafeOwnerEmail = admin_lookUp;
      } else {
        SafeOwnerEmail = "";
      }
    }
    if (!organizationName) return;

    const tabsStatusObj: any = {
      0: "pending",
      1: "active",
      2: "over_due",
      3: "awaiting_rating",
      4: "rework",
      5: "rework_over_due",
      6: "closed",
    };

    setIsLoading(true);
    if (tabsStatusObj[statusNum] == "closed") {
      if (!(start_date_before__Past && start_date_after__Past)) {
        return;
      }
    }

    if (tabsStatusObj[statusNum] == "pending") {
      if (!(start_date_before__Future && start_date_after__Future)) {
        return;
      }
    }

    setIsLoading(false);
  };

  useLayoutEffect(() => {
    setTimeFilter({
      setStart_date_after: setStart_date_after__Future,
      setStart_date_before: setStart_date_before__Future,
      timeType: "future",
    });

    setTimeFilter({
      setStart_date_after: setStart_date_after__Past,
      setStart_date_before: setStart_date_before__Past,
      timeType: "past",
    });
  }, []);

  // we get the task here depening on the tab we filter
  useEffect(() => {
    // const ownerEmail = getLoggedInUserEmail()

    if (organizationName && ownerEmail) {
      // console.log(start_date_before,start_date_after,'from TaskList Table')
      // dispatch(getTasks({ organizationName,ownerEmail:ownerEmail?ownerEmail:getLoggedInUserEmail(),showBoundary,
      //   "start_date_before":start_date_before,"start_date_after":start_date_after,
      //   "pageber":page,
      // }))

      getTask({ statusNum: TabStatusIndex });
    }
    if (organizationName && admin_lookUp) {
      // console.log(start_date_before,start_date_after,'from TaskList Table')
      // dispatch(getTasks({ organizationName,ownerEmail:ownerEmail?ownerEmail:getLoggedInUserEmail(),showBoundary,
      //   "start_date_before":start_date_before,"start_date_after":start_date_after,
      //   "pageber":page,
      // }))

      getTask({ statusNum: TabStatusIndex });
    } else {
      setLoadingTask(false);
    }
  }, [
    ownerEmail,
    start_date_before__Future,
    start_date_after__Future,
    start_date_before__Past,
    start_date_after__Past,
    page,
    TabStatusIndex,
    admin_lookUp,
  ]);

  useEffect(() => {
    const taskTabIndex = localStorage.getItem("taskTabIndex");
    if (taskTabIndex) {
      setStatusTabIndex(JSON.parse(taskTabIndex));
    }
  }, []);

  return (
    <>
      <br />
      {isInTaskPage ? (
        <Grid gap="2" mb="6" templateColumns="repeat(4, 1fr)">
          <CardList
            allow_percent={false}
            cardDetails={[
              {
                title: "My Pending Task",
                value: 0,
                rate: 0,
                allow_percent: false,
              },
              {
                title: "My Active Task",
                value: 0,
                rate: 0,
                allow_percent: false,
              },
              {
                title: "My Overdue Task",
                value: 0,
                rate: 0,
                allow_percent: false,
              },
              {
                title: "My Awaiting Rating Task",
                value: 0,
                rate: 0,
                allow_percent: false,
              },

              {
                title: "My Completed Task",
                value: 0,
                rate: 0,
                allow_percent: false,
              },
              // {
              //   title: 'My Work In Progress Task',
              //   value: 10,
              //   rate: 4,
              // },
            ]}
          />
        </Grid>
      ) : (
        ""
      )}

      {isLoading && <Preloader />}
      <Tabs
        isLazy
        onChange={(currentTimeFilterIndex) => {
          setPage(1); //everu time a tab is chage get page one of the new tab

          setTimeFilter({
            currentTab: currentTimeFilterIndex,
            setStart_date_after: setStart_date_after__Future,
            setStart_date_before: setStart_date_before__Future,
            timeType: "future",
          });

          setTimeFilter({
            currentTab: currentTimeFilterIndex,
            setStart_date_after: setStart_date_after__Past,
            setStart_date_before: setStart_date_before__Past,
            timeType: "past",
          });
        }}
      >
        <TabList>
          {["Day", "Week", "Month", "Quarter", "Bi-Annual", "Annual"].map(
            (word, index) => (
              <Tab isDisabled={isLoading} key={index}>
                {word}
              </Tab>
            )
          )}
          {/* <Tab>Week</Tab>
      <Tab>Month</Tab>
      <Tab>Quarter</Tab>
      <Tab>Bi-Annual</Tab>
      <Tab>Annual</Tab> */}
        </TabList>

        <TabPanels>
          {[...new Array(6)].map(() => (
            <TabPanel>
              <Tabs
                isLazy
                //  defaultIndex={1}
                index={TabStatusIndex}
                colorScheme={"blue"}
                isFitted
                variant="enclosed"
                onChange={(currenttabIndex) => {
                  localStorage.setItem(
                    "taskTabIndex",
                    JSON.stringify(currenttabIndex)
                  );
                  setPage(1); //everu time a tab is chage get page one of the new tab
                  setStatusTabIndex(currenttabIndex);
                }}
              >
                <TabList mb="1em">
                  {/*   */}
                  {[
                    "Pending",
                    "Active",
                    "Over Due",
                    "Awaiting Rating",
                    "Rework",
                    "Rework Over Due",
                    "Closed",
                  ].map((taskStatus, index) => (
                    <Tab
                      isDisabled={isLoading}
                      // disabled={isLoading}
                      style={{ color: isLoading ? "gray" : "" }}
                      key={index}
                    >
                      {taskStatus}
                    </Tab>
                  ))}
                  {/* <Tab >Pending</Tab>tabIndex = 0 */}
                  {/* <Tab>Active</Tab>tabIndex = 1
    <Tab>Over Due</Tab>2
    <Tab>Awaiting Rating</Tab>tabIndex = 3
    <Tab>Rework</Tab>tabIndex = 4
    <Tab>Rework Over Due</Tab>tabIndex = 5
    <Tab>Closed</Tab>tabIndex = 6 */}
                  {/* tabIndex = 7 */}
                </TabList>
                <TabPanels>
                  {[...new Array(7)].map(() => (
                    <TabPanel>
                      {/* navigate up to see the table */}
                      {/* 
            pending -0 represtents Pending
            */}

                      <TaskTable
                        tasks={tasks}
                        LoadingTask={LoadingTask}
                        currentTabNumber={TabStatusIndex}
                        page_count={pageCount}
                        page={page}
                        setPage={setPage}
                        nextPage={nextPage}
                        previousPage={previousPage}
                      />
                    </TabPanel>
                  ))}
                </TabPanels>
              </Tabs>
            </TabPanel>
          ))}
        </TabPanels>
      </Tabs>
    </>
  );
};

export default TaskListTable;
