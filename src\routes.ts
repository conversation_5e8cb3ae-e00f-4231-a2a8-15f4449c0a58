import { lazy } from "react";

export const organizationRoutes = [
  {
    path: "",
    component: lazy(() => import("./pages/index")),
  },
  {
    path: "structure",
    component: lazy(() => import("./pages/organization/organizationStructure")),
  },
  {
    path: "settings",
    component: lazy(() => import("./pages/organization/settings")),
  },
  {
    path: "structure/setup",
    component: lazy(() => import("./pages/organization/SetupOrganizationStructure")),
  },
  {
    path: "setup",
    component: lazy(() => import("./pages/organization/setupOrganization")),
  },
  {
    path: "structure/:structureName",
    component: lazy(() => import("./pages/organizationStructure/structureNamePage")),
  },
  {
    path: "people",
    component: lazy(() => import("./pages/organizationStructure/employees")),
  },
];

export const tenantRoutes = [
  {
    path: "",
    component: lazy(() => import("./pages/dashboard")),
  },
  {
    path: "strategy-deck",
    component: lazy(() => import("./pages/strategyDeck")),
  },
  {
    path: "reports",
    component: lazy(() => import("./pages/reports")),
  },
  {
    path: "initiative-report/:initiativeID",
    component: lazy(() => import("./pages/Reports/InitiativeReport")),
  },
  {
    path: "tasks",
    component: lazy(() => import("./pages/tasks")),
  },
  {
    path: "task-calendar",
    component: lazy(() => import("./pages/TaskCalendar")),
  },
  {
    path: "human-performance-management",
    component: lazy(() => import("./pages/Reports/HumanPerformance")),
  },
  {
    path: "team-lead-kpi",
    component: lazy(() => import("./pages/TeamLeadCreateInitiative")),
  },
  {
    path: "corporate-report",
    component: lazy(() => import("./pages/Reports/CorporatePerformance")),
  },
  {
    path: "leave-management",
    component: lazy(() => import("./pages/LeaveManagements/LeaveManagementLayout")),
    children: [
      {
        index: true, // Add index route
        component: lazy(() => import("./pages/LeaveManagements/RequestLeave")),
        label: "Request Leave",
      },
      {
        path: "request-leave",
        component: lazy(() => import("./pages/LeaveManagements/RequestLeave")),
        label: "Request Leave",
      },
      {
        path: "pending-leave",
        component: lazy(() => import("./pages/LeaveManagements/PendingLeave")),
        label: "Pending Leave",
      },
      {
        path: "trigger-leave-payment",
        component: lazy(() => import("./pages/LeaveManagements/TriggerLeavePayment")),
        label: "Trigger Leave Payment",
      },
      {
        path: "leave-history",
        component: lazy(() => import("./pages/LeaveManagements/LeaveHistory")),
        label: "Leave History",
      },
    ],
  },
  {
    path: "submit-task/:isTaskOwner/:taskId/:reworkLimit/:taskType/:taskName",
    component: lazy(() => import("./pages/submitTask")),
  },
  {
    path: "rate-task",
    component: lazy(() => import("./pages/rateTask")),
  },
  {
    path: "cpm",
    component: lazy(() => import("./pages/cpm")),
  },
  {
    path: "messages",
    component: lazy(() => import("./pages/messages")),
  },
  {
    path: "Settings",
    component: lazy(() => import("./pages/Settings")),
  },
  {
    path: "profile",
    component: lazy(() => import("./pages/profile")),
  },
];

export const tenantManagement = [
  {
    path: "home",
    component: lazy(() => import("./pages/SuperadminManagment/TenantManage")),
  },
];