import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect, useMemo, useState } from "react";
import { InputField, ReuseableForm } from "../components/custom/form";
import {
  useCurrentOrganization,
  useOrganizationStructure,
} from "../hooks/organization";
import { useUpdatePeople } from "../hooks/usePeople";
import { PeopleAttributes } from "../types/people";
import { PeopleSchema } from "./AddEmployee";
import { UserRole } from "../types/user";
import { uploadFile } from "../api/storage";
import { useGetDesignations } from "../hooks/useDesignation";
import { useGetCareerPaths } from "../hooks/useCareerPaths";
import { educationQualifications } from "../utils/educationalQualifications";

const UpdateEmployee = ({ employee }: { employee: <PERSON>A<PERSON><PERSON><PERSON><PERSON> }) => {
  const { onClose } = useDisclosure();
  let { data: currentOrganization } = useCurrentOrganization();
  currentOrganization = currentOrganization?.data;

  const [isLoading, setIsLoading] = useState(false);
  const updateMutation = useUpdatePeople(employee.id, onClose);

  const structureLvldata = useOrganizationStructure();
  const structureLvls = useMemo(() => {
    const levels = structureLvldata;

    const structureLevelNames = levels?.map((item: any) => item.name);
    const structureLevelTiersMap = levels?.reduce((acc: any, item: any) => {
      acc[item?.name] = item?.tiers;
      return acc;
    }, {} as Record<string, string[]>);

    return { levels: structureLevelNames, tiers: structureLevelTiersMap };
  }, [structureLvldata]);

  const { data: designations } = useGetDesignations();
  const designationList = designations?.data?.data?.map(
    (item: any) => item.name
  );

  const { data: careerPath } = useGetCareerPaths();
  const careerPathList = careerPath?.data?.data?.map((item: any) => item.name);

  const initialValues = {
    // Personal Info
    firstName: employee.firstName || "",
    lastName: employee.lastName || "",
    otherNames: employee.otherNames || "",
    officialPhoneNumber: employee.officialPhoneNumber || "",
    personalPhoneNumber: employee.personalPhoneNumber || "",
    officialEmail: employee.officialEmail || "",
    personalEmail: employee.personalEmail || "",
    address: employee.address || "",
    dateOfBirth: employee.dateOfBirth || "",
    gender: employee.gender || "",

    // Education
    educationDetailsInstitutions: employee.educationDetailsInstitutions || "",
    educationDetailsYears: employee.educationDetailsYears || "",
    educationDetailsQualifications:
      employee.educationDetailsQualifications || "",

    // Guarantor 1
    guarantor1IdCard: employee?.guarantor1IdCard || "",
    guarantor1Passport: employee?.guarantor1Passport || "",
    guarantor1FirstName: employee.guarantor1FirstName || "",
    guarantor1LastName: employee.guarantor1LastName || "",
    guarantor1Address: employee.guarantor1Address || "",
    guarantor1Occupation: employee.guarantor1Occupation || "",
    guarantor1Age: employee.guarantor1Age || "",

    // Guarantor 2
    guarantor2IdCard: employee?.guarantor2IdCard || "",
    guarantor2Passport: employee?.guarantor2Passport || "",
    guarantor2FirstName: employee.guarantor2FirstName || "",
    guarantor2LastName: employee.guarantor2LastName || "",
    guarantor2Address: employee.guarantor2Address || "",
    guarantor2Occupation: employee.guarantor2Occupation || "",
    guarantor2Age: employee.guarantor2Age || "",

    // Organization & Role
    structuralLevel: employee.structuralLevel || "",
    role: employee.role || "",
    dateEmployed: employee.dateEmployed || "",
    careerLevel: employee.careerLevel || "",
    designationName: employee.designationName || "",

    // Misc
    description: employee.description || "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: PeopleSchema,
    onSubmit: async (values, { resetForm }) => {
      setIsLoading(true);
      const {
        guarantor1IdCard,
        guarantor1Passport,
        guarantor2IdCard,
        guarantor2Passport,
      } = values;

      const documentFiles = [
        guarantor1IdCard,
        guarantor1Passport,
        guarantor2IdCard,
        guarantor2Passport,
      ];

      // Upload all documents concurrently
      const uploadedFiles = await Promise.all(
        documentFiles.map(async (file) => {
          if (!file) return null;

          // If it's a File object, upload it
          if ((file as any) instanceof File) {
            const formData = new FormData();
            formData.append("file", file);
            const res = await uploadFile(formData);
            return res?.data ?? null;
          }

          // If it's a string (likely an existing URL), return it
          if (typeof file === "string") {
            return file;
          }

          // In all other cases, return null
          return null;
        })
      );

      // Destructure the uploaded URLs back into the values object
      [
        values.guarantor1IdCard,
        values.guarantor1Passport,
        values.guarantor2IdCard,
        values.guarantor2Passport,
      ] = uploadedFiles;

      console.log({ uploadedFiles, values });

      setIsLoading(false);
      updateMutation.mutate({ values, resetForm });
    },
  });

  const userRoles: string[] = Object.values(UserRole);

  const fields: InputField[] = [
    {
      type: "grid",
      name: "name",
      gridCol: 2,
      gridInputs: [
        {
          name: "firstName",
          type: "text",
          label: "First Name",
          placeholder: "Enter first name",
          validate: true,
        },
        {
          name: "lastName",
          type: "text",
          label: "Last Name",
          placeholder: "Enter last name",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "gender",
      gridCol: 2,
      gridInputs: [
        {
          name: "otherNames",
          type: "text",
          label: "Other Names",
          placeholder: "Enter other names",
          validate: true,
        },
        {
          name: "gender",
          type: "select",
          label: "Gender",
          options: ["Male", "Female", "Others"],
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "contact",
      gridCol: 2,
      gridInputs: [
        {
          name: "officialPhoneNumber",
          type: "text",
          label: "Phone Number",
          placeholder: "Enter phone",
          validate: true,
        },
        {
          name: "personalPhoneNumber",
          type: "text",
          label: "Personal Phone",
          placeholder: "Enter personal phone",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "emails",
      gridCol: 2,
      gridInputs: [
        {
          name: "personalEmail",
          type: "email",
          label: "Personal Email",
          placeholder: "Enter personal email",
          validate: true,
        },
        {
          name: "officialEmail",
          type: "email",
          label: "Official Email",
          placeholder: "Enter official email",
          validate: true,
        },
      ],
    },
    {
      type: "select",
      label: "Structure Level",
      name: "structuralLevel",
      placeholder: "Select structure level",
      options: structureLvls?.levels?.length ? structureLvls?.levels : [],
      validate: true,
    },
    {
      name: "address",
      type: "text",
      label: "Address",
      placeholder: "Enter address",
      validate: true,
    },
    {
      type: "grid",
      name: "dates",
      gridCol: 2,
      gridInputs: [
        {
          name: "dateOfBirth",
          type: "date",
          label: "Date of Birth",
          placeholder: "Select birth date",
          validate: true,
        },
        {
          name: "dateEmployed",
          type: "date",
          label: "Date Employed",
          placeholder: "Select employment date",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "employment",
      gridCol: 2,
      gridInputs: [
        {
          name: "role",
          type: "select",
          label: "Role",
          options: userRoles,
          validate: true,
        },
        {
          name: "designationName",
          type: "select",
          label: "Designation",
          placeholder: "Enter designation",
          options: designationList,
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "organization",
      gridCol: 2,
      gridInputs: [
        // {
        //   name: "corporateName",
        //   type: "text",
        //   label: "Corporate",
        //   placeholder: "Enter corporate",
        //   validate: true,
        // },
        // {
        //   name: "divisionName",
        //   type: "text",
        //   label: "Division",
        //   placeholder: "Enter division",
        //   validate: true,
        // },
        // {
        //   name: "groupName",
        //   type: "text",
        //   label: "Group",
        //   placeholder: "Enter group",
        //   validate: true,
        // },
        // {
        //   name: "unitName",
        //   type: "text",
        //   label: "Unit",
        //   placeholder: "Enter unit",
        //   validate: true,
        // },
        {
          name: "careerLevel",
          type: "select",
          label: "Career Path",
          placeholder: "Enter career path",
          options: careerPathList,
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "education",
      gridCol: 2,
      gridInputs: [
        {
          name: "educationDetailsInstitutions",
          type: "text",
          label: "Institutions",
          placeholder: "Enter institutions (comma separated)",
          validate: true,
        },
        {
          name: "educationDetailsYears",
          type: "text",
          label: "Years",
          placeholder: "Enter years (comma separated)",
          validate: true,
        },
        {
          name: "educationDetailsQualifications",
          type: "select",
          label: "Qualification",
          placeholder: "Enter qualification",
          options: educationQualifications,
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "guarantor1",
      gridCol: 2,
      // header: "Guarantor 1 Details",
      gridInputs: [
        {
          name: "guarantor1IdCard",
          type: "upload",
          label: "Guarantor 1 ID Card",
          placeholder: "Upload Id Card for Guarantor 1",
        },
        {
          name: "guarantor1Passport",
          type: "upload",
          label: "Guarantor 1 Passport",
          placeholder: "Upload Passport for Guarantor 1",
        },
        {
          name: "guarantor1FirstName",
          type: "text",
          label: "Guarantor 1 First Name",
          placeholder: "Enter first name",
          validate: true,
        },
        {
          name: "guarantor1LastName",
          type: "text",
          label: "Guarantor 1 Last Name",
          placeholder: "Enter last name",
          validate: true,
        },
        {
          name: "guarantor1Occupation",
          type: "text",
          label: "Guarantor 1 Occupation",
          placeholder: "Enter occupation",
          validate: true,
        },
        {
          name: "guarantor1Age",
          type: "text",
          label: "Guarantor 1 Age",
          placeholder: "Enter age",
          validate: true,
        },
        {
          name: "guarantor1Address",
          type: "text",
          label: "Guarantor 1 Address",
          placeholder: "Enter address",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "guarantor2",
      gridCol: 2,
      // header: "Guarantor 2 Details",
      gridInputs: [
        {
          name: "guarantor2IdCard",
          type: "upload",
          label: "Guarantor 2 ID Card",
          placeholder: "Upload Id Card for Guarantor 2",
        },
        {
          name: "guarantor2Passport",
          type: "upload",
          label: "Guarantor 2 Passport",
          placeholder: "Upload Passport for Guarantor 2",
        },
        {
          name: "guarantor2FirstName",
          type: "text",
          label: "Guarantor 2 First Name",
          placeholder: "Enter first name",
          validate: true,
        },
        {
          name: "guarantor2LastName",
          type: "text",
          label: "Guarantor 2 Last Name",
          placeholder: "Enter last name",
          validate: true,
        },
        {
          name: "guarantor2Occupation",
          type: "text",
          label: "Guarantor 2 Occupation",
          placeholder: "Enter occupation",
          validate: true,
        },
        {
          name: "guarantor2Age",
          type: "text",
          label: "Guarantor 2 Age",
          placeholder: "Enter age",
          validate: true,
        },
        {
          name: "guarantor2Address",
          type: "text",
          label: "Guarantor 2 Address",
          placeholder: "Enter address",
          validate: true,
        },
      ],
    },
    {
      name: "description",
      type: "textarea",
      label: "Description",
      placeholder: "Enter description",
      validate: true,
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Employee
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          onClick={formik.submitForm}
          variant="primary"
          w="full"
          fontSize="sm"
          isLoading={updateMutation.isPending || isLoading}
          loadingText="Updating employee..."
        >
          Update Employee
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UpdateEmployee;
