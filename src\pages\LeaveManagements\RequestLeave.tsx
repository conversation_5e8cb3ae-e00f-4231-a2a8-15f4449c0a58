import React, { useState } from "react";
import AppBar from "../../components/AppBar";

import { Box, Container, Flex, Heading, Text } from "@chakra-ui/layout";
import { Button } from "@chakra-ui/button";
import LeaveRequestTable from "../../components/LeaveRequestTable/LeaveRequestTable";
import LeaveRequestForm from "../../components/LeaveRequestTable/LeaveRequestForm";

const enum LeaveRequest {
  PROFILE = "profile",
  REQUEST = "request",
}

const RequestLeave = () => {
  // Sample data - replace with actual data from your API/state
  const [tab, setTab] = useState<LeaveRequest>(LeaveRequest.PROFILE);
  const leaveRequestData = [
    {
      id: 1,
      employee: "<PERSON><PERSON><PERSON> Ayandele",
      role: "UI/UX Designer",
      leaveType: "Paid Leave",
      requestDate: "Apr 1 - Apr 5, 2025",
      status: "Pending",
    },
    {
      id: 2,
      employee: "<PERSON><PERSON><PERSON>",
      role: "UI/UX Designer",
      leaveType: "Paid Leave",
      requestDate: "Apr 1 - Apr 5, 2025",
      status: "Pending",
    },
    {
      id: 3,
      employee: "<PERSON><PERSON><PERSON> Ayandele",
      role: "UI/UX Designer",
      leaveType: "Paid Leave",
      requestDate: "Apr 1 - Apr 5, 2025",
      status: "Pending",
    },
    {
      id: 4,
      employee: "Tomiwa Ayandele",
      role: "UI/UX Designer",
      leaveType: "Paid Leave",
      requestDate: "Apr 1 - Apr 5, 2025",
      status: "Pending",
    },
    {
      id: 5,
      employee: "Tomiwa Ayandele",
      role: "UI/UX Designer",
      leaveType: "Paid Leave",
      requestDate: "Apr 1 - Apr 5, 2025",
      status: "Pending",
    },
    {
      id: 6,
      employee: "Tomiwa Ayandele",
      role: "UI/UX Designer",
      leaveType: "Paid Leave",
      requestDate: "Apr 1 - Apr 5, 2025",
      status: "Pending",
    },
  ];

  // Handle row action (View button click)
  const handleRowAction = (item: any) => {
    console.log("View leave request:", item);
    // Add your navigation logic here
    // e.g., navigate to leave request details page
  };

  return (
    <>
      <AppBar heading="Leave Management " />
      <Container maxWidth={"7xl"}>
        <Box borderBottom={"1px solid #E2E8F0"} mb={4} pb={2}>
          <Heading
            as={"h2"}
            fontSize={"2rem"}
            fontWeight={700}
            color={"#0B3178"}
            fontFamily="Inter"
          >
            Leave Management
          </Heading>
        </Box>
        {tab === LeaveRequest.PROFILE && (
          <>
            <Flex justifyContent="space-between" alignItems="center" mb={4}>
              <Text fontWeight={550} fontSize="1.2rem" fontFamily="Inter">
                Your Leave Banks
              </Text>
              <Button
                backgroundColor="#0B3178"
                color="white"
                _hover={{ bg: "#0B3178" }}
                fontSize="14px"
                fontWeight={500}
                px={8}
                py={6}
                borderRadius={"10px"}
                fontFamily="Inter"
                onClick={() => setTab(LeaveRequest.REQUEST)}
              >
                Request For New Leave
              </Button>
            </Flex>

            {/* Leave Bank Statistics */}
            <Box
              width="531px"
              height="82px"
              border="1px solid #868686"
              borderRadius="8px"
              mb={16}
              boxShadow="0px 4px 4px rgba(0, 0, 0, 0.25)"
              p={4}
            >
              <Flex
                justifyContent="space-between"
                alignItems="center"
                px="2rem"
              >
                <Box textAlign="center">
                  <Flex flexDirection="column">
                    <Text
                      fontWeight={550}
                      fontSize="2xl"
                      fontFamily="Inter"
                      alignItems="start"
                    >
                      5/
                      <Text as="span" fontSize="sm" fontFamily="Inter">
                        10
                      </Text>
                    </Text>
                    <Text fontSize="xs" fontWeight={550} fontFamily="Inter">
                      Paid Leave Remaining
                    </Text>
                  </Flex>
                </Box>
                <Box textAlign="center">
                  <Flex flexDirection="column" alignItems="center">
                    <Text
                      fontWeight={550}
                      fontSize="2xl"
                      fontFamily="Inter"
                      alignItems="start"
                    >
                      3/
                      <Text as="span" fontSize="sm" fontFamily="Inter">
                        2
                      </Text>
                    </Text>
                    <Text fontSize="xs" fontWeight={550} fontFamily="Inter">
                      Unpaid Leave Remaining
                    </Text>
                  </Flex>
                </Box>
                <Box textAlign="center">
                  <Flex flexDirection="column" alignItems="center">
                    <Text
                      fontWeight={550}
                      fontSize="2xl"
                      fontFamily="Inter"
                      alignItems="start"
                    >
                      2/
                      <Text as="span" fontSize="sm" fontFamily="Inter">
                        5
                      </Text>
                    </Text>
                    <Text fontSize="xs" fontWeight={550} fontFamily="Inter">
                      Sick Leave
                    </Text>
                  </Flex>
                </Box>
              </Flex>
            </Box>

            {/* Leave Request Table */}
            <LeaveRequestTable
              data={leaveRequestData}
              title="Your Previous Leave Request"
              onRowAction={handleRowAction}
              actionLabel="View"
              showCheckboxes={true}
              showSearch={true}
              showFilter={true}
            />
          </>
        )}

        {tab === LeaveRequest.REQUEST && (
          <>
            <LeaveRequestForm />
          </>
        )}
      </Container>
    </>
  );
};

export default RequestLeave;
