import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect, useMemo, useState } from "react";
import * as yup from "yup";
import { InputField, ReuseableForm } from "../components/custom/form";
import {
  useCurrentOrganization,
  useOrganizationStructure,
} from "../hooks/organization";
import useCalculateEndDateOrOccurrence from "../hooks/useCalculateOccurrenceOrEndDate";
import { useUpdateObjective } from "../hooks/useObjectives";
import { useGetPerspectives } from "../hooks/usePerspectives";
import { TObjective } from "../types/objectives";
import { TPerspective } from "../types/perspectives";
import { formatDate } from "../utils/formatDate";
import { routineTypeArray } from "./AddKPI";
import { useGetStructuralLevels } from "../hooks/useStructure";
// import { useGetStructuralLevels } from "../hooks/useStructure";

export const spreadSchema = yup.object().shape({
  id: yup.string().required("ID is required"),
  name: yup.string().required("name is required"),
  relativePoint: yup
    .number()
    .typeError("Relative point must be a number")
    .required("Relative point is required")
    .positive("Relative point must be positive"),
});

const validationSchema = yup.object().shape({
  name: yup.string().required("Objective name is required"),
  // corporate: yup.string().required("Corporate level is required"),
  perspectives: yup
    .array()
    .of(spreadSchema)
    .min(1, "At least one perspective is required")
    .required("Perspectives is required"),
  routineType: yup.string().required("Routine option is required"),
  startDate: yup.date().required("Start date is required"),
  endDate: yup.date().optional(),
  afterOccurrence: yup.number().required("After occurrence is required"),
  corporate: yup.string().required("Structure Level is required").nullable(),
  tierName: yup
    .string()
    .when("corporate", (corporate, schema) =>
      corporate
        ? schema.required(
            "Tier is required, Add tiers for structure levels in the [admin panel](/organization/structure)"
          )
        : schema.optional()
    ),
});

export { validationSchema as ObjectiveSchema };

const UpdateObjective = ({ objective }: { objective: TObjective }) => {
  const { onClose } = useDisclosure();
  const [page, setPage] = useState(1);
  const [selectedStructureLevel, setSelectedStructureLevel] = useState("");
  const [currentTiers, setCurrentTiers] = useState([]);

  const structureLvldata = useOrganizationStructure();
  const structureLvls = useMemo(() => {
    const levels = structureLvldata;

    const structureLevelNames = levels?.map((item: any) => item.name);
    const structureLevelTiersMap = levels?.reduce((acc: any, item: any) => {
      acc[item?.name] = item?.tiers;
      return acc;
    }, {} as Record<string, string[]>);

    return { levels: structureLevelNames, tiers: structureLevelTiersMap };
  }, [structureLvldata]);

  let { data: currentOrganization } = useCurrentOrganization();
  currentOrganization = currentOrganization?.data;

  let { data: perspectives } = useGetPerspectives(page, 1000);
  perspectives = perspectives?.data;
  const perspectivesData = perspectives?.data;

  const updateMutation = useUpdateObjective(objective.id, onClose);

  const initialValues = {
    name: objective?.name || "",
    // corporate: objective?.corporate || "",
    routineType: objective?.routineType || "",
    perspectives: objective?.perspectives || [],
    startDate: formatDate(objective?.startDate?.toString(), "yyyy-MM-dd") || "",
    endDate:
      formatDate(objective?.endDate?.toString() || "", "yyyy-MM-dd") || "",
    afterOccurrence: objective?.afterOccurrence || 0,
    // structureLevel: objective?.structureLevel || "",
    // tierName: objective?.tierName || "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: (values, { resetForm }) =>
      updateMutation.mutate({ values, resetForm }),
  });

  useCalculateEndDateOrOccurrence(formik);

  useEffect(() => {
    if (selectedStructureLevel) {
      const tiers = structureLvls?.tiers[selectedStructureLevel] || [];
      setCurrentTiers(tiers); // Update the current tiers state

      // Manually set the Formik value for tiers, if available
      if (tiers.length > 0) {
        formik.setFieldValue("tierName", tiers[0]); // Set default tier value if any
      } else {
        formik.setFieldValue("tierName", ""); // Set empty value if no tiers
      }
    }
  }, [selectedStructureLevel, structureLvls?.tiers]);

  const fields: InputField[] = [
    {
      name: "name",
      type: "text",
      label: "Objective Name",
      placeholder: "Enter objective name",
      validate: true,
    },
    // {
    //   name: "corporate",
    //   type: "text",
    //   label: "Corporate Level",
    //   placeholder: "Enter corporate level",
    //   validate: true,
    // },
    {
      type: "select",
      label: "Structure Level",
      name: "corporate",
      placeholder: "Select the structure level for this Objective",
      options: structureLvls?.levels?.length ? structureLvls?.levels : [],
      onChangeCallback: (val) => {
        setSelectedStructureLevel(val.value);
      },
    },
    {
      type: "select",
      label: "Tier",
      name: "tierName",
      placeholder: "Select the tier for this structure level",
      options: currentTiers,
    },
    {
      name: "perspectives",
      type: "add-more",
      label: "Assign perspectives",
      placeholder: "Select perspectives",
      selected: objective.perspectives,
      addMoreOptions: perspectivesData?.map((perspective: TPerspective) => ({
        id: perspective.id,
        name: perspective.name,
      })),
    },
    {
      type: "grid",
      name: "objectiveDates",
      gridCol: 2,
      gridInputs: [
        {
          name: "routineType",
          type: "select",
          label: "Routine Option",
          options: routineTypeArray,
          placeholder: "select routine type",
          validate: true,
        },
        {
          name: "afterOccurrence",
          type: "number",
          label: "After Occurrence",
          placeholder: "Enter number of occurrences (optional)",
        },
      ],
    },

    {
      type: "grid",
      name: "objectiveDates",
      gridCol: 2,
      gridInputs: [
        {
          name: "startDate",
          type: "date",
          label: "Start Date",
          placeholder: "Select start date",
          validate: true,
        },
        {
          name: "endDate",
          type: "date",
          label: "End Date",
          placeholder: "Select end date (optional)",
        },
      ],
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Objective
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          onClick={formik.submitForm}
          variant="primary"
          fontWeight="500"
          w="full"
          fontSize="sm"
          isLoading={updateMutation.isPending}
          loadingText="Updating objective..."
        >
          Update Objective
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UpdateObjective;
