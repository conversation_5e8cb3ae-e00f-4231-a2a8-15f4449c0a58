import { RoutineType } from "../types/objectives";

type Params = {
  startDate?: string;
  routineType?: RoutineType | string;
  afterOccurrence?: string;
  endDate?: string;
};

const ERROR_INVALID_ROUTINE_TYPE = "Invalid routine type";
const ERROR_END_DATE_BEFORE_START = "End date must be after start date";
const ERROR_ONCE_CAN_ONLY_OCCUR_ONCE =
  "Routine type 'Once' can only occur once";

export function calculateEndDateOrOccurrence({
  startDate,
  routineType,
  afterOccurrence,
  endDate,
}: Params): {
  calculatedEndDate?: Date;
  calculatedAfterOccurrence?: string;
  errorMessage?: string;
} {
  if (!startDate || !routineType) return {};

  const start = new Date(startDate);
  const parsedOccurrence = Number(afterOccurrence);
  const hasValidOccurrence = afterOccurrence && parsedOccurrence > 0;
  const hasValidEndDate = !!endDate;

  if (hasValidOccurrence && !hasValidEndDate) {
    // Calculate endDate
    const result = new Date(start);
    switch (routineType) {
      case RoutineType.MONTHLY:
        result.setMonth(result.getMonth() + parsedOccurrence);
        break;
      case RoutineType.QUARTERLY:
        result.setMonth(result.getMonth() + parsedOccurrence * 3);
        break;
      case RoutineType.BI_ANNUALLY:
        result.setMonth(result.getMonth() + parsedOccurrence * 6);
        break;
      case RoutineType.ANNUALLY:
        result.setFullYear(result.getFullYear() + parsedOccurrence);
        break;
      case RoutineType.ONCE:
        if (parsedOccurrence > 1) {
          return {
            errorMessage: ERROR_ONCE_CAN_ONLY_OCCUR_ONCE,
          };
        }
        break;
      default:
        return {
          errorMessage: ERROR_INVALID_ROUTINE_TYPE,
        };
    }

    return { calculatedEndDate: result };
  }

  if (!afterOccurrence && endDate) {
    // Calculate afterOccurrence
    const end = new Date(endDate);
    if (end <= start) {
      return {
        errorMessage: ERROR_END_DATE_BEFORE_START,
      };
    }

    const diffInMonths =
      (end.getFullYear() - start.getFullYear()) * 12 +
      (end.getMonth() - start.getMonth());

    let occurrence = 0;
    switch (routineType) {
      case RoutineType.MONTHLY:
        occurrence = diffInMonths;
        break;
      case RoutineType.QUARTERLY:
        occurrence = Math.floor(diffInMonths / 3);
        break;
      case RoutineType.BI_ANNUALLY:
        occurrence = Math.floor(diffInMonths / 6);
        break;
      case RoutineType.ANNUALLY:
        occurrence = Math.floor(diffInMonths / 12);
        break;
      case RoutineType.ONCE:
        occurrence = 1;
        break;
      default:
        return { errorMessage: ERROR_INVALID_ROUTINE_TYPE };
    }

    return {
      calculatedAfterOccurrence: String(Math.max(1, occurrence)),
    };
  }

  return {};
}
