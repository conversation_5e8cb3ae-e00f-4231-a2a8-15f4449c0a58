import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect, useMemo, useState } from "react";
import { InputField, ReuseableForm } from "../components/custom/form";
import {
  useCurrentOrganization,
  useOrganizationStructure,
} from "../hooks/organization";
import useCalculateEndDateOrOccurrence from "../hooks/useCalculateOccurrenceOrEndDate";
import { useAddObjective } from "../hooks/useObjectives";
import { useGetPerspectives } from "../hooks/usePerspectives";
import { TPerspective } from "../types/perspectives";
import { routineTypeArray } from "./AddKPI";
import { ObjectiveSchema } from "./UpdateObjective";

const AddObjective: React.FC = () => {
  const { onClose } = useDisclosure();
  const [page, setPage] = useState(1);
  let { data: currentOrganization } = useCurrentOrganization();
  currentOrganization = currentOrganization?.data;

  const [selectedStructureLevel, setSelectedStructureLevel] = useState("");
  const [currentTiers, setCurrentTiers] = useState([]);

  const structureLvldata = useOrganizationStructure();
  const structureLvls = useMemo(() => {
    const levels = structureLvldata;

    const structureLevelNames = levels?.map((item: any) => item.name);
    const structureLevelTiersMap = levels?.reduce((acc: any, item: any) => {
      acc[item?.name] = item?.tiers;
      return acc;
    }, {} as Record<string, string[]>);

    return { levels: structureLevelNames, tiers: structureLevelTiersMap };
  }, [structureLvldata]);

  let { data: perspectives } = useGetPerspectives(page);
  perspectives = perspectives?.data;
  const perspectivesData = perspectives?.data;

  const addMutation = useAddObjective(onClose);

  const initialValues = {
    name: localStorage.getItem("name") || "",
    // corporate: localStorage.getItem("corporate") || "",
    routineType: localStorage.getItem("routineType") || "",
    perspectives: localStorage.getItem("perspectives") || null,
    startDate: localStorage.getItem("startDate") || "",
    endDate: localStorage.getItem("endDate") || null,
    afterOccurrence: localStorage.getItem("afterOccurrence") || "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: ObjectiveSchema,
    onSubmit: (values, { resetForm }) => {
      console.log({ values });

      return addMutation.mutate({ values, resetForm });
    },
  });

  useCalculateEndDateOrOccurrence(formik);

  useEffect(() => {
    if (selectedStructureLevel) {
      const tiers = structureLvls?.tiers[selectedStructureLevel] || [];
      setCurrentTiers(tiers); // Update the current tiers state

      // Manually set the Formik value for tiers, if available
      if (tiers.length > 0) {
        formik.setFieldValue("tierName", tiers[0]); // Set default tier value if any
      } else {
        formik.setFieldValue("tierName", ""); // Set empty value if no tiers
      }
    }
  }, [selectedStructureLevel, structureLvls?.tiers]);

  const fields: InputField[] = [
    {
      name: "name",
      type: "text",
      label: "Objective Name",
      placeholder: "Enter objective name",
      validate: true,
    },
    // {
    //   name: "corporate",
    //   type: "text",
    //   label: "Corporate Level",
    //   placeholder: "Enter corporate level",
    //   validate: true,
    // },
    {
      name: "perspectives",
      type: "add-more",
      label: "Assign perspectives",
      placeholder: "Select perspectives",
      addMoreOptions: perspectivesData?.map((perspective: TPerspective) => ({
        id: perspective.id,
        name: perspective.name,
      })),
      helpText: "Create a perspective in the perspective tab first",
    },

    {
      type: "select",
      label: "Structure Level",
      name: "corporate",
      placeholder: "Select the structure level for this KPI",
      options: structureLvls?.levels?.length ? structureLvls?.levels : [],
      onChangeCallback: (val) => {
        setSelectedStructureLevel(val.value);
      },
    },
    {
      type: "select",
      label: "Tier",
      name: "tierName",
      placeholder: "Select the tier for this structure level",
      options: currentTiers,
    },
    {
      type: "grid",
      name: "objectiveDates",
      gridCol: 2,
      gridInputs: [
        {
          name: "routineType",
          type: "select",
          label: "Routine Option",
          options: routineTypeArray,
          placeholder: "select routine type",
          validate: true,
        },
        {
          name: "afterOccurrence",
          type: "number",
          label: "After Occurrence",
          placeholder: "Enter number of occurrences (optional)",
        },
      ],
    },
    {
      type: "grid",
      name: "objectiveDates",
      gridCol: 2,
      gridInputs: [
        {
          name: "startDate",
          type: "date",
          label: "Start Date",
          placeholder: "Select start date",
          validate: true,
        },
        {
          name: "endDate",
          type: "date",
          label: "End Date",
          placeholder: "Select end date (optional)",
        },
      ],
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Set New Objective
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          onClick={formik.submitForm}
          variant="primary"
          w="full"
          fontSize="sm"
          isLoading={addMutation.isPending}
          loadingText="Adding objective..."
        >
          Create Objective
        </Button>
      </DrawerFooter>
    </>
  );
};

export default AddObjective;
