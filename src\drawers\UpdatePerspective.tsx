import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  useDisclosure,
} from "@chakra-ui/react";

import { useFormik } from "formik";
import * as yup from "yup";
import { ReuseableForm } from "../components/custom/form";
import { useUpdatePerspective } from "../hooks/usePerspectives";
import { TPerspective } from "../types/perspectives";

export interface AddPerspectiveInputs {
  name: string;
}

const validationSchema = yup.object().shape({
  name: yup.string().required("Perspective name is required"),
});

const UpdatePerspective = ({ perspective }: { perspective: TPerspective }) => {
  const { onClose } = useDisclosure();

  const updateMutation = useUpdatePerspective(
    perspective.id as number,
    onClose
  );

  const formik = useFormik({
    initialValues: { name: perspective.name },
    validationSchema,
    onSubmit: (values, { resetForm }) =>
      updateMutation.mutate({ name: values.name, reset<PERSON><PERSON> }),
  });

  const fields = [
    {
      name: "name",
      type: "text",
      label: "Perspective name",
      placeholder: "",
      validate: true,
    },
  ];

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Perspective
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          onClick={formik.submitForm}
          variant="primary"
          w="full"
          fontSize="sm"
          isLoading={updateMutation.isPending}
          loadingText="Updating perspective..."
        >
          Update Perspective
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UpdatePerspective;
