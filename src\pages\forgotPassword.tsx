import { Flex } from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect } from "react";
import { IoMdMail } from "react-icons/io";
import { useNavigate } from "react-router-dom";
import * as Yup from "yup";
import { forgotPassword } from "../api/authentication";
import ForgotPasswordIllustration from "../assets/images/forgotpassword.svg";
import AuthContainer from "../components/authentication/authContainer";
import AuthSideImage from "../components/AuthSideImage";
import { ReuseableForm } from "../components/custom/form";
import SEO from "../components/SEO";

const forgotPasswordSchema = Yup.object().shape({
  email: Yup.string()
    .email("Invalid Email Address")
    .required("Email is required"),
});

const ForgotPassword = () => {
  const initialValues = { email: "" };

  const navigate = useNavigate();

  const formik = useFormik({
    initialValues,
    validationSchema: forgotPasswordSchema,
    onSubmit: async ({ email }, { resetForm }) => {
      const success = await forgotPassword({ email, resetForm });

      if (success) {
        navigate("/");
      }
    },
  });

  const fields = [
    {
      name: "email",
      type: "email",
      label: "Organization Email",
      placeholder: "e.g. <EMAIL>",
      validate: true,
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <Flex minH="100vh" w="100vw" overflowY="hidden">
      <SEO title="Forgot Password" />
      <AuthSideImage SignUpIllustration={ForgotPasswordIllustration} />
      <AuthContainer
        title="Forgot Password?"
        subtitle="Enter your email and further instructions will be sent to reset your password"
        bottomText="Remember Password?"
        bottomLink={{ text: "Login", url: "/" }}
      >
        <ReuseableForm
          formik={formik}
          inputArray={fields}
          button={{
            text: "Send Mail",
            icon: <IoMdMail />,
            type: "submit",
            buttonLoadingText: "Sending...",
          }}
        />
      </AuthContainer>
    </Flex>
  );
};

export default ForgotPassword;
