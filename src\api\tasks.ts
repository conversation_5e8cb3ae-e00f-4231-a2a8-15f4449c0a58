import { TTasks } from "../types/tasks";
import { CrudService, Filters } from "./crud";

const taskCrud = new CrudService<TTasks>("tasks", {
  includeTenantAndUser: false,
});

export const getAllTasks = async (page?: number, limit?: number) => {
  return await taskCrud.getAll(page, limit);
};

export const getTasksWithFilters = async (
  filters: Filters,
  page?: number,
  limit?: number
) => {
  return await taskCrud.getByFilters(filters, page, limit);
};

export const getTaskById = async (id: number) => {
  return await taskCrud.getById(id);
};

export const createTask = async (
  task: Omit<TTasks, "id">,
  resetForm: () => void
) => {
  return await taskCrud.create(task, resetForm);
};

export const updateTask = async (
  id: number,
  task: Partial<Omit<TTasks, "id">>,
  resetForm?: () => void
) => {
  return await taskCrud.update(id, task, resetForm);
};

export const deleteTask = async (id: number) => {
  return await taskCrud.delete(id);
};

export const bulkCreateTasks = async (formData: FormData) => {
  return await taskCrud.bulkCreate(formData);
};
