  import { CloseIcon, HamburgerIcon } from "@chakra-ui/icons";
  import { Box, Flex, IconButton } from "@chakra-ui/react";
  import { AnimatePresence, motion } from "framer-motion";
  import { useEffect, useState } from "react";
  import { useMediaQuery } from "react-responsive";
  import Sidebar from "./Sidebar";

  const MotionFlex = motion.create(Flex);
  const MotionBox = motion.create(Box);
  const MotionIconButton = motion.create(IconButton);

  const SidebarToggleButton = ({
    isSidebarOpen,
    toggleSidebar,
  }: {
    isSidebarOpen: boolean;
    toggleSidebar: () => void;
  }) => (
    <MotionIconButton
      aria-label="Toggle Sidebar"
      icon={isSidebarOpen ? <CloseIcon /> : <HamburgerIcon />}
      display={{ base: "block", md: "none" }}
      onClick={toggleSidebar}
      position="fixed"
      width={30}
      height={30}
      fontSize="small"
      top="1rem"
      right="2rem"
      zIndex="50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    />
  );

  const SidebarContainer = ({
    isSidebarOpen,
    closeSidebar,
  }: {
    isSidebarOpen: boolean;
    closeSidebar: () => void;
  }) => {
    return (
      <AnimatePresence>
        {(isSidebarOpen || !useMediaQuery({ maxWidth: 768 })) && (
          <MotionFlex
            initial={{ x: "-100%" }}
            animate={{ x: 0 }}
            exit={{ x: "-100%" }}
            transition={{ duration: 0.3 }}
            w={{ "2xl": "300px", md: "100px", base: "85%" }}
            maxW={"350px"}
            h="100vh"
            position={{ base: "fixed", md: "relative" }}
            overflow="hidden"
            overflowX="hidden"
            direction="column"
            justifyContent="space-between"
            gap="2"
            top="0"
            left="0"
            px="4"
            py="6"
            bg="white"
            zIndex="40"
          >
            <Sidebar closeSidebar={closeSidebar} />
          </MotionFlex>
        )}
      </AnimatePresence>
    );
  };

  const SidebarOverlay = ({ closeSidebar }: { closeSidebar: () => void }) => (
    <AnimatePresence>
      <MotionBox
        key="overlay"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        position="fixed"
        top="0"
        left="0"
        width="100vw"
        height="100vh"
        bg="blackAlpha.600"
        zIndex="30"
        onClick={closeSidebar}
      />
    </AnimatePresence>
  );

  const Layout = ({ children }: { children: React.ReactNode }) => {
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const isDesktop = useMediaQuery({ maxWidth: 1536 });

    const isTabletView = useMediaQuery({ minWidth: 768 });

    const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);
    const closeSidebar = () => setIsSidebarOpen(false);

    useEffect(() => {
      document.body.style.overflow =
        isSidebarOpen && !isDesktop ? "hidden" : "auto";
      return () => {
        document.body.style.overflow = "auto";
      };
    }, [isSidebarOpen, isDesktop]);

    useEffect(() => {
      if (isTabletView) {
        closeSidebar();
      }
    }, [isTabletView]);

    return (
      <Flex h="100vh" overflow="hidden">
        <SidebarToggleButton
          isSidebarOpen={isSidebarOpen}
          toggleSidebar={toggleSidebar}
        />
        <SidebarContainer
          isSidebarOpen={isSidebarOpen}
          closeSidebar={closeSidebar}
        />
        {isSidebarOpen && !isTabletView && (
          <SidebarOverlay closeSidebar={closeSidebar} />
        )}
        <MotionBox
          px={5}
          flex="1"
          w="100%"
          minH="100vh"
          overflowY="auto"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {children}
        </MotionBox>
      </Flex>
    );
  };

  export default Layout;
