import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useToast } from "@chakra-ui/react";
import { ErrorMessage, Field, Form, Formik } from "formik";
import { useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import * as yup from "yup";
import axiosInstance from "../api/index";
import Preloader from "../components/Preloader";
import errorMessageGetter from "../utils/errorMessages";

// Define the CreateHrAdminForm type
interface CreateHrAdminForm {
  first_name: string;
  last_name: string;
  phone_number: string;
  email: string;
  password: string;
}

const schema = yup.object().shape({
  first_name: yup.string().required("First name is required"),
  last_name: yup.string().required("Last name is required"),
  phone_number: yup
    .string()
    .matches(/^\d+$/, "Phone number must be numeric")
    .required("Phone number is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  password: yup.string().min(6, "Password must be at least 6 characters"),
});

export const UpdateHrAdminDrawer = (
  props: CreateHrAdminForm
): React.ReactElement => {
  const [isLoading, setIsLoading] = useState(false);
  const { showBoundary } = useErrorBoundary();
  const toast = useToast();

  const initialValues: CreateHrAdminForm = {
    first_name: props.first_name || "",
    last_name: props.last_name || "",
    phone_number: props.phone_number || "",
    email: props.email || "",
    password: "",
  };

  const onSubmit = async (values: CreateHrAdminForm) => {
    console.log({ "submitted data": values });
    const org_name = localStorage.getItem("current_organization_short_name");
    if (!org_name) return;
    setIsLoading(true);
    try {
      const resp = await axiosInstance.put(
        `/auth/register/admin/?organisation_short_name=${org_name}`,
        values
      );
      console.log({ updated: resp });
      toast({
        title: "Updated Successfully",
        status: "success",
        position: "top",
        duration: 3000,
        isClosable: true,
      });
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (err: any) {
      console.log(err.response);
      if (err.response.status === 403) {
        toast({
          title: "You don't have permission to this function",
          status: "error",
          position: "top",
          duration: 3000,
          isClosable: true,
        });
      }
      if (err.response.status === 400) {
        toast({
          title: errorMessageGetter(err.response.data),
          status: "error",
          position: "top",
          duration: 3000,
          isClosable: true,
        });
      }
      if (err.response.status === 401) {
        showBoundary(err);
      }
    }
    setIsLoading(false);
  };

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Hr Admin
      </DrawerHeader>
      <DrawerBody>
        {isLoading && <Preloader />}
        <Formik
          initialValues={initialValues}
          validationSchema={schema}
          onSubmit={onSubmit}
        >
          {({ isSubmitting }) => (
            <Form style={{ width: "100%", padding: "1rem" }}>
              <Grid gridTemplateColumns="1f" columnGap={4} rowGap={8} mb="10">
                <Box>
                  <label htmlFor="first_name">First Name</label>
                  <Field
                    id="first_name"
                    name="first_name"
                    placeholder="Nwokolo"
                    className="chakra-input"
                  />
                  <ErrorMessage
                    name="first_name"
                    component="div"
                    className="error-message"
                  />
                </Box>

                <Box>
                  <label htmlFor="last_name">Last Name</label>
                  <Field
                    id="last_name"
                    name="last_name"
                    placeholder="Matthew"
                    className="chakra-input"
                  />
                  <ErrorMessage
                    name="last_name"
                    component="div"
                    className="error-message"
                  />
                </Box>

                <Box>
                  <label htmlFor="phone_number">Phone Number</label>
                  <Field
                    id="phone_number"
                    name="phone_number"
                    placeholder="Please Input correct Phone Number"
                    className="chakra-input"
                  />
                  <ErrorMessage
                    name="phone_number"
                    component="div"
                    className="error-message"
                  />
                </Box>

                <Box>
                  <label htmlFor="email">Email</label>
                  <Field
                    id="email"
                    name="email"
                    placeholder="Please Input correct Email"
                    className="chakra-input"
                    disabled
                  />
                  <ErrorMessage
                    name="email"
                    component="div"
                    className="error-message"
                  />
                </Box>

                <Box>
                  <label htmlFor="password">Password</label>
                  <Field
                    id="password"
                    name="password"
                    placeholder="Please Input correct Password"
                    className="chakra-input"
                    type="password"
                  />
                  <ErrorMessage
                    name="password"
                    component="div"
                    className="error-message"
                  />
                </Box>
              </Grid>

              <Button
                fontWeight="500"
                variant="primary"
                type="submit"
                isLoading={isSubmitting || isLoading}
                loadingText="Updating..."
              >
                Update Hr Admin
              </Button>
            </Form>
          )}
        </Formik>
      </DrawerBody>
      <DrawerFooter></DrawerFooter>
    </>
  );
};
