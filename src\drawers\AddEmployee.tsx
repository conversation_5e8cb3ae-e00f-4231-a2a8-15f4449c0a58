import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect, useMemo, useState } from "react";
import * as yup from "yup";
import { InputField, ReuseableForm } from "../components/custom/form";
import {
  useCurrentOrganization,
  useOrganizationStructure,
} from "../hooks/organization";
import { useAddPeople } from "../hooks/usePeople";
import { useGetStructuralLevels } from "../hooks/useStructure";
import { UserRole } from "../types/user";
import { uploadFile } from "../api/storage";
import { useGetDesignations } from "../hooks/useDesignation";
import { useGetCareerPaths } from "../hooks/useCareerPaths";
import { educationQualifications } from "../utils/educationalQualifications";

const PeopleSchema = yup.object().shape({
  // Personal Info
  firstName: yup.string().required("First name is required"),
  lastName: yup.string().required("Last name is required"),
  otherNames: yup.string().optional(),
  officialPhoneNumber: yup.string().optional(),
  personalPhoneNumber: yup
    .string()
    .required("Personal phone number is required"),
  officialEmail: yup.string().email("Invalid official email").optional(),
  personalEmail: yup
    .string()
    .email("Invalid personal email")
    .required("Personal email is required"),
  address: yup.string().required("Address is required"),
  dateOfBirth: yup.string().required("Date of birth is required"),
  gender: yup.string().required("Gender is required"),

  // Education
  educationDetailsInstitutions: yup
    .string()
    .required("Education institutions are required"),
  educationDetailsYears: yup.string().required("Education years are required"),
  educationDetailsQualifications: yup
    .string()
    .required("Education qualifications are required"),

  // Guarantor 1
  guarantor1IdCard: yup.string().nullable().optional(),
  guarantor1Passport: yup.string().nullable().optional(),
  guarantor1FirstName: yup
    .string()
    .required("Guarantor first name is required"),
  guarantor1LastName: yup.string().required("Guarantor last name is required"),
  guarantor1Address: yup.string().required("Guarantor address is required"),
  guarantor1Occupation: yup
    .string()
    .required("Guarantor occupation is required"),
  guarantor1Age: yup.string().required("Guarantor age is required"),

  // Guarantor 2
  guarantor2IdCard: yup.string().nullable().optional(),
  guarantor2Passport: yup.string().nullable().optional(),
  guarantor2FirstName: yup
    .string()
    .required("Guarantor2 first name is required"),
  guarantor2LastName: yup.string().required("Guarantor2 last name is required"),
  guarantor2Address: yup.string().required("Guarantor2 address is required"),
  guarantor2Occupation: yup
    .string()
    .required("Guarantor2 occupation is required"),
  guarantor2Age: yup.string().required("Guarantor2 age is required"),

  // Organization & Role
  structuralLevel: yup.string().required("Structural level is required"),
  role: yup
    .string()
    // .oneOf(Object.values(UserRole), 'Invalid role')
    .required("Role is required"),
  dateEmployed: yup.string().required("Date employed is required"),
  careerLevel: yup.string().required("Career level is required"),
  designationName: yup.string().required("Designation is required"),

  // Misc
  description: yup.string().optional(),
});

export { PeopleSchema };
const AddEmployee = () => {
  const { onClose } = useDisclosure();
  const addMutation = useAddPeople(onClose);
  const [isLoading, setIsLoading] = useState(false);
  let { data: currentOrganization } = useCurrentOrganization();
  currentOrganization = currentOrganization?.data;

  const structureLvldata = useOrganizationStructure();
  const structureLvls = useMemo(() => {
    const levels = structureLvldata;

    const structureLevelNames = levels?.map((item: any) => item.name);
    const structureLevelTiersMap = levels?.reduce((acc: any, item: any) => {
      acc[item?.name] = item?.tiers;
      return acc;
    }, {} as Record<string, string[]>);

    return { levels: structureLevelNames, tiers: structureLevelTiersMap };
  }, [structureLvldata]);

  const { data: designations } = useGetDesignations();
  const designationList = designations?.data?.data?.map(
    (item: any) => item.name
  );

  const { data: careerPath } = useGetCareerPaths();
  const careerPathList = careerPath?.data?.data?.map((item: any) => item.name);

  const initialValues = {
    // Personal Info
    firstName: "",
    lastName: "",
    otherNames: "",
    officialPhoneNumber: "",
    personalPhoneNumber: "",
    officialEmail: "",
    personalEmail: "",
    address: "",
    dateOfBirth: "",
    gender: "",

    // Education
    educationDetailsInstitutions: "",
    educationDetailsYears: "",
    educationDetailsQualifications: "",

    // Guarantor 1
    guarantor1IdCard: "",
    guarantor1Passport: "",
    guarantor1FirstName: "",
    guarantor1LastName: "",
    guarantor1Address: "",
    guarantor1Occupation: "",
    guarantor1Age: "",

    // Guarantor 2
    guarantor2IdCard: "",
    guarantor2Passport: "",
    guarantor2FirstName: "",
    guarantor2LastName: "",
    guarantor2Address: "",
    guarantor2Occupation: "",
    guarantor2Age: "",

    // Organization & Role
    structuralLevel: "",
    role: "",
    dateEmployed: "",
    careerLevel: "",
    designationName: "",

    // Misc
    description: "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: PeopleSchema,
    onSubmit: async (values, { resetForm }) => {
      setIsLoading(true);
      const {
        guarantor1IdCard,
        guarantor1Passport,
        guarantor2IdCard,
        guarantor2Passport,
      } = values;

      const documentFiles = [
        guarantor1IdCard,
        guarantor1Passport,
        guarantor2IdCard,
        guarantor2Passport,
      ];

      // Upload all documents concurrently
      const uploadedFiles = await Promise.all(
        documentFiles.map(async (file) => {
          if (!file) return null; // Skip if no file is provided
          const formData = new FormData();
          formData.append("file", file);
          const res = await uploadFile(formData);
          return res?.data ?? null;
        })
      );

      // Destructure the uploaded URLs back into the values object
      [
        values.guarantor1IdCard,
        values.guarantor1Passport,
        values.guarantor2IdCard,
        values.guarantor2Passport,
      ] = uploadedFiles;

      console.log({ uploadedFiles, values });

      setIsLoading(false);
      addMutation.mutate({ values, resetForm });
    },
  });

  const userRoles: string[] = Object.values(UserRole);
  // console.log({ errors: formik.errors });

  const fields: InputField[] = [
    {
      type: "grid",
      name: "name",
      gridCol: 2,
      gridInputs: [
        {
          name: "firstName",
          type: "text",
          label: "First Name",
          placeholder: "Enter first name",
          validate: true,
        },
        {
          name: "lastName",
          type: "text",
          label: "Last Name",
          placeholder: "Enter last name",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "gender",
      gridCol: 2,
      gridInputs: [
        {
          name: "otherNames",
          type: "text",
          label: "Other Names",
          placeholder: "Enter other names",
          validate: true,
        },
        {
          name: "gender",
          type: "select",
          label: "Gender",
          options: ["Male", "Female", "Others"],
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "contact",
      gridCol: 2,
      gridInputs: [
        {
          name: "officialPhoneNumber",
          type: "text",
          label: "Phone Number",
          placeholder: "Enter phone",
          validate: true,
        },
        {
          name: "personalPhoneNumber",
          type: "text",
          label: "Personal Phone",
          placeholder: "Enter personal phone",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "emails",
      gridCol: 2,
      gridInputs: [
        {
          name: "personalEmail",
          type: "email",
          label: "Personal Email",
          placeholder: "Enter personal email",
          validate: true,
        },
        {
          name: "officialEmail",
          type: "email",
          label: "Official Email",
          placeholder: "Enter official email",
          validate: true,
        },
      ],
    },
    {
      type: "select",
      label: "Structure Level",
      name: "structuralLevel",
      placeholder: "Select structure level",
      options: structureLvls?.levels?.length ? structureLvls?.levels : [],
      validate: true,
    },
    {
      name: "address",
      type: "text",
      label: "Address",
      placeholder: "Enter address",
      validate: true,
    },
    {
      type: "grid",
      name: "dates",
      gridCol: 2,
      gridInputs: [
        {
          name: "dateOfBirth",
          type: "date",
          label: "Date of Birth",
          placeholder: "Select birth date",
          validate: true,
        },
        {
          name: "dateEmployed",
          type: "date",
          label: "Date Employed",
          placeholder: "Select employment date",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "employment",
      gridCol: 2,
      gridInputs: [
        {
          name: "role",
          type: "select",
          label: "Role",
          options: userRoles,
          validate: true,
        },
        {
          name: "designationName",
          type: "select",
          label: "Designation",
          placeholder: "Enter designation",
          options: designationList,
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "organization",
      gridCol: 2,
      gridInputs: [
        // {
        //   name: "corporateName",
        //   type: "text",
        //   label: "Corporate",
        //   placeholder: "Enter corporate",
        //   validate: true,
        // },
        // {
        //   name: "divisionName",
        //   type: "text",
        //   label: "Division",
        //   placeholder: "Enter division",
        //   validate: true,
        // },
        // {
        //   name: "groupName",
        //   type: "text",
        //   label: "Group",
        //   placeholder: "Enter group",
        //   validate: true,
        // },
        // {
        //   name: "unitName",
        //   type: "text",
        //   label: "Unit",
        //   placeholder: "Enter unit",
        //   validate: true,
        // },
        {
          name: "careerLevel",
          type: "select",
          label: "Career Path",
          placeholder: "Enter career path",
          options: careerPathList,
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "education",
      gridCol: 2,
      gridInputs: [
        {
          name: "educationDetailsInstitutions",
          type: "text",
          label: "Institutions",
          placeholder: "Enter institutions (comma separated)",
          validate: true,
        },
        {
          name: "educationDetailsYears",
          type: "text",
          label: "Years",
          placeholder: "Enter years (comma separated)",
          validate: true,
        },
        {
          name: "educationDetailsQualifications",
          type: "select",
          label: "Qualification",
          placeholder: "Enter qualification",
          options: educationQualifications,
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "guarantor1",
      gridCol: 2,
      // header: "Guarantor 1 Details",
      gridInputs: [
        {
          name: "guarantor1IdCard",
          type: "upload",
          label: "Guarantor 1 ID Card",
          placeholder: "Upload Id Card for Guarantor 1",
        },
        {
          name: "guarantor1Passport",
          type: "upload",
          label: "Guarantor 1 Passport",
          placeholder: "Upload Passport for Guarantor 1",
        },
        {
          name: "guarantor1FirstName",
          type: "text",
          label: "Guarantor 1 First Name",
          placeholder: "Enter first name",
          validate: true,
        },
        {
          name: "guarantor1LastName",
          type: "text",
          label: "Guarantor 1 Last Name",
          placeholder: "Enter last name",
          validate: true,
        },
        {
          name: "guarantor1Occupation",
          type: "text",
          label: "Guarantor 1 Occupation",
          placeholder: "Enter occupation",
          validate: true,
        },
        {
          name: "guarantor1Age",
          type: "text",
          label: "Guarantor 1 Age",
          placeholder: "Enter age",
          validate: true,
        },
        {
          name: "guarantor1Address",
          type: "text",
          label: "Guarantor 1 Address",
          placeholder: "Enter address",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "guarantor2",
      gridCol: 2,
      // header: "Guarantor 2 Details",
      gridInputs: [
        {
          name: "guarantor2IdCard",
          type: "upload",
          label: "Guarantor 2 ID Card",
          placeholder: "Upload Id Card for Guarantor 2",
        },
        {
          name: "guarantor2Passport",
          type: "upload",
          label: "Guarantor 2 Passport",
          placeholder: "Upload Passport for Guarantor 2",
        },
        {
          name: "guarantor2FirstName",
          type: "text",
          label: "Guarantor 2 First Name",
          placeholder: "Enter first name",
          validate: true,
        },
        {
          name: "guarantor2LastName",
          type: "text",
          label: "Guarantor 2 Last Name",
          placeholder: "Enter last name",
          validate: true,
        },
        {
          name: "guarantor2Occupation",
          type: "text",
          label: "Guarantor 2 Occupation",
          placeholder: "Enter occupation",
          validate: true,
        },
        {
          name: "guarantor2Age",
          type: "text",
          label: "Guarantor 2 Age",
          placeholder: "Enter age",
          validate: true,
        },
        {
          name: "guarantor2Address",
          type: "text",
          label: "Guarantor 2 Address",
          placeholder: "Enter address",
          validate: true,
        },
      ],
    },
    {
      name: "description",
      type: "textarea",
      label: "Description",
      placeholder: "Enter description",
      validate: true,
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Add New Employee
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          onClick={formik.submitForm}
          variant="primary"
          w="full"
          fontSize="sm"
          isLoading={addMutation.isPending || isLoading}
          loadingText="Creating employee..."
        >
          Create Employee
        </Button>
      </DrawerFooter>
    </>
  );
};

export default AddEmployee;
