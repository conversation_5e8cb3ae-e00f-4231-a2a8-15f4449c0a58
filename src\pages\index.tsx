import { HStack, Text } from "@chakra-ui/react";
import {
  HiOutlineBriefcase,
  HiOutlineUserGroup,
  HiOutlineViewGrid,
} from "react-icons/hi";
import AppBar from "../components/AppBar";
import CustomTab from "../components/custom/CustomTab";
import LeaveApplication from "../tabs/dashboard/LeaveApplication";
import CareerPathTab from "../tabs/home/<USER>";
import Designation from "../tabs/home/<USER>";

import { StackIcon } from "@radix-ui/react-icons";
import { IoMdPaper } from "react-icons/io";
import { useCurrentOrganization } from "../hooks/organization";
import OrganizationStructure from "../tabs/home/<USER>";
import PayrollTab from "../tabs/home/<USER>/Payroll";
import { TRole } from "../types/user";
import Employees from "../drawers/Employee";

const Home = () => {
  let { data: currentOrganization } = useCurrentOrganization();
  currentOrganization = currentOrganization?.data;
  const userRoles: TRole[] = ["admin", "employer", "hr"];
  const VisibleToHr: TRole[] = ["hr"];

  const tabs = [
    {
      label: "Organization Structure",
      icon: <StackIcon fontSize="22px" />,
      visibleTo: userRoles,
    },
    {
      label: "Designation",
      icon: <HiOutlineViewGrid size="22px" />,
      visibleTo: userRoles,
    },
    {
      label: "Career Path/Grade Levels",
      icon: <HiOutlineBriefcase size="22px" />,
      visibleTo: userRoles,
    },
    {
      label: "People",
      icon: <HiOutlineUserGroup size="22px" />,
      visibleTo: userRoles,
    },
    {
      label: "Leave Applications management",
      icon: <HiOutlineUserGroup size="22px" />,
      visibleTo: VisibleToHr,
    },
    {
      label: "Payroll",
      icon: <IoMdPaper size="20px" />,
      visibleTo: VisibleToHr,
    },
  ];

  const panels = [
    OrganizationStructure,
    Designation,
    CareerPathTab,
    Employees,
    LeaveApplication,
    PayrollTab,
  ];

  return (
    <>
      <AppBar heading="Home" />

      <HStack mb="5">
        <Text fontSize="lg" textTransform="capitalize">
          {currentOrganization?.name || ""}
        </Text>
      </HStack>

      <CustomTab
        tabList={tabs}
        tabPanels={panels}
        tabId="indexPageTab"
        userType={userRoles}
        variant="line"
        hasIcon={true}
      />
    </>
  );
};

export default Home;
