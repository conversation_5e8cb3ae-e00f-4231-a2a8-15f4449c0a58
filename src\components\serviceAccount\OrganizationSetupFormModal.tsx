import {
  Box,
  Button,
  Flex,
  Image,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Text,
  useDisclosure,
} from "@chakra-ui/react";
import { PlusIcon } from "@radix-ui/react-icons";
import bagIcon from "../../assets/icons/bag-frame.svg";
import CustomTab from "../custom/CustomTab";
import CustomStructure from "./form/Structures";
import OrganizationSetupForm from "./OrganizationSetupForm";

const OrganizationSetupFormModal = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  return (
    <>
      <Button
        onClick={onOpen}
        fontSize="small"
        size="sm"
        fontWeight="500"
        variant="primary"
        leftIcon={<PlusIcon />}
      >
        Setup Organization Structure
      </Button>

      <Modal onClose={onClose} size="xl" isOpen={isOpen} isCentered>
        <ModalOverlay />
        <ModalContent>
          <ModalCloseButton />
          <ModalHeader>
            <Flex>
              <Image src={bagIcon} ml={6} />
              <Box ml={2}>
                <Text as="h1" fontWeight="500">
                  Setup Organizational Structure
                </Text>
                <Text fontSize="sm" fontWeight="500" color="gray.500">
                  Set up your organization structure here
                </Text>
              </Box>
            </Flex>
          </ModalHeader>

          <ModalBody maxH="75vh" overflowY="auto">
            <CustomTab
              tabList={[
                {
                  label: "Default Structure",
                  showForAll: true,
                },
                {
                  label: "Custom Structure",
                  showForAll: true,
                },
              ]}
              tabPanels={[OrganizationSetupForm, CustomStructure]}
              tabId={"structureLevelSetup"}
            />
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default OrganizationSetupFormModal;
