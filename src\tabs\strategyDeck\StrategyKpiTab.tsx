import { Box, Grid } from "@chakra-ui/react";
import {
  ActivityLogIcon,
  DownloadIcon,
  PersonIcon,
  UploadIcon,
} from "@radix-ui/react-icons";
import { useCallback, useMemo, useState } from "react";
import { BsPeople } from "react-icons/bs";
import { HiOutlinePlus } from "react-icons/hi";
import { RiSendPlane2Fill } from "react-icons/ri";
import { getAllKPIs } from "../../api/kpis";
import SelectAsyncPaginate from "../../components/AsyncSelect";
import CrudUI, { CrudRow } from "../../components/CrudUi";
import CustomTab from "../../components/custom/CustomTab";
import { CustomTooltip } from "../../components/custom/Tooltip";
import StructurePick from "../../components/StructurePick/StructurePick";
import AddKPI from "../../drawers/AddKPI";
import CustomDrawer from "../../drawers/CustomDrawer";
import MyKpiReportDrawer from "../../drawers/KPIDetails";
import UpdateKPI from "../../drawers/UpdateKPI";
import UploadInitiative from "../../drawers/UploadInitiative";
import { useDeleteKPI } from "../../hooks/useKPIs";
import { useOrganizationDateFormat } from "../../hooks/useOrganizationDateFormat";
import { TKPI } from "../../types/kpis";
import { Status } from "../../types/objectives";
import { capitalizeFirst, downloadFile } from "../../utils";
import JDAndInitiativesTab from "./JDAndInitiative";
import CardList from "../../components/CardList";

const StrategyKpiTab = (): React.ReactElement => {
  const [teamMember, setTeamMember] = useState<any>(null);
  const [status, setStatus] = useState<Status>(Status.PENDING);

  const deleteMutation = useDeleteKPI();
  const organizationDateFormat = useOrganizationDateFormat();

  const [teamLeadLookUp, setTeamLeadLookUp] = useState("");
  const [teamInfo, setTeamInfo] = useState<{
    level_name: string;
    level_id: string;
  }>();

  const ORG_NAME = useMemo(
    () => localStorage.getItem("current_organization_short_name"),
    []
  );

  const handleLevelChange = useCallback((data: any, selectedLevel: string) => {
    const levelMap: Record<string, string> = {
      "corporate-level": "corporate_level__uuid",
      "departmental-level": "department__uuid",
      "divisional-level": "division__uuid",
      "group-level": "group__uuid",
      "unit-level": "unit__uuid",
    };
    const levelKey = levelMap[selectedLevel];
    if (levelKey) {
      setTeamLeadLookUp(`&${levelKey}=${data.uuid}`);
      setTeamInfo({ level_name: selectedLevel, level_id: data.uuid });
    }
  }, []);

  const tabList = useMemo(
    () => [
      {
        label: "Organization KPI",
        icon: <ActivityLogIcon />,
        showForAll: true,
      },
      { label: "Team KPI", icon: <BsPeople />, showForAll: true },
      {
        label: "Individual Team Member KPI",
        icon: <PersonIcon />,
        showForAll: true,
      },
    ],
    []
  );

  const tabPanels = useMemo(
    () => [
      () => (
        <CrudUI<TKPI>
          title="Strategy KPIs"
          queryKey="strategy-kpi"
          queryFn={(page) => getAllKPIs(page, undefined, status)}
          columns={[
            "KPI Name",
            "Upline Objective",
            "Upline Project",
            "Owner/Team",
            "Occurrence",
            "Routine type",
            "start Date",
            "Actions",
          ]}
          preTableComp={
            <Grid gap="2" mb="6" mt="6" templateColumns="repeat(4, 1fr)">
              <CardList
                allow_percent={false}
                cardDetails={[
                  {
                    title: "Pending KPI",
                    value: 0,
                    rate: 4,
                    width: "30%",
                    allow_percent: false,
                  },
                  {
                    title: "Active KPI",
                    value: 0,
                    rate: 4,
                    width: "30%",
                    allow_percent: false,
                  },
                  {
                    title: "Closed KPI",
                    value: 0,
                    rate: 4,
                    width: "30%",
                    allow_percent: false,
                  },
                ]}
              />
            </Grid>
          }
          renderRow={(kpi) => {
            return (
              <CrudRow
                fields={[
                  {
                    label: "KPI name",
                    value: capitalizeFirst(kpi?.name)?.replaceAll("kpi", "KPI"),
                  },
                  {
                    label: "Upline Objective",
                    value: kpi.uplineObjective[0].name,
                  },
                  {
                    label: "Upline Project",
                    value: kpi.uplineProject?.[0]?.name,
                  },
                  {
                    label: "Owner/Team",
                    value: kpi.ownerEmail.toLowerCase(),
                  },
                  {
                    label: "Occurrence",
                    value: kpi.afterOccurrence,
                  },
                  {
                    label: "Routine Type",
                    value: capitalizeFirst(kpi.routineType),
                  },
                  {
                    label: "Start Date",
                    value: organizationDateFormat(kpi.startDate),
                  },
                ].filter((item) => item.value)}
                item={kpi}
                itemName="KPI"
                onDelete={() => deleteMutation.mutate(kpi.id)}
                tableDataFields={[
                  capitalizeFirst(kpi?.name)?.replaceAll("kpi", "KPI"),
                  kpi.uplineObjective[0].name,
                  kpi.uplineProject?.[0]?.name || "",
                  kpi.ownerEmail.toLowerCase(),
                  kpi.afterOccurrence,
                  capitalizeFirst(kpi.routineType),
                  organizationDateFormat(kpi.startDate),
                ]}
                renderDrawer={
                  <>
                    {kpi.brief && (
                      <CustomTooltip
                        tip="Download KPI brief"
                        padding="2"
                        onClick={() => {
                          downloadFile(
                            kpi.brief || "",
                            kpi.name.replaceAll(" ", "-")
                          );
                        }}
                      >
                        <DownloadIcon />
                      </CustomTooltip>
                    )}
                    <CustomDrawer showModalBtnText="View" drawerSize="sm">
                      <MyKpiReportDrawer kpi={kpi} />
                    </CustomDrawer>

                    <CustomDrawer
                      showModalBtnText=""
                      showModalBtnVariant="primary"
                      showModalBtnColor="white"
                      leftIcon={<RiSendPlane2Fill />}
                      drawerSize="sm"
                    >
                      <UpdateKPI {...kpi} />
                    </CustomDrawer>
                  </>
                }
              />
            );
          }}
          actions={
            <>
              <CustomDrawer
                showModalBtnText="Upload KPIs"
                showModalBtnVariant="outline"
                showModalBtnColor="primary"
                leftIcon={<UploadIcon />}
                drawerSize="sm"
              >
                <UploadInitiative />
              </CustomDrawer>
              <CustomDrawer
                showModalBtnText="Add New KPI"
                showModalBtnVariant="primary"
                showModalBtnColor="white"
                leftIcon={<HiOutlinePlus />}
                drawerSize="sm"
              >
                <AddKPI />
              </CustomDrawer>
            </>
          }
        />
      ),
      () => (
        <>
          <Box maxW="700px" mx="auto" my={6}>
            <StructurePick
              handleLevelChange={(x, y) => handleLevelChange(x, y as string)}
              handleStructureLevelChange={() => {}}
            />
          </Box>
          {teamLeadLookUp && teamInfo && (
            <JDAndInitiativesTab
              team_lead_lookUp={teamLeadLookUp}
              team_info={teamInfo}
              key={teamLeadLookUp}
              isInInitiativePage={false}
            />
          )}
        </>
      ),
      () => (
        <>
          <Box w="80%" mx="auto" my={6}>
            <SelectAsyncPaginate
              onPointChange={null}
              url={`/client/${ORG_NAME}/employee/?some=2`}
              value={teamMember}
              onChange={setTeamMember}
              SelectLabel={(option: any) =>
                `${option.user.first_name} ${option.user.last_name}`
              }
              SelectValue={(option: any) => option.user.email}
              placeholder="Get Your Team Member"
            />
          </Box>
          {teamMember?.user?.email && (
            <Box key={teamMember.user.email}>
              <JDAndInitiativesTab
                isInInitiativePage={false}
                team_lead_lookUp={`&ownerEmail=${teamMember.user.email}`}
              />
            </Box>
          )}
        </>
      ),
    ],
    [status]
  );

  return (
    <CustomTab
      tabList={tabList}
      tabPanels={tabPanels}
      tabId="strategyKPITab"
      userType={[]}
      hasIcon
    />
  );
};

export default StrategyKpiTab;
