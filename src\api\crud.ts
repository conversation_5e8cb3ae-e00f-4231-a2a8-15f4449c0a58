import { Status } from "../types/objectives";
import { dashboardAPIRequest } from "./(dashboard)";
import { getCurrentUser } from "./user";

interface CRUDOptions {
  includeTenantAndUser?: boolean;
}

export interface Filters {
  status?: Status;
  ownerEmail?: string;
  levelId?: string | number;
}

export class CrudService<T extends { tenantId?: string; createdBy?: string } = any> {
  protected basePath: string;
  protected options: CRUDOptions;

  constructor(basePath: string, options?: CRUDOptions) {
    this.basePath = basePath;
    this.options = options || {
      includeTenantAndUser: true,
    };
  }

  async getAll(
    page?: number,
    limit?: number,
    status?: Status,
    ownerEmail?: string
  ) {
    return dashboardAPIRequest({
      url: this.basePath,
      method: "GET",
      params: { page, limit, status, ownerEmail },
    });
  }

  async getByFilters(filters: Filters, page?: number, limit?: number) {
    return dashboardAPIRequest({
      url: this.basePath,
      method: "GET",
      params: { ...filters, page, limit },
    });
  }

  async getById(id: number | string, name?: string) {
    return dashboardAPIRequest({
      url: `${this.basePath}/${id}`,
      method: "GET",
      params: { name },
    });
  }

  async create(data: Partial<T>, resetForm?: () => void) {
    if (this.options.includeTenantAndUser) {
      const [user] = await Promise.all([getCurrentUser()]);
      data.tenantId = user.tenantId;
      data.createdBy = user.email;
    }

    return dashboardAPIRequest({
      url: this.basePath,
      method: "POST",
      data,
      resetForm,
    });
  }

  async update(id: number | string, data: Partial<T>, resetForm?: () => void) {
    if (this.options.includeTenantAndUser) {
      const [user] = await Promise.all([getCurrentUser()]);
      data.tenantId = user.tenantId;
      data.createdBy = user.email;
    }

    return dashboardAPIRequest({
      url: `${this.basePath}/${id}`,
      method: "PUT",
      data,
      resetForm,
    });
  }

  async patch(id: number | string, data: Partial<T>, resetForm?: () => void) {
    if (this.options.includeTenantAndUser) {
      const [user] = await Promise.all([getCurrentUser()]);
      data.tenantId = user.tenantId;
      data.createdBy = user.email;
    }

    return dashboardAPIRequest({
      url: `${this.basePath}/${id}`,
      method: "PATCH",
      data,
      resetForm,
    });
  }

  async delete(id: number | string) {
    return dashboardAPIRequest({
      url: `${this.basePath}/${id}`,
      method: "DELETE",
    });
  }

  async bulkCreate(formData: FormData) {
    return dashboardAPIRequest({
      url: `${this.basePath}/bulk-upload`,
      method: "POST",
      data: formData,
      contentType: "multipart/form-data",
    });
  }
}
