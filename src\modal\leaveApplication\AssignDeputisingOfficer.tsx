import { Button, FormControl, FormLabel, useToast } from "@chakra-ui/react";
import { Form, Formik } from "formik";
import { useState } from "react";
import * as Yup from "yup";
import axiosInstance from "../../api";
import SelectAsyncPaginate from "../../components/AsyncSelect";

interface AssignDeputisingOfficeProps {
  employee_leave_application_id: number;
}

const AssignDeputisingOffice = ({
  employee_leave_application_id,
}: AssignDeputisingOfficeProps) => {
  const [selectedDeputizingOfficer, setSelectedDeputizingOfficer] =
    useState(null);
  const org_name = localStorage.getItem("current_organization_short_name");
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const validationSchema = Yup.object().shape({
    employee_uuid: Yup.number().required("Deputizing officer is required"),
    hand_over_file: Yup.mixed().required("Handover note is required"),
  });

  return (
    <Formik
      initialValues={{ employee_uuid: "", hand_over_file: null }}
      validationSchema={validationSchema}
      onSubmit={async (values, { setSubmitting }) => {
        const form = new FormData();
        form.append("employee_uuid", values.employee_uuid.toString());
        form.append(
          "employee_leave_application_id",
          employee_leave_application_id.toString()
        );
        if (values.hand_over_file) {
          form.append("hand_over_file", values.hand_over_file);
        }
        setIsLoading(true);

        try {
          const resp = await axiosInstance.post(
            `client/${org_name}/leave-management/leave-application/assign_handover/`,
            form
          );

          if (resp.data.status === 201) {
            toast({
              title: "Created Successfully",
              status: "success",
              position: "top",
              duration: 3000,
              isClosable: true,
            });
            setTimeout(() => window.location.reload(), 2000);
          }
        } catch (err) {
          const data = (err as any).response?.data;
          if (data?.error?.employee_leave_application_id) {
            toast({
              title: "Employee Handover Application Already Exists",
              status: "error",
              position: "top",
              duration: 3000,
              isClosable: true,
            });
          }
        }
        setIsLoading(false);
        setSubmitting(false);
      }}
    >
      {({ setFieldValue, errors, touched }) => (
        <Form>
          <FormControl mb="4">
            <FormLabel fontSize="xs" fontWeight="500">
              Deputizing officer
            </FormLabel>
            <SelectAsyncPaginate
              url={`/client/${org_name}/employee/?me=2`}
              value={selectedDeputizingOfficer}
              onChange={(value: any) => {
                setFieldValue("employee_uuid", value.uuid);
                setSelectedDeputizingOfficer(value);
              }}
              SelectLabel={(option: any) =>
                `${option.user.first_name} ${option.user.last_name}`
              }
              SelectValue={(option: any) => `${option.user.email}`}
              placeholder="Select a deputizing officer"
            />
            {errors.employee_uuid && touched.employee_uuid && (
              <div style={{ color: "red", fontSize: "12px" }}>
                {errors.employee_uuid}
              </div>
            )}
          </FormControl>

          <FormControl mb="4">
            <FormLabel fontSize="xs" fontWeight="500">
              Handover Note
            </FormLabel>
            <input
              type="file"
              onChange={(event) => {
                if (event.currentTarget.files) {
                  setFieldValue("hand_over_file", event.currentTarget.files[0]);
                }
              }}
            />
            {errors.hand_over_file && touched.hand_over_file && (
              <div style={{ color: "red", fontSize: "12px" }}>
                {errors.hand_over_file}
              </div>
            )}
          </FormControl>

          <Button
            type="submit"
            variant="primary"
            w="full"
            isLoading={isLoading}
            loadingText="Applying for Leave..."
            style={{ width: "40%", margin: "0 auto", display: "block" }}
          >
            Submit
          </Button>
        </Form>
      )}
    </Formik>
  );
};

export default AssignDeputisingOffice;