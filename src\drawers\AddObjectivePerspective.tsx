import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FormControl,
  FormLabel,
  InputProps,
  Select,
  Text,
  useToast,
} from "@chakra-ui/react";
import { Field, Form, Formik } from "formik";
import React from "react";
import * as yup from "yup";
import InputWithLabel from "../components/InputWithLabel";

interface AddObjectivePerspectiveSpreadInputs {
  objective_name: string;
  objective_owner: string;
  objective_type: string;
  duration: string;
  start_time: string;
  end_time: string;
  routineType: string;
  financial_target_point: number;
  customer_target_point: number;
  internal_process_target_point: number;
  learning_and_growth_target_point: number;
  total_target: number;
}

type InputTypeProps =
  | "text"
  | "number"
  | "password"
  | "email"
  | "tel"
  | "date"
  | "datetime-local"
  | "url"
  | "time"
  | "search";

export interface InputWithLabelProps extends InputProps {
  id: string;
  type?: InputTypeProps;
  bg?: string;
  label: string;
  value?: any;
  name?: string;
  disabled?: boolean;
  formErrorMessage?: string;
}

const validationSchema = yup.object().shape({
  objective_name: yup.string().required("Objective name is required"),
  objective_owner: yup.string().required("Objective owner is required"),
  objective_type: yup.string().required("Objective type is required"),
  duration: yup.string().required("Duration is required"),
  start_time: yup.string().required("Start time is required"),
  end_time: yup.string().required("End time is required"),
  routineType: yup.string().required("Routine type is required"),
  financial_target_point: yup.number().required("Financial target is required"),
  customer_target_point: yup.number().required("Customer target is required"),
  internal_process_target_point: yup
    .number()
    .required("Internal process target is required"),
  learning_and_growth_target_point: yup
    .number()
    .required("Learning & growth target is required"),
  total_target: yup.number().required("Total target is required"),
});

const inputFields: InputWithLabelProps[] = [
  {
    id: "objective_name",
    name: "objective_name",
    label: "Name of Objective/Perspective",
    type: "text",
    formErrorMessage: "Objective Name is required",
  },
  {
    id: "objective_owner",
    name: "objective_owner",
    label: "Objective Owner",
    type: "text",
    formErrorMessage: "Objective Owner is required",
  },
  {
    id: "duration",
    name: "duration",
    label: "Duration",
    type: "text",
    formErrorMessage: "Duration is required",
  },
  {
    id: "start_time",
    name: "start_time",
    label: "Start Time",
    type: "datetime-local",
    formErrorMessage: "Start Time is required",
  },
  {
    id: "end_time",
    name: "end_time",
    label: "End Time",
    type: "datetime-local",
    formErrorMessage: "End Time is required",
  },
  {
    id: "financial_target_point",
    name: "financial_target_point",
    label: "Financial Target Point",
    type: "number",
    formErrorMessage: "Financial Target Point is required",
  },
  {
    id: "customer_target_point",
    name: "customer_target_point",
    label: "Customer Target Point",
    type: "number",
    formErrorMessage: "Customer Target Point is required",
  },
  {
    id: "internal_process_target_point",
    name: "internal_process_target_point",
    label: "Internal Process Target Point",
    type: "number",
    formErrorMessage: "Internal Process Target Point is required",
  },
  {
    id: "learning_and_growth_target_point",
    name: "learning_and_growth_target_point",
    label: "Learning & Growth Target Point",
    type: "number",
    formErrorMessage: "Learning & Growth Target Point is required",
  },
  {
    id: "total_target",
    name: "total_target",
    label: "Total Target",
    type: "number",
    formErrorMessage: "Total Target is required",
  },
];

const selectFields = [
  {
    id: "objective_type",
    name: "objective_type",
    label: "Objective Type",
    options: [
      { value: "Qualitative", label: "Qualitative" },
      { value: "Quantitative", label: "Quantitative" },
      {
        value: "Quantitative & Qualitative",
        label: "Quantitative & Qualitative",
      },
    ],
  },
  {
    id: "routineType",
    name: "routineType",
    label: "Select Routine Options",
    options: [
      { value: "weekly", label: "Weekly" },
      { value: "monthly", label: "Monthly" },
      { value: "quarterly", label: "Quarterly" },
      { value: "bi-annually", label: "Bi-Annually" },
      { value: "annually", label: "Annually" },
    ],
  },
];

const AddObjectivePerspectiveSpread: React.FC = () => {
  const toast = useToast();

  const onSubmit = (values: AddObjectivePerspectiveSpreadInputs) => {
    console.log("Submitting:", values);
    // Add your submission logic here
  };

  return (
    <Formik
      initialValues={{
        objective_name: "",
        objective_owner: "",
        objective_type: "",
        duration: "",
        start_time: "",
        end_time: "",
        routineType: "",
        financial_target_point: 0,
        customer_target_point: 0,
        internal_process_target_point: 0,
        learning_and_growth_target_point: 0,
        total_target: 0,
      }}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      {({ errors, touched }) => (
        <>
          <DrawerCloseButton />
          <DrawerHeader>Create New Objective/Perspective</DrawerHeader>
          <DrawerBody>
            <Form id="add-objective-perspective-form">
              {inputFields.map(
                ({ id, name, label, type, formErrorMessage }) => (
                  <Field name={name} key={id}>
                    {({ field }: any) => (
                      <InputWithLabel
                        id={id}
                        label={label}
                        variant="filled"
                        bg="secondary.200"
                        mb="5"
                        type={type as InputTypeProps | undefined}
                        {...field}
                        formErrorMessage={
                          touched[
                            name as keyof AddObjectivePerspectiveSpreadInputs
                          ] &&
                          errors[
                            name as keyof AddObjectivePerspectiveSpreadInputs
                          ]
                            ? formErrorMessage
                            : undefined
                        }
                      />
                    )}
                  </Field>
                )
              )}

              {selectFields.map(({ id, name, label, options }) => (
                <FormControl id={id} mb="5" key={id}>
                  <FormLabel htmlFor={id}>{label}</FormLabel>
                  <Field
                    as={Select}
                    name={name}
                    placeholder="Select type"
                    variant="filled"
                    bg="secondary.200"
                  >
                    {options.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </Field>
                  <Text fontSize="sm" color="crimson">
                    {touched[
                      name as keyof AddObjectivePerspectiveSpreadInputs
                    ] &&
                      errors[name as keyof AddObjectivePerspectiveSpreadInputs]}
                  </Text>
                </FormControl>
              ))}
            </Form>
          </DrawerBody>
          <DrawerFooter>
            <Button
              type="submit"
              form="add-objective-perspective-form"
              variant="primary"
              w="full"
            >
              Create Objective
            </Button>
          </DrawerFooter>
        </>
      )}
    </Formik>
  );
};

export default AddObjectivePerspectiveSpread;
