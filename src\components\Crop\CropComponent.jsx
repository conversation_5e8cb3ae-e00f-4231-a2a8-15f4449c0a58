import {
  Box,
  Button,
  Flex,
  FormLabel,
  Modal,
  Modal<PERSON>ody,
  ModalClose<PERSON>utton,
  Modal<PERSON>nt,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Slider,
  SliderFilledTrack,
  SliderThumb,
  SliderTrack,
} from "@chakra-ui/react";
import { useCallback, useState } from "react";
import <PERSON>ropper from "react-easy-crop";
import { toast } from "sonner";
import getCroppedImg from "./CropEasy";

const CropImage = ({ setValue, fieldName }) => {
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [rotation, setRotation] = useState(0);
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [imageUrl, setImageUrl] = useState(null);
  const [isOpen, setIsOpen] = useState(false);

  const onCropComplete = useCallback((_, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const showCroppedImage = useCallback(async () => {
    if (!imageUrl) return;
    try {
      const croppedImage = await getCroppedImg(
        imageUrl,
        croppedAreaPixels,
        rotation
      );
      if (setValue) {
        setValue(fieldName, croppedImage.file);
        toast.success("Image Cropped");
      }
    } catch (e) {
      console.error(e);
    }
  }, [croppedAreaPixels, rotation, imageUrl, setValue, fieldName]);

  const onClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  const handleChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setIsOpen(true);
      setImageUrl(URL.createObjectURL(file));
    }
  };

  const handleRemoveImage = () => {
    setImageUrl(null);
    setIsOpen(false);
  };

  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent style={{ height: "500px" }}>
          <ModalHeader>Crop Photo</ModalHeader>
          <ModalCloseButton />
          <ModalBody style={{ position: "relative", width: "100%" }}>
            {imageUrl && (
              <div
                style={{
                  width: "400px",
                  height: "300px",
                  position: "absolute",
                }}
              >
                <Cropper
                  image={imageUrl}
                  crop={crop}
                  rotation={rotation}
                  zoom={zoom}
                  aspect={4 / 3}
                  onCropChange={setCrop}
                  onRotationChange={setRotation}
                  onCropComplete={onCropComplete}
                  onZoomChange={setZoom}
                />
              </div>
            )}
          </ModalBody>
          <ModalFooter style={{ display: "flex", flexDirection: "column" }}>
            <Box style={{ width: "50%", textAlign: "center" }}>
              <FormLabel fontSize="xs" fontWeight="500">
                Zoom
              </FormLabel>
              <Slider
                aria-label="slider-ex-1"
                defaultValue={1}
                min={1}
                value={zoom}
                onChange={setZoom}
              >
                <SliderTrack>
                  <SliderFilledTrack />
                </SliderTrack>
                <SliderThumb />
              </Slider>
            </Box>
            <br />
            <br />
            <Flex justifyContent="space-between" width="80%">
              <Button colorScheme="ghost" mr={3} onClick={onClose}>
                Close
              </Button>
              <Button variant="blue" onClick={showCroppedImage}>
                Save
              </Button>
            </Flex>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {imageUrl ? (
        <Button
          style={{ fontSize: "1rem", fontWeight: "normal" }}
          onClick={handleRemoveImage}
        >
          Cancel Upload
        </Button>
      ) : (
        <input type="file" onChange={handleChange} />
      )}
    </>
  );
};

export default CropImage;
