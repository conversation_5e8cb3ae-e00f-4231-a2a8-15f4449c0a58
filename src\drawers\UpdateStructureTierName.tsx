import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect, useRef } from "react";
import { ReuseableForm } from "../components/custom/form";
import { useUpdateTier } from "../hooks/useTier";
import { tierUpdateSchema } from "../schemas/structure";
import { TTier } from "../types/tier";

interface UpdateCorporateProps {
  item: TTier;
}

const UpdateStructureTierName: React.FC<UpdateCorporateProps> = ({ item }) => {
  const closeRef = useRef(null);

  const updateMutation = useUpdateTier(item.id as number);

  const initialValues = {
    tierName: item.tierName || "",
    tierDescription: item.tierDescription || "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: tierUpdateSchema,
    onSubmit: (values, { resetForm }) => {
      updateMutation.mutate({ values, resetForm });
        (closeRef?.current as any)
        .click();
    },
  });

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  const fields = [
    {
      name: "tierName",
      type: "text",
      label: "Structure Tier Name",
      placeholder: "Enter structure name",
      validate: true,
    },
    {
      name: "tierDescription",
      type: "textarea",
      label: "Description (Optional)",
      placeholder: "Write a brief description about this tier",
    },
  ];

  return (
    <>
      <DrawerCloseButton ref={closeRef} />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Structure Tier
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          onClick={formik.submitForm}
          fontWeight="500"
          variant="primary"
          w="full"
          size="sm"
          isLoading={updateMutation.isPending}
          loadingText="Updating tier name..."
        >
          Update
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UpdateStructureTierName;
