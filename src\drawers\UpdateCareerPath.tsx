import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Drawer<PERSON><PERSON><PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON>er,
  DrawerHeader,
  useDisclosure,
} from "@chakra-ui/react";
import { TCareerPath } from "../types/careerpath";
import { useUpdateCareerPath } from "../hooks/useCareerPaths";
import { ReuseableForm } from "../components/custom/form";
import { careerPathSchema, fields } from "./AddCareerPath";
import { useFormik } from "formik";



const UpdateCareerPath = ({ careerPath }: { careerPath: TCareerPath }) => {
  const { onClose } = useDisclosure();
const updateMutation = useUpdateCareerPath(careerPath.id,onClose);

 const initialValues = {
    name: careerPath.name ,
    level: careerPath.level,
    minAge: careerPath.minAge,
    maxAge: careerPath.maxAge,
    positionLifespan: careerPath.positionLifespan,
    yearsOfExperience: careerPath.yearsOfExperience,
    annualPackage: careerPath.annualPackage,
    educationalQualification: careerPath.educationalQualification,
    slotsAvailable: careerPath.slotsAvailable,
  };

  const formik = useFormik({
    initialValues,
    validationSchema: careerPathSchema,
    onSubmit: (values, { resetForm }) => 
      updateMutation.mutate({ values, resetForm })
  });

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader>Update Career Path Level</DrawerHeader>
      <DrawerBody>
       <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
                  variant="primary"
          w="full"
          isLoading={updateMutation.isPending}
          loadingText="Updating..."
          onClick={formik.submitForm}
        >
          Update Career Path
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UpdateCareerPath;
