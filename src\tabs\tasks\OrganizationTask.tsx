import { BsFillTrash2Fill } from "react-icons/bs";
import { HiOutlinePlus } from "react-icons/hi";
import { useGetCurrentUser } from "../../hooks/user";
import Preloader from "../../components/Preloader";
import CreateTaskModal from "../../modal/task/create-task";
import CustomDrawer from "../../drawers/CustomDrawer";
import UploadTask from "../../drawers/UploadTask";
import TypeVerifierUserChecker from "../../utils/userChecker";
import { Button } from "@chakra-ui/button";
import CrudUI, { CrudRow } from "../../components/CrudUi";
import { TTasks } from "../../types/tasks";
import { useOrganizationDateFormat } from "../../hooks/useOrganizationDateFormat";
import { RiSendPlane2Fill } from "react-icons/ri";
import { useDeleteTask, useGetTasks } from "../../hooks/useTask";
import { getAllTasks } from "../../api/tasks";
import TaskDetails from "../../drawers/TaskDetails";
import UpdateTask from "../../drawers/updateTasks";
import { Grid } from "@chakra-ui/layout";
import CardList from "../../components/CardList";

const OrganizationTasks = () => {
  const organizationDateFormat = useOrganizationDateFormat();
  const deleteMutation = useDeleteTask();

  const renderRow = (task: TTasks) => {
    const taskTableInformation = [
      {
        label: "Task Name",
        value: task.name || "N/A",
        key: "name",
      },
      {
        label: "Upline KPI",
        value: task.uplineKpi || "N/A",
        key: "uplineInitiative",
      },
      {
        label: "Owner",
        value: task.ownerEmail || "N/A",
        key: "ownerEmail",
      },
      {
        label: "Task Type",
        value: task.taskType || "N/A",
        key: "taskType",
      },
      {
        label: "Routine Type",
        value: task.routineType || "N/A",
        key: "routineType",
      },
      {
        label: "Start Date",
        value: task.startDate ? organizationDateFormat(task.startDate) : "N/A",
        key: "startDate",
      },
      {
        label: "End Date",
        value: task.endDate ? organizationDateFormat(task.endDate) : "N/A",
        key: "endDate",
      },
      {
        label: "Duration",
        value: task.duration || "N/A",
        key: "duration",
      },
      {
        label: "Created By",
        value: task.createdBy || "N/A",
        key: "createdBy",
      },
    ];

    return (
      <CrudRow
        item={task}
        itemName="Task"
        fields={taskTableInformation}
        renderDrawer={
          <>
            <CustomDrawer showModalBtnText="View" drawerSize="lg">
              <TaskDetails task={task} />
            </CustomDrawer>

            <CustomDrawer
              showModalBtnText=""
              showModalBtnVariant="primary"
              showModalBtnColor="white"
              leftIcon={<RiSendPlane2Fill />}
              drawerSize="lg"
            >
              <UpdateTask task={task} />
            </CustomDrawer>
          </>
        }
        onDelete={() => deleteMutation.mutate(task.id)}
        tableDataFields={taskTableInformation.map(({ value }) => value)}
      />
    );
  };

  const columns = [
    "Task Name",
    "Upline KPI",
    "Owner",
    "Task Type",
    "Routine Type",
    "Start Date",
    "End Date",
    "Duration",
    "Created By",
    "Actions",
  ];

  const actions = !TypeVerifierUserChecker(["employee"], "client_tokens") ? (
    <>
      <CreateTaskModal />
      <CustomDrawer
        showModalBtnText="Upload Task"
        showModalBtnVariant="outline"
        showModalBtnColor="primary"
        leftIcon={<HiOutlinePlus />}
        drawerSize="sm"
      >
        <UploadTask />
      </CustomDrawer>
      <Button
        leftIcon={<BsFillTrash2Fill />}
        color="white"
        backgroundColor="red.600"
        size="sm"
        fontWeight="500"
      >
        Delete Selected
      </Button>
    </>
  ) : null;

  return (
    <>
      <CrudUI<TTasks>
        title="Tasks"
        queryKey="organizationTasks"
        queryFn={(page: number) => getAllTasks(page)}
        columns={columns}
        renderRow={(task) => renderRow(task)}
        actions={actions}
        preTableComp={
          <Grid gap="2" mb="6" mt="6" templateColumns="repeat(4, 1fr)">
            <CardList
              allow_percent={false}
              cardDetails={[
                {
                  title: "Pending Tasks",
                  value: 0,
                  rate: 0,
                  allow_percent: false,
                },
                {
                  title: "Active Tasks",
                  value: 0,
                  rate: 0,
                  allow_percent: false,
                },
                {
                  title: "Overdue Tasks",
                  value: 0,
                  rate: 0,
                  allow_percent: false,
                },
                {
                  title: "Awaiting Rating Tasks",
                  value: 0,
                  rate: 0,
                  allow_percent: false,
                },

                {
                  title: "Completed Tasks",
                  value: 0,
                  rate: 0,
                  allow_percent: false,
                },
                {
                  title: "Tasks In Progress",
                  value: 0,
                  rate: 0,
                },
              ]}
            />
          </Grid>
        }
      />
    </>
  );
};

export default OrganizationTasks;
