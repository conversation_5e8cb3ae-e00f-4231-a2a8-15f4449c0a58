import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
} from "@chakra-ui/react";
import React from "react";
import CreateHrAdmin from "../components/CreateHrAdmin";

const CreateHrAdminDrawer = (): React.ReactElement => {
  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Create Hr Admin
      </DrawerHeader>
      <DrawerBody>
        <CreateHrAdmin />
      </DrawerBody>

      <DrawerFooter></DrawerFooter>
    </>
  );
};

export default CreateHrAdminDrawer