import { Box } from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect } from "react";
import { createStructuralLevel } from "../../../api/structuralLevel";
import { StructureSchema } from "../../../schemas/structure";
import CustomTab from "../../custom/CustomTab";
import { InputField, ReuseableForm } from "../../custom/form";
import Tiers from "./tiers";

const CustomStructure: React.FC = () => {
  return (
    <>
      <CustomTab
        tabList={[
          {
            label: "Create Structure",
            showForAll: true,
          },
          {
            label: "Add Tier",
            showForAll: true,
          },
        ]}
        tabPanels={[CustomStructureForm, Tiers]}
        tabId={"customStructureTabKey"}
      />
    </>
  );
};

const CustomStructureForm = () => {
  const initialValues = {
    name: localStorage.getItem("name") || "",
    description: localStorage.getItem("description") || "",
    priority: localStorage.getItem("priority") || "1",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: StructureSchema,
    onSubmit: async (values, { resetForm }) => {
      await createStructuralLevel(
        { ...values, priority: Number(values.priority) },
        resetForm
      );
    },
  });

  const fields: InputField[] = [
    {
      name: "name",
      type: "text",
      label: "Structure Name",
      placeholder: "Enter structure name",
      validate: true,
    },
    {
      name: "description",
      type: "textarea",
      label: "Description",
      placeholder: "Write a brief description about this structure",
    },
    {
      name: "priority",
      type: "number",
      label: "Priority",
      helpText:
        "Structure priority helps you organize your structure levels in a systematic hierarchy. Now you know which structure ranks higher than the other",
      placeholder: "Select priority",
      validate: true,
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <Box mx="4">
      <ReuseableForm
        formik={formik}
        inputArray={fields}
        button={{
          text: "Create Structure",
          buttonLoadingText: "Creating structure...",
        }}
      />
    </Box>
  );
};

export default CustomStructure;
