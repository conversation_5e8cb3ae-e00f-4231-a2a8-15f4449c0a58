import React, { useState } from "react";
import AppBar from "../../components/AppBar";

import { Box, Container, Flex, Heading, Text } from "@chakra-ui/layout";
import { Button } from "@chakra-ui/button";
import TriggerLeavePaymentTable from "../../components/LeaveRequestTable/TriggerLeavePaymentTable";
import PaymentSuccessModal from "../../components/LeaveRequestTable/PaymentSuccessModal";

const enum LeavePayment {
  PROFILE = "profile",
  TRIGGER = "trigger",
}

const TriggerLeavePayment = () => {
  // Sample data - replace with actual data from your API/state
  const [tab, setTab] = useState<LeavePayment>(LeavePayment.PROFILE);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const leavePaymentData = [
    {
      id: 1,
      employee: "<PERSON><PERSON><PERSON>",
      role: "UI/UX Designer",
      leaveType: "Paid Leave",
      requestDate: "Apr 1 - Apr 5, 2025",
      status: "Approved",
    },
    {
      id: 2,
      employee: "<PERSON>",
      role: "Frontend Developer",
      leaveType: "Paid Leave",
      requestDate: "Apr 8 - Apr 12, 2025",
      status: "Approved",
    },
    {
      id: 3,
      employee: "Jane Smith",
      role: "Backend Developer",
      leaveType: "Sick Leave",
      requestDate: "Apr 15 - Apr 17, 2025",
      status: "Approved",
    },
    {
      id: 4,
      employee: "Mike Johnson",
      role: "Product Manager",
      leaveType: "Paid Leave",
      requestDate: "Apr 20 - Apr 24, 2025",
      status: "Approved",
    },
    {
      id: 5,
      employee: "Sarah Wilson",
      role: "QA Engineer",
      leaveType: "Paid Leave",
      requestDate: "Apr 25 - Apr 29, 2025",
      status: "Approved",
    },
    {
      id: 6,
      employee: "David Brown",
      role: "DevOps Engineer",
      leaveType: "Paid Leave",
      requestDate: "May 1 - May 5, 2025",
      status: "Approved",
    },
  ];

  // Handle row action (Trigger Pay button click)
  const handleRowAction = (item: any) => {
    console.log("Trigger payment for leave request:", item);
    // Add your payment trigger logic here
    // e.g., call API to trigger payment
  };

  // Handle main trigger pay button click
  const handleMainTriggerPay = () => {
    setIsModalOpen(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <AppBar heading="Leave Management " />
      <Container maxWidth={"7xl"}>
        <Box borderBottom={"1px solid #E2E8F0"} mb={4} pb={2}>
          <Heading
            as={"h2"}
            fontSize={"2rem"}
            fontWeight={700}
            color={"#0B3178"}
            fontFamily="Inter"
          >
            Trigger Leave Payment
          </Heading>
        </Box>
        {tab === LeavePayment.PROFILE && (
          <>
            <Flex justifyContent="space-between" alignItems="center" mb={12}>
              <Box>
                <Text fontWeight={550} fontSize="1.2rem" fontFamily="Inter">
                  Trigger Leave Payment
                </Text>
                <Text
                  fontSize="sm"
                  fontStyle="italic"
                  color="gray.600"
                  fontFamily="Inter"
                  mt={1}
                >
                  Initiate payroll processing for upcoming approved paid leaves.
                </Text>
              </Box>
              <Button
                backgroundColor="#0B3178"
                color="white"
                _hover={{ bg: "#0B3178" }}
                fontSize="14px"
                fontWeight={500}
                px={10}
                py={6}
                borderRadius={"10px"}
                fontFamily="Inter"
                onClick={handleMainTriggerPay}
              >
                Trigger Pay
              </Button>
            </Flex>

            {/* Leave Payment Table */}
            <TriggerLeavePaymentTable
              data={leavePaymentData}
              title="Select from Organization Tree"
              onRowAction={handleRowAction}
              actionLabel="Trigger Pay"
              showCheckboxes={true}
              showSearch={true}
              showFilter={true}
            />
          </>
        )}

        {tab === LeavePayment.TRIGGER && (
          <>
            <Box>
              <Text
                fontWeight={600}
                fontSize="1.2rem"
                fontFamily="Inter"
                mb={4}
              >
                Payment Processing
              </Text>
              <Text fontSize="sm" color="gray.600" fontFamily="Inter">
                This section would contain the payment processing interface. You
                can add forms, payment gateway integration, or bulk payment
                options here.
              </Text>
            </Box>
          </>
        )}
      </Container>

      {/* Payment Success Modal */}
      <PaymentSuccessModal isOpen={isModalOpen} onClose={handleModalClose} />
    </>
  );
};

export default TriggerLeavePayment;
