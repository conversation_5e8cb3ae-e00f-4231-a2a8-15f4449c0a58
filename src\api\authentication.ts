import { makeApiRequest, REGISTER_EMAIL_KEY } from ".";
import { setSession } from "../utils/session";

export type APIMethods = "POST" | "GET" | "PUT" | "PATCH" | "DELETE";
export type TAuthTypes = "login" | "register" | "deactivate" | "reset";

interface AuthenticationServiceParams {
  url: string;
  values?: Record<string, any>;
  type?: TAuthTypes;
  method?: APIMethods;
  resetForm?: () => void;
}

export type TUserRole = "employer" | "employee" | "hr";

interface RegisterUserParams {
  name: string;
  email: string;
  phoneNumber: string;
  userRole?: TUserRole;
  password: string;
  resetForm?: () => void;
}

interface LoginUserParams {
  email: string;
  password: string;
  resetForm?: () => void;
}

export const authenticationService = async ({
  url,
  values,
  type,
  method = "POST",
  resetForm,
}: AuthenticationServiceParams): Promise<boolean> => {
  try {
    const response = await makeApiRequest({
      url: `auth/${url}`,
      method,
      data: values,
      resetForm,
      type: "auth-" + type,
    });

    if (!response) return false;
    if (type === "login") {
      setSession(response.data.token);
    } else if (type === "register" && values?.email) {
      localStorage.setItem(REGISTER_EMAIL_KEY, values.email);
    }
    return true;
  } catch (error) {
    console.error("Auth submission error:", error);
    return false;
  }
};

export const loginUser = async ({
  email,
  password,
  resetForm,
}: LoginUserParams): Promise<boolean> =>
  await authenticationService({
    values: {
      email,
      password,
    },
    url: "login",
    resetForm,
    type: "login",
  });

export const registerUser = async ({
  email,
  password,
  name,
  phoneNumber,
  // userRole,
  resetForm,
}: RegisterUserParams): Promise<boolean> =>
  await authenticationService({
    values: {
      name,
      email,
      phoneNumber,
      password,
      userRole: "employer",
    },
    url: "register",
    resetForm,
    type: "register",
  });

export const verifyAccount = async ({
  token,
}: {
  token: string;
}): Promise<boolean> => {
  return await authenticationService({
    url: "verify-account/?token=" + token,
  });
};

export const forgotPassword = async ({
  email,
  resetForm,
}: {
  email: string;
  resetForm?: () => void;
}): Promise<boolean> => {
  return await authenticationService({
    url: "forgot-password",
    resetForm,
    values: { email },
  });
};

export const resetPassword = async ({
  password,
  token,
  resetForm,
}: {
  password: string;
  token: string | null;
  resetForm?: () => void;
}): Promise<boolean> => {
  return await authenticationService({
    url: "reset-password",
    values: { newPassword: password, token },
    resetForm,
  });
};

export const deactivateAccount = async ({
  email,
  password,
}: LoginUserParams): Promise<boolean> => {
  return await authenticationService({
    values: { email, password },
    url: "deactivate-account",
    type: "deactivate",
  });
};
