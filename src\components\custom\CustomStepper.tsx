import { Box, Circle, Flex, Text, useColorModeValue } from "@chakra-ui/react";
import { useState } from "react";
import { structuralLevelIndexKey } from "../../utils/structuralLevel";
export interface Step {
  label: string;
  completed: boolean;
}

type CustomStepperProps = {
  steps: Step[];
  currentStep: number;
  components: React.ReactNode[];
};

const CustomStepper = ({
  steps,
  currentStep,
  components,
}: CustomStepperProps) => {
  const activeColor = useColorModeValue("primary", "blue.300");
  const inactiveColor = useColorModeValue("gray.300", "gray.600");
  const [selected, setSelected] = useState(currentStep);

  return (
    <Flex align="center" direction="column" justify="space-between" w="full">
      <Flex align="center" w="full" mb={10}>
        {steps.map((step, index) => {
          const isActive = index === selected;
          const isCompleted = selected > index;
          const color = isCompleted || isActive ? activeColor : inactiveColor;

          return (
            <Flex
              key={index}
              pos="relative"
              direction="column"
              align="center"
              flex="1"
              gap="1"
            >
              <Circle
                size="10"
                bg={color}
                color="white"
                fontWeight="500"
                transition="all 0.4s ease"
                cursor="pointer"
                onClick={() => {
                  setSelected(index);
                  localStorage.setItem(
                    structuralLevelIndexKey,
                    index.toString()
                  );
                }}
              >
                {isCompleted ? "✓" : index + 1}
              </Circle>
              <Text
                fontSize="small"
                textAlign="center"
                fontWeight="500"
                noOfLines={1}
                textColor={color}
                transition="all 0.4s ease"
                isTruncated
              >
                {step.label}
              </Text>
              {index < steps.length - 1 && (
                <Box
                  position="absolute"
                  height="2px"
                  bg={isCompleted ? activeColor : inactiveColor}
                  left="50%"
                  right="0"
                  top="50%"
                  transform="auto"
                  translateY="-10px"
                  translateX="50%"
                  zIndex="-1"
                  transition="all 0.4s ease"
                />
              )}
            </Flex>
          );
        })}
      </Flex>

      <Box w="100%" px={{ base: "2", sm: "4" }} maxW="500px">
        {components.map((cm, index) => {
          const isActive = index === selected;
          return isActive ? <Box key={index}>{cm}</Box> : <></>;
        })}
      </Box>
    </Flex>
  );
};

export default CustomStepper;
