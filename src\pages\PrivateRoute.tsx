import { Outlet, useNavigate } from "react-router-dom";
import Preloader from "../components/Preloader";
import { useCheckUserType } from "../hooks/useCheckUserType";
import { useGetCurrentUser } from "../hooks/user";
import { getSession } from "../utils/session";
import PageNotFound from "./notfound";

const PrivateRoute = () => {
  const accessToken = getSession();
  const navigate = useNavigate();

  const isAuthenticated = accessToken ? true : false;

  const { data: currentUser, isLoading } = useGetCurrentUser();

  const isAdmin = useCheckUserType(["employer", "hr", "admin"]);

  if (!isAuthenticated) {
    navigate("/");
    return;
  }

  if (isLoading) return <Preloader />;

  if (!currentUser || !isAdmin) return <PageNotFound />;

  return <Outlet />;
};

export default PrivateRoute;
