import {
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  useToast,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import moment from "moment";
import { useEffect, useState } from "react";
import * as yup from "yup";
import axiosInstance from "../api";
// import { CalendarInputWithoutPast } from "../components/DateInput";
import InputWithLabel from "../components/InputWithLabel";
import Preloader from "../components/Preloader";

type CreateTenantLicence = {
  duration: string;
  start_date: string;
  duration_number: number;
  end_time: string;
  start_time: string;
};

const schema = yup.object({
  duration: yup.string().required(),
  start_date: yup.string().required(),
  duration_number: yup.number().required(),
  start_time: yup.string().required(),
});

type Prop = {
  company_short_name: string;
};

const CreateTenantLicence = ({
  company_short_name,
}: Prop): React.ReactElement => {
  const toast = useToast();
  const [isLoading, setIsloading] = useState(false);
  const [isDeleteing, setIsDeleteing] = useState(false);

  const formik = useFormik<CreateTenantLicence>({
    initialValues: {
      duration: "",
      start_date: "",
      duration_number: 0,
      end_time: "22:00",
      start_time: "",
    },
    validationSchema: schema,
    onSubmit: async (values) => {
      let formateddata: any = {
        start_date: moment(values.start_date).format("YYYY-MM-DD"),
        end_time: "22:00",
        start_time: values.start_time,
      };

      if (values.duration === "daily") {
        formateddata["end_date"] = moment(values.start_date)
          .add("days", values.duration_number)
          .format("YYYY-MM-DD");
      }
      if (values.duration === "monthly") {
        formateddata["end_date"] = moment(values.start_date)
          .add("days", values.duration_number)
          .format("YYYY-MM-DD");
      }
      if (values.duration === "quarterly") {
        formateddata["end_date"] = moment(values.start_date)
          .add("months", 3)
          .format("YYYY-MM-DD");
      }

      setIsloading(true);
      try {
        const resp = await axiosInstance.post(
          `/client-management/manager/${company_short_name}/handle_lincene/`,
          formateddata
        );
        setIsloading(false);

        if (resp.data.status === 201) {
          toast({
            title: "Activation Set!",
            status: "success",
            position: "top",
            duration: 5000,
            isClosable: true,
          });
        }
        if (resp.data.status === 200) {
          toast({
            title:
              "This Client is currently Activate you have to wait till this activation Expires!",
            status: "error",
            position: "top",
            duration: 5000,
            isClosable: true,
          });
        }
      } catch (err: any) {
        setIsloading(false);
        toast({
          title: "Something went wrong refresh and try again or re-login",
          status: "error",
          position: "top",
          duration: 5000,
          isClosable: true,
        });
      }
    },
  });

  const formatQuestion = (): string => {
    if (formik.values.duration === "daily")
      return "How many days do you want it to last";
    if (formik.values.duration === "monthly")
      return "How many months do you want it to last";
    return "";
  };

  useEffect(() => {
    if (formik.values.duration === "quarterly") {
      formik.setFieldValue("duration_number", 3);
    }
    if (formik.values.duration === "monthly") {
      formik.setFieldValue("duration_number", 30);
    }
  }, [formik.values.duration]);

  return (
    <form style={{ padding: "1rem" }} onSubmit={formik.handleSubmit}>
      {isLoading && <Preloader />}

      <FormControl>
        <FormLabel fontSize="xs" htmlFor={"End Date"} fontWeight="500">
          Start Date
        </FormLabel>
        {/* <CalendarInputWithoutPast
          name="start_date"
          dateFormat="yyyy/MM/dd"
          value={formik.values.start_date}
          onChange={(date: any) => formik.setFieldValue("start_date", date)}
        /> */}
      </FormControl>

      <br />

      <FormControl>
        <FormLabel fontSize="xs" htmlFor={"End Date"} fontWeight="500">
          Start Time
        </FormLabel>
        <Input
          type="time"
          name="start_time"
          value={formik.values.start_time}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
        />
      </FormControl>

      <br />

      <Select
        placeholder="Duration"
        name="duration"
        value={formik.values.duration}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
      >
        <option value="daily">Daily</option>
        <option value="monthly">Monthly</option>
        <option value="quarterly">Quarterly</option>
      </Select>

      <br />

      {formik.values.duration && formik.values.duration !== "quarterly" && (
        <InputWithLabel
          id="duration_number"
          label={formatQuestion()}
          name="duration_number"
          value={formik.values.duration_number}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          type="number"
        />
      )}

      <br />
      <br />

      <Button
        type="submit"
        variant="primary"
        fontWeight="500"
        isLoading={isLoading}
        loadingText="Please Wait"
        size="lg"
      >
        Create Licence
      </Button>
    </form>
  );
};

export default CreateTenantLicence;
