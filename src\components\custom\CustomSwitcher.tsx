// CustomSwitcher.tsx
import { Box, useColorModeValue, useToken } from "@chakra-ui/react";
import { motion } from "framer-motion";
import { useState } from "react";

const MotionBox = motion.create(Box);

export const CustomSwitcher = ({
  isChecked = false,
  disabled = false,
  onChange,
}: {
  isChecked?: boolean;
  disabled?: boolean;
  onChange?: (val: boolean) => void;
}) => {
  const [checked, setChecked] = useState(isChecked);
  const toggle = () => {
    setChecked(!checked);
    onChange?.(!checked);
  };

  const bgOn = useColorModeValue("primary", "green.300");
  const bgOff = useColorModeValue("gray.300", "gray.600");
  const knobColor = useColorModeValue("white", "gray.900");
  const [onColor, offColor, knob] = useToken("colors", [
    bgOn,
    bgOff,
    knobColor,
  ]);

  return (
    <Box
      as="button"
      role="switch"
      aria-checked={checked}
      onClick={toggle}
      disabled={disabled}
      w="45px"
      h="22px"
      p="2px"
      rounded="full"
      bg={checked ? onColor : offColor}
      transition="background-color 0.2s"
      display="flex"
          alignItems="center"
          _disabled={{cursor:"not-allowed"}}
    >
      <MotionBox
        layout
        transition={{ type: "spring", stiffness: 900, damping: 25 }}
        w="16px"
        h="16px"
        bg={knob}
        rounded="full"
        initial={false}
        ml={checked ? "24px" : "0px"}
        boxShadow="md"
      />
    </Box>
  );
};
