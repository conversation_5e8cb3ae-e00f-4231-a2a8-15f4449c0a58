import {
  Box,
  Button,
  Flex,
  Heading,
  Input,
  Text,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  Stack,
  Grid,
  GridItem,
  useToast,
} from "@chakra-ui/react";
import { BsFillHouseDoorFill } from "react-icons/bs";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import { MdDateRange, MdDelete } from "react-icons/md";
import { Formik, Form, Field, FieldProps, ErrorMessage } from "formik";
import * as yup from "yup";
import { useEffect, useLayoutEffect, useRef, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import moment from "moment";
import FilterTableData from "../FilterTableData";
import Preloader from "../Preloader";

const validationSchema = yup.object().shape({
  name: yup.string().required("Please Enter Name"),
  date: yup.string().required("Please don't enter past date"),
});

const PublicHoliday = (): React.ReactElement => {
  const { showBoundary } = useErrorBoundary();
  const [start_date_after, setStart_date_after] = useState<string>("");
  const [start_date_before, setStart_date_before] = useState<string>("");
  const [status, setStatus] = useState<string>(""); // Added status state
  const toast = useToast();

  const handleGetHoliday = () => {
    // Your implementation
  };

  const onSubmit = (values: any) => {
    console.log({ "submitted data": values });
    setStatus("creating");
    // Add your submission logic here
  };

  useEffect(() => {
    handleGetHoliday();
  }, []);

  return (
    <Box style={{ width: "900px", margin: "0 auto" }}>
      <Formik
        initialValues={{ name: "", date: "" }}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ isSubmitting, handleSubmit }) => (
          <Form>
            {status === "pending" || status === "deleting" ? (
              <Preloader />
            ) : (
              <>
                <Flex justifyContent={"space-between"}>
                  <InputGroup width={"40%"}>
                    <InputLeftElement
                      pointerEvents="none"
                      children={<BsFillHouseDoorFill color="gray.300" />}
                    />
                    <Field name="name">
                      {({ field, form }: FieldProps) => (
                        <Input
                          {...field}
                          type="text"
                          placeholder="Public Holiday Name"
                          isInvalid={!!(form.errors.name && form.touched.name)}
                        />
                      )}
                    </Field>
                    <Text color="crimson">
                      <ErrorMessage name="name" />
                    </Text>
                  </InputGroup>

                  <InputGroup width={"40%"}>
                    <Field name="date">
                      {({ field, form }: FieldProps) => (
                        <Input
                          {...field}
                          type="date"
                          isInvalid={!!(form.errors.date && form.touched.date)}
                        />
                      )}
                    </Field>
                    <InputRightElement children={<MdDateRange color="green.500" />} />
                    <Text color="crimson">
                      <ErrorMessage name="date" />
                    </Text>
                  </InputGroup>

                  <Button
                    variant="primary"
                    type="submit"
                    isLoading={isSubmitting}
                    loadingText="Creating"
                  >
                    Create
                  </Button>
                </Flex>

                <br />
                <br />
                <br />
                <Grid templateColumns="repeat(5, 1fr)" gap={6} mb="10">
                  {[].length === 0
                    ? ""
                    : [].map((data: any, index: number) => (
                        <Box
                          width={"150px"}
                          textAlign="center"
                          borderWidth="1px"
                          borderRadius="lg"
                          padding={"1rem"}
                          boxShadow="xl"
                          key={index}
                          style={{ position: "relative" }}
                        >
                          <Heading fontSize="small">{data.name}</Heading>
                          <Text>
                            <br />
                            {data.date}
                          </Text>
                          <Text>
                            <MdDelete
                              style={{
                                position: "absolute",
                                top: "0",
                                right: "0",
                                cursor: "pointer",
                              }}
                              onClick={(e) => {
                                console.log("clicked");
                              }}
                            />
                          </Text>
                        </Box>
                      ))}
                </Grid>
              </>
            )}
          </Form>
        )}
      </Formik>
    </Box>
  );
};

export default PublicHoliday;