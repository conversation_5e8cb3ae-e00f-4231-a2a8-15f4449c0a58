import axios, { AxiosRequestConfig } from "axios";
import { toast } from "sonner";
import { getSession, removeSession } from "../utils/session";

export const REGISTER_EMAIL_KEY = "register_email";
export const EMAIL_VERIFIED_KEY = "email_verified";

interface ToastExtrasParams {
  error: any;
  label: string;
  urlTo?: string;
}

const toastExtras = ({ error, label, urlTo = "/" }: ToastExtrasParams) => ({
  description: error.response.data.errors[0].message,
  duration: 15000,
  action: {
    label,
    onClick: () => (window.location.href = urlTo),
  },
});

export const frontendUrl = (): string => window.location.origin;

export function goToLogin(): void {
  removeSession();
  const pathname = window.location.pathname;
  window.location.replace("/?next=" + pathname);
}

export function getAuthorizationHeader(): { Authorization: string } | null {
  const token = getSession();

  if (!token) return null;
  const accessToken = token;

  return { Authorization: "Bearer " + accessToken };
}

export const baseURL =
  process.env.NODE_ENV === "production"
    ? "https://node.emetricsapi.watchdoglogisticsng.com/"
    : process.env.NODE_ENV === "staging"
    ? "https://testnode.emetricsapi.watchdoglogisticsng.com/"
    : "https://testnode.emetricsapi.watchdoglogisticsng.com/";

const axiosInstance = axios.create({ baseURL, withCredentials: true });

export default axiosInstance;
export interface MakeApiRequestParams {
  url: string;
  method: AxiosRequestConfig["method"];
  data?: any;
  resetForm?: () => void;
  type?: string;
  contentType?: string;
  params?: Record<string, any>;
}

export async function makeApiRequest({
  url,
  method,
  data,
  resetForm,
  type = "",
  contentType = "application/json",
  params,
}: MakeApiRequestParams): Promise<any> {
  try {
    const authorization = getAuthorizationHeader();

    if (!authorization && !type.startsWith("auth")) {
      goToLogin();
      return;
    }

    const response = await axiosInstance({
      url,
      method,
      data,
      headers: {
        ...authorization,
        "Content-Type": contentType,
      },
      params,
    });

    if (response.status >= 200 && response.status < 300) {
      resetForm?.();
      const responseMessage = response.data.message;

      if (
        response.data.success &&
        responseMessage &&
        !url.includes("structural-level")
      ) {
        toast.success(responseMessage);
      }
      return response.data;
    } else {
      console.error({ tryError: response });
    }
  } catch (error: any) {
    if (!navigator.onLine) {
      toast.error("Network error. Please check your internet connection.");
      return;
    }

    if (error.response?.data?.statusCode === 44) {
      goToLogin();
      return;
    }

    if (error.status === 417) {
      toast("Account Deactivated", {
        ...toastExtras({
          error,
          label: "Reactivate Now",
          urlTo: "/reactivate-account",
        }),
      });
      return;
    }
    if (error.status === 418) {
      toast("Account Reactivated", {
        ...toastExtras({
          error,
          label: "Login",
          urlTo: "/login",
        }),
      });
      return;
    }

    // Handle general errors
    const errorMsg = error?.response?.data?.error;

    if (errorMsg && !url.includes("structural-level")) {
      toast.error(errorMsg);
      console.error("API request failed:", (error as any)?.message);
      return;
    }
    if (method?.toLowerCase() !== "get" && !url.includes("structural-level"))
      toast.error(error?.message || "Something went wrong");
    console.error(error);
    return;
  }
}
