import {
  But<PERSON>,
  Drawer,
  DrawerContent,
  DrawerOverlay,
  useDisclosure,
} from "@chakra-ui/react";
import { JSX, useRef } from "react";

type ShowModalBtnVariant =
  | "default"
  | "solid"
  | "outline"
  | "primary"
  | "ghost";
interface CustomDrawerProps {
  showModalBtnText: string;
  showModalBtnColor?: string;
  showModalBtnVariant?: ShowModalBtnVariant;
  children: JSX.Element;
  leftIcon?: JSX.Element;
  drawerSize?: "xs" | "sm" | "md" | "lg" | "xl" | "full";
  disabled?: boolean;
}

const CustomDrawer: React.FC<CustomDrawerProps> = ({
  children,
  showModalBtnText,
  showModalBtnColor,
  showModalBtnVariant = "default",
  leftIcon,
  drawerSize = "md",
  disabled = false,
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const btnRef: any = useRef(null);
  return (
    <>
      <Button
        color={showModalBtnColor}
        variant={showModalBtnVariant}
        ref={btnRef}
        onClick={onOpen}
        fontSize="small"
        size="sm"
        fontWeight="500"
        disabled={disabled}
        display="flex"
        gap="2"
      >
        {leftIcon} {showModalBtnText}
      </Button>
      <Drawer
        isOpen={isOpen}
        placement="right"
        onClose={onClose}
        finalFocusRef={btnRef}
        size={drawerSize}
      >
        <DrawerOverlay>
          <DrawerContent ml={-10}>{children}</DrawerContent>
        </DrawerOverlay>
      </Drawer>
    </>
  );
};

export default CustomDrawer;
