import AddObjective from "../../drawers/AddObjective";
import CustomDrawer from "../../drawers/CustomDrawer";
import ObjectiveDetails from "../../drawers/ObjectiveDrawer";

import { PlusIcon } from "@radix-ui/react-icons";
import { RiSendPlane2Fill } from "react-icons/ri";
import { getAllObjectives } from "../../api/objectives";
import CrudUI, { CrudRow } from "../../components/CrudUi";
import UpdateObjective from "../../drawers/UpdateObjective";
import UploadFileDrawer from "../../drawers/UploadFileDrawer";
import {
  objectivesQueryKey,
  useDeleteObjective,
} from "../../hooks/useObjectives";
import { useOrganizationDateFormat } from "../../hooks/useOrganizationDateFormat";
import { TObjective } from "../../types/objectives";
import { capitalizeFirst } from "../../utils";

const Objectives = ({
  isInObjectivePage = true,
}: {
  isInObjectivePage?: boolean;
}) => {
  const deleteMutation = useDeleteObjective();
  const organizationDateFormat = useOrganizationDateFormat();

  const renderRow = (objective: TObjective) => {
    const objectiveTableInformation = [
      { label: "Objective", value: capitalizeFirst(objective.name) },
      { label: "Owner/Team", value: objective.createdBy?.toLowerCase() || "" },
      { label: "Duration", value: objective.afterOccurrence || "" },
      {
        label: "Routine Options",
        value: capitalizeFirst(objective.routineType) || "",
      },
      {
        label: "Start Date",
        value: organizationDateFormat(objective.startDate),
      },
    ];

    return (
      <CrudRow
        item={objective}
        itemName="Objective"
        fields={objectiveTableInformation}
        renderDrawer={
          <>
            <CustomDrawer showModalBtnText="View" drawerSize="sm">
              <ObjectiveDetails {...objective} />
            </CustomDrawer>

            <CustomDrawer
              showModalBtnText=""
              showModalBtnVariant="primary"
              showModalBtnColor="white"
              leftIcon={<RiSendPlane2Fill />}
              drawerSize="sm"
            >
              <UpdateObjective objective={objective} />
            </CustomDrawer>
          </>
        }
        onDelete={() => deleteMutation.mutate(objective.id)}
        tableDataFields={objectiveTableInformation.map(({ value }) =>
          String(value)
        )}
      />
    );
  };

  return (
    <CrudUI<TObjective>
      title="Objectives"
      queryKey="objectives"
      queryFn={(page) => getAllObjectives(page)}
      columns={[
        "Objective",
        "Owner/Team",
        "Occurrence",
        "Routine type",
        "start Date",
        "Actions",
      ]}
      renderRow={renderRow}
      actions={
        isInObjectivePage && (
          <>
            <UploadFileDrawer
              uploadUrl="objectives/bulk-upload"
              resourceName="objectives"
              invalidateQueryKey={objectivesQueryKey}
            />

            <CustomDrawer
              showModalBtnText="Add New Objective"
              showModalBtnVariant="primary"
              showModalBtnColor="white"
              drawerSize="sm"
              leftIcon={<PlusIcon />}
            >
              <AddObjective />
            </CustomDrawer>
          </>
        )
      }
    />
  );
};

export default Objectives;
