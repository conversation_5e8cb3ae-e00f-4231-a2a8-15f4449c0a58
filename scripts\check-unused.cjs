const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");
const chalk = require("chalk");
const inquirer = require("inquirer");

const runCommand = (label, command) => {
  console.log(chalk.blue(`\n▶ ${label}\n`));
  try {
    return execSync(command, { encoding: "utf-8" });
  } catch (error) {
    console.error(chalk.red(`✖ Error running ${label}:`), error.message);
    return "";
  }
};

const runDepcheck = async () => {
  const depcheck = require("depcheck");

  console.log(
    chalk.blue("\n▶ Checking unused npm packages with depcheck...\n")
  );
  const result = await depcheck(process.cwd(), {
    ignoreDirs: ["dist", "build", "node_modules"],
  });

  const unused = [...result.dependencies, ...result.devDependencies];

  if (unused.length === 0) {
    console.log(chalk.green("✔ No unused npm packages found."));
    return;
  }

  console.log(chalk.yellow("⚠️ Unused npm packages:"));
  unused.forEach((pkg) => console.log("•", pkg));

  const { toDelete } = await inquirer.prompt([
    {
      type: "checkbox",
      name: "toDelete",
      message: "Select packages to uninstall:",
      choices: unused,
    },
  ]);

  if (toDelete.length) {
    console.log(chalk.red("🚮 Uninstalling selected packages..."));
    execSync(`npm uninstall ${toDelete.join(" ")}`, { stdio: "inherit" });
  }
};

const runUnimported = async () => {
  console.log(
    chalk.blue("\n▶ Checking unused files/modules with unimported...\n")
  );
  const output = runCommand(
    "unimported",
    "npx unimported --silent --reporter=json"
  );

  let parsed;
  try {
    parsed = JSON.parse(output);
  } catch {
    console.log(chalk.green("✔ No unused files found."));
    return;
  }

  const unusedFiles = parsed.unused?.map((f) => f.source) || [];

  if (unusedFiles.length === 0) {
    console.log(chalk.green("✔ No unused files found."));
    return;
  }

  console.log(chalk.yellow("⚠️ Unused files/modules:"));
  unusedFiles.forEach((file) => console.log("•", file));

  const { filesToDelete } = await inquirer.prompt([
    {
      type: "checkbox",
      name: "filesToDelete",
      message: "Select files to delete:",
      choices: unusedFiles,
    },
  ]);

  filesToDelete.forEach((file) => {
    const absPath = path.resolve(process.cwd(), file);
    if (fs.existsSync(absPath)) {
      fs.unlinkSync(absPath);
      console.log(chalk.red(`🗑️ Deleted: ${file}`));
    }
  });
};

const runTsPrune = () => {
  console.log(chalk.blue("\n▶ Checking unused exports with ts-prune...\n"));
  const result = runCommand("ts-prune", "npx ts-prune");
  if (!result.trim()) {
    console.log(chalk.green("✔ No unused exports found."));
  } else {
    console.log(chalk.yellow("⚠️ Unused exports:\n"));
    console.log(result);
  }
};

(async () => {
  console.log(
    chalk.yellow("\n🔍 Checking for unused packages, files, and exports...\n")
  );
  await runDepcheck();
  await runUnimported();
  runTsPrune();
})();
