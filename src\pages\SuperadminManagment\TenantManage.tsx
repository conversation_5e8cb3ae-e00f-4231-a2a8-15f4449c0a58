import { <PERSON>, Button, Stack } from "@chakra-ui/react";
import { HiOutlinePlus } from "react-icons/hi";
import { useNavigate } from "react-router-dom";
import AppBar from "../../components/AppBar";
import TenantManageTable from "../../components/TenantManage/TenantManageTable";

const TenantManage = () => {
  const route = useNavigate();

  return (
    <Box>
      <AppBar
        avatar="../../logo.png"
        imgAlt="Organization Avatar"
        heading="Tenant Management"
      />

      <Stack direction="row" spacing={4}>
        <Button
          // @ts-ignore
          showModalBtnText="Add New Perspective"
          showModalBtnVariant="primary"
          showModalBtnColor="white"
          leftIcon={<HiOutlinePlus />}
          drawerSize="xs"
          onClick={(e) => route("/sign-up")}
        >
          Create Tenant
        </Button>

        {/* <CustomModal openBtnText='Update' headingText='Update Tenant'subHeadText='..'>
                    <p> hell world</p>
                    </CustomModal> */}
      </Stack>

      <br />
      <TenantManageTable data={[]} />
    </Box>
  );
};
export default TenantManage;
