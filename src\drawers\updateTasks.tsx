import React, { useEffect, useMemo } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  useDisclosure,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { ReuseableForm, InputField } from "../components/custom/form";
import { useAddTask, useUpdateTask } from "../hooks/useTask";
import { useGetKPIs } from "../hooks/useKPIs";
import { RoutineType } from "../types/objectives";
import { TTasks } from "../types/tasks";
import { TaskSchema } from "./AddTask";
import { usePeopleEmails } from "../hooks/usePeople";

const UpdateTask = ({ task }: { task: TTasks }) => {
  const { onClose } = useDisclosure();
  const updateTaskMutation = useUpdateTask(task?.id, onClose);
  const { data: kpiData } = useGetKPIs();
  const peopleList = usePeopleEmails();

  const kpiList = kpiData?.data?.data?.map((item: any) => item?.name);
  const initialValues = {
    name: task?.name || "",
    uplineKpi: task?.uplineKpi || "", // Assuming this is the same as 'uplineKpi'
    ownerEmail: task?.ownerEmail || "",
    taskType: task?.taskType || "",
    routineType: task?.routineType || "",
    startDate: task?.startDate || "",
    startTime: task?.startTime || "",
    duration: task?.duration || "",
    repeatEvery: task?.repeatEvery || "",
    occursOnDaysWeekly: task?.occursOnDaysWeekly || "",
    occursOnDayNumberMonthly: task?.occursOnDayNumberMonthly || "",
    occursDayPositionMonthly: task?.occursDayPositionMonthly || "",
    occursOnDayMonthly: task?.occursOnDayMonthly || "",
    endDate: task?.endDate || "",
    afterOccurrence: task?.afterOccurrence || "",
    reworkLimit: task?.reworkLimit || "",
    qualityTargetPoint: task?.qualityTargetPoint || "",
    quantityTargetPoint: task?.quantityTargetPoint || "",
    quantityTargetUnit: task?.quantityTargetUnit || "",
    turnAroundTimeTargetPoint: task?.turnAroundTimeTargetPoint || "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: TaskSchema,
    onSubmit: (values, { resetForm }) => {
      updateTaskMutation.mutate({
        values: {
          ...values,
          duration: values?.duration?.toString(),
          repeatEvery: values?.repeatEvery?.toString(),
        },
        resetForm,
      });
    },
  });

  const routineTypesArray = Array.from(Object.values(RoutineType));

  // Define form fields
  const fields: InputField[] = [
    {
      name: "name",
      type: "text",
      label: "Task Name",
      placeholder: "Enter task name",
      validate: true,
    },
    {
      name: "uplineKpi",
      type: "select",
      label: "Upline Kpi",
      options: kpiList,
      validate: true,
    },
    {
      type: "grid",
      name: "contact",
      gridCol: 2,
      gridInputs: [
        {
          name: "ownerEmail",
          type: "select",
          label: "Owner Email",
          placeholder: "Enter owner email",
          options: peopleList,
          validate: true,
        },
        {
          name: "taskType",
          type: "text",
          label: "Task Type",
          placeholder: "Enter task type",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "timing",
      gridCol: 2,
      gridInputs: [
        {
          name: "startDate",
          type: "date",
          label: "Start Date",
          placeholder: "Select start date",
          validate: true,
        },
        {
          name: "startTime",
          type: "time",
          label: "Start Time",
          placeholder: "Select start time",
          validate: true,
        },
        {
          name: "duration",
          type: "number",
          label: "Duration",
          placeholder: "Enter duration",
          helpText: "Durations are in mins",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "recurrence",
      gridCol: 2,
      gridInputs: [
        {
          name: "routineType",
          type: "select",
          label: "Routine Type",
          options: routineTypesArray,
          placeholder: "Select routine type",
          validate: true,
        },
        {
          name: "repeatEvery",
          type: "number",
          label: "Repeat Every",
          placeholder: "e.g., 1 week, 2 months",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "weekly",
      gridCol: 1,
      gridInputs: [
        {
          name: "occursOnDaysWeekly",
          type: "text",
          label: "Occurs On (Weekly)",
          placeholder: "e.g., Mon, Wed",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "monthly",
      gridCol: 2,
      gridInputs: [
        {
          name: "occursOnDayNumberMonthly",
          type: "text",
          label: "Day Number (Monthly)",
          placeholder: "e.g., 15",
          validate: true,
        },
        {
          name: "occursDayPositionMonthly",
          type: "text",
          label: "Day Position (Monthly)",
          placeholder: "e.g., First, Last",
          validate: true,
        },
        {
          name: "occursOnDayMonthly",
          type: "text",
          label: "Day (Monthly)",
          placeholder: "e.g., Monday",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "conclusion",
      gridCol: 2,
      gridInputs: [
        {
          name: "endDate",
          type: "date",
          label: "End Date",
          placeholder: "Select end date",
          validate: true,
        },
        {
          name: "afterOccurrence",
          type: "text",
          label: "After Occurrences",
          placeholder: "Enter number of occurrences",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "targets",
      gridCol: 2,
      gridInputs: [
        {
          name: "qualityTargetPoint",
          type: "text",
          label: "Quality Target Point",
          placeholder: "Enter quality points",
          validate: true,
        },
        {
          name: "quantityTargetPoint",
          type: "text",
          label: "Quantity Target Point",
          placeholder: "Enter quantity points",
          validate: true,
        },
        {
          name: "quantityTargetUnit",
          type: "text",
          label: "Quantity Target Unit",
          placeholder: "Enter quantity units",
          validate: true,
        },
      ],
    },
    {
      name: "turnAroundTimeTargetPoint",
      type: "text",
      label: "Turn-Around Time Target Point",
      placeholder: "Enter TAT points",
      validate: true,
    },
    {
      name: "reworkLimit",
      type: "text",
      label: "Rework Limit",
      placeholder: "Enter rework limit",
      validate: true,
    },
  ];

  // Reset form values on mount
  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Existing Task
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          onClick={() => formik.submitForm()}
          variant="primary"
          w="full"
          isLoading={updateTaskMutation.isPending}
          loadingText="Updating task..."
        >
          Update Task
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UpdateTask;
