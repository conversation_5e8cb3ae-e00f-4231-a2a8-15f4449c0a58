import { Flex, ListIcon, ListItem, Text } from "@chakra-ui/react";
import { NavLink } from "react-router-dom";
import { blue200 } from "../theme/colors";

const NavItem = ({
  to,
  icon,
  target,
  children,
  isDisable = false,
  isActive = false,
  isVisible = true,
  toggleSidebar,
}: {
  to: string | object;
  icon: any;
  target?: string;
  children: React.ReactNode;
  isDisable?: boolean;
  isActive?: boolean;
  isVisible?: boolean;
  toggleSidebar?: () => void;
}) => (
  <ListItem bg="transparent" py="1" color="primary" m="0">
    <NavLink
      to={to}
      target={target}
      onClick={toggleSidebar}
      style={{
        padding: "5px",
        paddingLeft: "8px",
        display: "block",
        borderRadius: 5,
        backgroundColor: isActive ? blue200 : "transparent",
        color: isActive ? "#fff" : "gray.400",
        cursor: isDisable ? "not-allowed" : "pointer",
        pointerEvents: isActive ? "none" : "all",
      }}
    >
      <Flex
        alignItems="center"
        color={isActive ? "#fff" : "gray.500"}
        transition="color 0.4s"
        _hover={{ color: "black" }}
      >
        <ListIcon as={icon} fontSize={{ base: "base", "2xl": "small" }} />
        {isVisible && (
          <Text fontSize="small" fontWeight="400">
            {children}
          </Text>
        )}
      </Flex>
    </NavLink>
  </ListItem>
);

export default NavItem;
