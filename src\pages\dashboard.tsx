import { <PERSON>, Tab, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>anel, Tab<PERSON>ane<PERSON>, Tabs } from "@chakra-ui/react";
import { useEffect, useState } from "react";
import { BsBarChartFill } from "react-icons/bs";
import { HiOutlineUserCircle } from "react-icons/hi";
import AppBar from "../components/AppBar";
import DashboardKpi from "../tabs/dashboard/DashboardKpi/DashboardKpi";
import DashboardTask from "../tabs/dashboard/DashboardTask/DashboardTask";
import LeaveApplication from "../tabs/dashboard/LeaveApplication";
import MyTask from "../tabs/tasks/MyTask";
import StrategyKpiTab from "../tabs/strategyDeck/StrategyKpiTab";

/*
tabs of monthly ,daily ,weekly
but not are affected by this
so we save a var that holds the calculated date of the tab

function that get all performance using the tab calulated date,
function that get pending and closed task using the calulated date
function that get any task apart for pending and close which is not controlled by date
*/
const Dashboard = () => {
  const [tabIndex, setTabIndex] = useState<number>(0);

  useEffect(() => {
    const currentPageTab = localStorage.getItem("DasboardPageTab");
    if (currentPageTab) {
      setTabIndex(JSON.parse(currentPageTab));
    }
  }, []);

  return (
    <>
      <AppBar heading="Dashboard" />
      <Tabs
        colorScheme="primary"
        isLazy
        index={tabIndex}
        onChange={(currentIndex) => {
          setTabIndex(currentIndex);
          localStorage.setItem("DasboardPageTab", JSON.stringify(currentIndex));
        }}
      >
        <TabList>
          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
            <Box as="span" mr="2">
              <HiOutlineUserCircle size="22px" />
            </Box>
            Task
          </Tab>

          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
            <Box as="span" mr="2">
              <BsBarChartFill size="22px" />
            </Box>
            KPI
          </Tab>

          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
            <Box as="span" mr="2">
              <BsBarChartFill size="22px" />
            </Box>
            Leave Application
          </Tab>
        </TabList>

        <TabPanels pt="3">
          <TabPanel px="0">
            <MyTask />
          </TabPanel>

          <TabPanel px="0">
            <StrategyKpiTab />
          </TabPanel>

          <TabPanel px="0">
            <LeaveApplication />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </>
  );
};

export default Dashboard;
