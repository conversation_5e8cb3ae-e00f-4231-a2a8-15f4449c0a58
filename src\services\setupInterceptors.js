import axiosInstance from "../api";
import TokenService from "./token.service";

const setup = (store) => {
  axiosInstance.interceptors.request.use(
    (config) => {
      let token;

      if (
        window.location.pathname.includes("/organization") ||
        window.location.pathname.includes("/tenant-management")
      ) {
        token = TokenService.getLocalAccessToken();
      } else if (config.url.includes("/client")) {
        token = TokenService.getLocalAccessToken("client_tokens");
      }

      if (token) {
        config.headers["Authorization"] = `Bearer ${token}`;
        config.headers["Access-Control-Allow-Origin"] = "*";
      }

      return config;
    },
    (error) => {
      console.error("Request error:", error);
      return Promise.reject(error);
    }
  );

  const { dispatch } = store;

  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalConfig = error.config;

      return Promise.reject(error);
    }
  );
};

export default setup;
