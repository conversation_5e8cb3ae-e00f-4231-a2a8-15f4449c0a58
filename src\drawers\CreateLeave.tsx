import {
  <PERSON><PERSON>,
  Checkbox,
  <PERSON>er<PERSON>ody,
  <PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>er,
  FormControl,
  FormLabel,
  Select,
  Text,
  useToast,
} from "@chakra-ui/react";
import { Field, Form, Formik } from "formik";
import { useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import * as yup from "yup";
import SelectAsyncPaginate from "../components/AsyncSelect";
import InputWithLabel from "../components/InputWithLabel";

const validationSchema = yup.object().shape({
  leave_choice: yup.string().required("Leave Choice is required"),
  duration: yup.number().required("Duration is required"),
  leave_allowance: yup.string().required("Leave Allowance is required"),
  leave_formula: yup.number().required("Leave Formula is required"),
  grade_level: yup.string(),
});

const CreateLeave = () => {
  const org_name = localStorage.getItem("current_organization_short_name");
  const [careerP<PERSON><PERSON>, setCareerPaths] = useState<any>();
  const { showBoundary } = useErrorBoundary();
  const [forAll, setForAll] = useState(true);
  const toast = useToast();

  const onSubmit = (values: any) => {
    if (org_name) {
      console.log("Submitting:", values);
      // Add your submission logic here
    }
  };

  return (
    <Formik
      initialValues={{
        leave_choice: "",
        duration: 0,
        leave_allowance: "",
        leave_formula: 0,
        grade_level: "",
      }}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      {({ errors, touched, setFieldValue, values, isSubmitting }) => (
        <>
          <DrawerCloseButton />
          <DrawerHeader fontWeight="500" fontSize="md">
            Leave Creation
          </DrawerHeader>

          <DrawerBody>
            <Form id="add-leave-form">
              <FormLabel mb="3" fontSize="xs" fontWeight="500">
                Leave Type
              </FormLabel>
              <Field
                as={Select}
                placeholder="Leave Type"
                name="leave_choice"
                mb="3"
              >
                <option value="annual">Annual</option>
                <option value="sick">Sick</option>
                <option value="maternity">Maternity</option>
                <option value="paternity">Paternity</option>
                <option value="bereavement">Bereavement</option>
                <option value="compensatory">Compensatory</option>
                <option value="sabbatical">Sabbatical</option>
              </Field>
              <Text fontSize="sm" color="crimson">
                {touched.leave_choice && errors.leave_choice}
              </Text>

              <Field name="duration">
                {({ field }: any) => (
                  <InputWithLabel
                    id="duration"
                    type="number"
                    label="Number of days (duration)"
                    variant="filled"
                    bg="secondary.200"
                    {...field}
                    formErrorMessage={touched.duration && errors.duration}
                    mb="3"
                  />
                )}
              </Field>

              <Field name="leave_allowance">
                {({ field }: any) => (
                  <InputWithLabel
                    id="leave_allowance"
                    type="number"
                    label="Leave Allowance"
                    variant="filled"
                    bg="secondary.200"
                    {...field}
                    formErrorMessage={
                      touched.leave_allowance && errors.leave_allowance
                    }
                    mb="3"
                  />
                )}
              </Field>

              <FormLabel fontSize="xs" fontWeight="500">
                Leave Formula
              </FormLabel>
              <Field
                as={Select}
                placeholder="Leave Formula"
                name="leave_formula"
              >
                <option value="5">5%</option>
                <option value="10">10%</option>
                <option value="15">15%</option>
                <option value="20">20%</option>
                <option value="25">25%</option>
                <option value="30">30%</option>
                <option value="35">35%</option>
                <option value="40">40%</option>
                <option value="45">45%</option>
                <option value="50">50%</option>
                <option value="55">55%</option>
                <option value="60">60%</option>
                <option value="65">65%</option>
                <option value="70">70%</option>
                <option value="75">75%</option>
                <option value="80">80%</option>
                <option value="85">85%</option>
                <option value="90">90%</option>
                <option value="95">95%</option>
                <option value="100">100%</option>
              </Field>
              <Text fontSize="sm" color="crimson">
                {touched.leave_formula && errors.leave_formula}
              </Text>

              {!forAll && (
                <FormControl>
                  <FormLabel fontSize="xs" fontWeight="500">
                    Pick Career Path Level (Grade Level)
                  </FormLabel>
                  <SelectAsyncPaginate
                    onPointChange={null}
                    url={`/client/${org_name}/career-path?me=1`}
                    value={careerPaths}
                    onChange={(value: any) => {
                      setFieldValue("grade_level", value?.level);
                      setCareerPaths(value);
                    }}
                    SelectLabel={(option: any) => `${option.name}`}
                    SelectValue={(option: any) => `${option.level}`}
                    placeholder=""
                  />
                </FormControl>
              )}

              <Checkbox
                isChecked={forAll}
                onChange={(e) => setForAll(e.target.checked)}
                mt="3"
              >
                Apply for all levels
              </Checkbox>
            </Form>
          </DrawerBody>

          <DrawerFooter>
            <Button
              type="submit"
              form="add-leave-form"
              variant="primary"
              w="full"
              isLoading={isSubmitting}
              loadingText="Creating Leave..."
            >
              Create Leave
            </Button>
          </DrawerFooter>
        </>
      )}
    </Formik>
  );
};

export default CreateLeave;