import { HiOutlinePlus } from "react-icons/hi";
import { RiSendPlane2Fill } from "react-icons/ri";
import { getAllPerspectives } from "../../api/perspectives";
import CrudUI, { CrudRow } from "../../components/CrudUi";
import AddPerspective from "../../drawers/AddPerspective";
import CustomDrawer from "../../drawers/CustomDrawer";
import UpdatePerspective from "../../drawers/UpdatePerspective";
import UploadFileDrawer from "../../drawers/UploadFileDrawer";
import {
  perspectivesQueryKey,
  useDeletePerspective,
} from "../../hooks/usePerspectives";
import { TPerspective } from "../../types/perspectives";
import { capitalizeFirst } from "../../utils";

const Perspectives = () => {
  const deleteMutation = useDeletePerspective();
  const actions = (
    <>
      <UploadFileDrawer
        uploadUrl="perspectives/bulk-upload"
        resourceName="perspectives"
        invalidateQueryKey={perspectivesQueryKey}
      />
      <CustomDrawer
        showModalBtnText="Add New Perspective"
        showModalBtnVariant="primary"
        showModalBtnColor="white"
        leftIcon={<HiOutlinePlus />}
        drawerSize="xs"
      >
        <AddPerspective />
      </CustomDrawer>
    </>
  );

  return (
    <CrudUI<TPerspective>
      title="Perspectives"
      queryKey="perspectives"
      queryFn={(page: number) => getAllPerspectives(page)}
      columns={[
        "Name of Perspective",
        "Target Point",
        "Achieved Points",
        "Achieved Rating (%)",
        "",
        "",
      ]}
      actions={actions}
      renderRow={(perspective) => {
        return (
          <CrudRow
            fields={[
              {
                label: "Perspective Name",
                value: capitalizeFirst(perspective.name),
              },
              {
                label: "Target Point",
                value: perspective.targetPoint,
              },
            ]}
            item={perspective}
            itemName="Perspective"
            onDelete={(id) => deleteMutation.mutate(id)}
            renderDrawer={
              <CustomDrawer
                showModalBtnText=""
                showModalBtnVariant="primary"
                showModalBtnColor="white"
                leftIcon={<RiSendPlane2Fill />}
                drawerSize="xs"
              >
                <UpdatePerspective perspective={perspective} />
              </CustomDrawer>
            }
            tableDataFields={[
              perspective.name,
              `${perspective?.targetPoint}`,
              "",
              "",
            ]}
          />
        );
      }}
    />
  );
};

export default Perspectives;
