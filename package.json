{"name": "e-metrics", "version": "0.1.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "start": "vite preview", "check:unused": "node scripts/check-unused.cjs"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@chakra-ui/button": "^2.1.0", "@chakra-ui/icons": "^2.2.6", "@chakra-ui/layout": "^2.3.1", "@chakra-ui/menu": "^2.2.1", "@chakra-ui/react": "2.8.2", "@chakra-ui/system": "^2.6.2", "@chakra-ui/theme-tools": "^2.2.8", "@chakra-ui/toast": "^7.0.2", "@chakra-ui/utils": "^2.2.4", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/inter": "^5.1.1", "@fontsource/poppins": "^5.1.1", "@hookform/resolvers": "^4.1.2", "@radix-ui/react-icons": "^1.3.2", "@reduxjs/toolkit": "^2.6.0", "@sentry/react": "^9.1.0", "@sentry/tracing": "^7.120.3", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-devtools": "^5.69.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/react-clock": "^4.1.0", "@types/react-datepicker": "^7.0.0", "@types/react-dom": "^19.0.4", "@types/react-gauge-chart": "^0.4.3", "@types/react-redux": "^7.1.18", "@types/react-time-picker": "^6.0.0", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.7.9", "crypto-js": "^4.2.0", "D": "^1.0.0", "date-fns": "^4.1.0", "dompurify": "^3.2.4", "formik": "^2.4.6", "framer-motion": "^12.4.7", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.12.6", "lodash.debounce": "^4.0.8", "lucide-react": "^0.511.0", "moment": "^2.29.1", "moment-timezone": "^0.5.48", "rc-time-picker": "^3.7.3", "react": "^19.0.0", "react-big-calendar": "^1.5.0", "react-datepicker": "^8.1.0", "react-dom": "^19.0.0", "react-duration-picker": "^1.1.1", "react-easy-crop": "^5.2.0", "react-error-boundary": "^5.0.0", "react-gauge-chart": "^0.5.1", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-image-crop": "^11.0.7", "react-markdown": "^10.1.0", "react-paginate": "^8.1.3", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-responsive": "^10.0.0", "react-router-dom": "^7.2.0", "react-select": "^5.10.1", "react-select-async-paginate": "^0.7.9", "react-table": "^7.8.0", "react-time-picker": "^7.0.0", "react-window": "^1.8.11", "sonner": "^2.0.1", "styled-components": "^6.1.15", "typescript": "^5.7.3", "uuid": "^11.1.0", "vite-plugin-compression": "^0.5.1", "yup": "^1.6.1"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.13.5", "@types/react": "^19.0.10", "@types/react-big-calendar": "^1.16.1", "@types/react-router-dom": "^5.1.8", "@types/react-table": "^7.7.20", "autoprefixer": "^10.4.20", "depcheck": "^1.4.7", "inquirer": "8", "postcss": "^8.5.3", "tailwindcss": "^4.0.8", "ts-prune": "^0.10.3", "unimported": "^1.31.1", "vite": "^6.3.2"}}