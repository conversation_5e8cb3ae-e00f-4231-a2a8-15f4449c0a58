import { Stack } from "@chakra-ui/react";
import { HiOutlinePlus } from "react-icons/hi";
import CustomDrawer from "../../drawers/CustomDrawer";
import AddTask from "../../drawers/AddTask";

const CreateTaskModal = () => {
  return (
    <>
      <Stack
        direction={{ base: "column", md: "row" }}
        w={{ base: "full", md: "fit-content" }}
        spacing={4}
      >
        <CustomDrawer
          showModalBtnText="Add Task"
          showModalBtnVariant="primary"
          showModalBtnColor="white"
          leftIcon={<HiOutlinePlus />}
          drawerSize="md"
        >
          <AddTask />
        </CustomDrawer>
      </Stack>
      {/* <Button variant={"primary"} leftIcon={<HiOutlinePlus />} onClick={onOpen}>
        Add Task
      </Button>
      <Modal onClose={onClose} size="4xl" isOpen={isOpen}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <Flex my="3" ml="0">
              <Image src={bagIcon} ml={5} />
              <Box ml={2}>
                <Text as="h1" fontWeight="500">
                  Setup Task
                </Text>
                <Text as="h2" fontSize="md" color="gray.600">
                  Start setting up your Task here
                </Text>
              </Box>
            </Flex>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody> <TaskCreateForm /> </ModalBody>
          <ModalFooter>
            <Button onClick={onClose}>Close</Button>
          </ModalFooter>
        </ModalContent>
      </Modal> */}
    </>
  );
};

export default CreateTaskModal;
