import { Flex, <PERSON><PERSON>, <PERSON> } from "@chakra-ui/react";
import { blue200 } from "../theme/colors";
import { removeSession } from "../utils/session";

const UnauthorizedPage = () => {
  return (
    <Flex
      alignItems="center"
      justifyContent="center"
      textAlign="center"
      gap="6"
      flexDirection="column"
      mt="10"
      p="5"
      height="70vh"
    >
      <Heading as="h1" size="lg" fontWeight="500" maxW="500px">
        You Don't Have Correct Authorization to this Page
      </Heading>

      <Link
        href="/"
        onClick={removeSession}
        bg={blue200}
        color="whitesmoke"
        textDecoration="none"
        borderRadius="6"
        px="4"
        py="1"
        fontSize="sm"
      >
        Login
      </Link>
      <Link
        href="/sign-up"
        onClick={removeSession}
        fontSize="sm"
        color={blue200}
      >
        Create an organization
      </Link>
    </Flex>
  );
};

export default UnauthorizedPage;
