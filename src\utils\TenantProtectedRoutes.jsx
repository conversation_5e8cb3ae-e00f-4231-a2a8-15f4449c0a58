import { Route } from "react-router-dom";
import { tenantRoutes } from "../routes";
import Layout from "../components/Layout";
import Preloader from "../components/Preloader";

const TenantProtectedRoutes = () => {
  return tenantRoutes.map(({ component: Component, path }) => (
    <Route
      path={path}
      key={path}
      loader={<Preloader />}
      element={
        <Layout>
          <Component />
        </Layout>
      }
    />
  ));
};

export default TenantProtectedRoutes;
