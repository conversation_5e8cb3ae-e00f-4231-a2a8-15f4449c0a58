import { Box } from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect } from "react";
import { toast } from "sonner";
import { createStructuralLevel } from "../../../api/structuralLevel";
import { createTier } from "../../../api/tierName";
import { useGetOrganizationStructures } from "../../../hooks/organization";
import { useGetTiers } from "../../../hooks/useTier";
import { tierSchema } from "../../../schemas/structure";
import { TTier } from "../../../types/tier";
import { defaultStructuralLevelArray } from "../../../utils/structuralLevel";
import { InputField, ReuseableForm } from "../../custom/form";

const Tiers = ({ isDefault = false }: { isDefault?: boolean }) => {
  const { customStructures, defaultStructures } =
    useGetOrganizationStructures();

  const structureLevel = isDefault ? defaultStructures : customStructures;

  const defaultStructureExist = defaultStructuralLevelArray.reduce(
    (acc, level) => {
      const exists = structureLevel?.some(
        (l) => l.name.toLowerCase() === level.toLowerCase()
      );
      acc[level.toLowerCase()] = exists;
      return acc;
    },
    {} as Record<string, boolean>
  ) as {
    corporate: boolean;
    division: boolean;
    group: boolean;
    department: boolean;
    unit: boolean;
  };

  const initialValues = {
    tierName: localStorage.getItem("tierName") || "",
    level: localStorage.getItem("level") || "",
    uplineName: localStorage.getItem("uplineName") || "",
    tierDescription: localStorage.getItem("tierDescription") || "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: tierSchema,
    onSubmit: async (values, { resetForm }) => {
      const level = structureLevel?.find(
        (structure) =>
          structure.name.toLowerCase() === values.level.toLowerCase()
      );

      if (!level) {
        toast.error("No Structural level found");
        return;
      }
      const tier = tiers.find(
        (tier) =>
          tier.tierName.toLowerCase() === values.uplineName.toLowerCase()
      );

      const newValues: TTier = {
        levelId: level?.id,
        tierName: values.tierName,
        uplineId: tier?.id || level.id,
        uplineName: values.uplineName || level.name,
        tierDescription: values.tierDescription,
        uplineValue: tier?.tierName || level.name,
      };

      await createTier(newValues, resetForm);
    },
  });

  const level = structureLevel.find(
    (structure) =>
      structure.name.toLowerCase() === formik.values.level.toLowerCase()
  );
  const uplineLevel = level
    ? structureLevel.at(structureLevel.indexOf(level) + 1)
    : undefined;

  const { data: tiersData } = useGetTiers(
    undefined,
    100000,
    uplineLevel?.id || 0
  );

  const tiers = (tiersData?.data?.data as TTier[]) || [];

  const fields: InputField[] = [
    {
      name: "tierName",
      type: "text",
      label: "Tier Name",
      placeholder: "Enter structure name",
      helpText:
        "This represents the name of a tier or hierarchy in your organization structure. Use full names e.g Chief Executive Officer instead of CEO for a more professional look.",
      validate: true,
    },
    {
      name: "level",
      type: "select",
      label: "Structure Level",
      placeholder: "Select structural level",
      options: structureLevel.map((structure) => structure.name),
      validate: true,
    },
    {
      name: "uplineName",
      type: "select",
      label: "Choose Upline",
      placeholder: "Select upline",
      options: tiers.map((t) => t.tierName),
      visible: level
        ? !(structureLevel.indexOf(level) === structureLevel.length - 1)
        : undefined,
      validate: true,
    },
    {
      name: "tierDescription",
      type: "textarea",
      label: "Description (Optional)",
      placeholder: "Write a brief description about this tier",
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
    if (level && structureLevel.indexOf(level) === structureLevel.length - 1) {
      formik.setFieldValue("uplineName", level.name);
    }

    if ((structureLevel || []).length <= 0) return;
    if (!defaultStructureExist.corporate) {
      (async () =>
        await createStructuralLevel({
          name: "corporate",
          priority: 1,
          structureType: "default",
        }))();
    }
    if (!defaultStructureExist.division) {
      (async () =>
        await createStructuralLevel({
          name: "division",
          priority: 2,
          structureType: "default",
        }))();
    }
    if (!defaultStructureExist.group) {
      (async () =>
        await createStructuralLevel({
          name: "group",
          priority: 3,
          structureType: "default",
        }))();
    }
    if (!defaultStructureExist.department) {
      (async () =>
        await createStructuralLevel({
          name: "department",
          priority: 4,
          structureType: "default",
        }))();
    }
    if (!defaultStructureExist.unit) {
      (async () =>
        await createStructuralLevel({
          name: "unit",
          priority: 5,
          structureType: "default",
        }))();
    }
  }, [structureLevel]);

  return (
    <Box mx="2">
      <ReuseableForm
        formik={formik}
        inputArray={fields}
        button={{
          text: "Add Tier",
          buttonLoadingText: "Adding tier...",
        }}
      />
    </Box>
  );
};

export default Tiers;
