import React from "react";
import {
  <PERSON>dal,
  ModalOverlay,
  ModalContent,
  ModalCloseButton,
  Box,
  Text,
  Flex,
} from "@chakra-ui/react";
import { CheckIcon } from "@chakra-ui/icons";

interface LeaveRequestSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const LeaveRequestSuccessModal: React.FC<LeaveRequestSuccessModalProps> = ({
  isOpen,
  onClose,
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay bg="blackAlpha.600" />
      <ModalContent
        maxW="400px"
        borderRadius="16px"
        bg="white"
        p={8}
        mx={4}
      >
        {/* Close Button */}
        <ModalCloseButton
          position="absolute"
          top={4}
          right={4}
          color="gray.500"
          _hover={{ color: "gray.700" }}
        />

        {/* Content */}
        <Flex
          direction="column"
          align="center"
          justify="center"
          textAlign="center"
        >
          {/* Blue Checkmark */}
          <Box
            w="60px"
            h="60px"
            borderRadius="50%"
            bg="#0B3178"
            display="flex"
            alignItems="center"
            justifyContent="center"
            mb={6}
          >
            <CheckIcon color="white" boxSize={6} />
          </Box>

          {/* Success Text */}
          <Text
            fontSize="md"
            fontWeight={500}
            color="gray.800"
            textAlign="center"
            fontFamily="Inter"
            lineHeight="1.5"
          >
            Your leave request has been submitted successfully. You'll be notified once a decision is made
          </Text>
        </Flex>
      </ModalContent>
    </Modal>
  );
};

export default LeaveRequestSuccessModal;
