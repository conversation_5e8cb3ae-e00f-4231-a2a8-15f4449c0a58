import {
  Box,
  Drawer<PERSON>ody,
  Drawer<PERSON>lose<PERSON>utton,
  Flex,
  Grid,
  <PERSON>ing,
  <PERSON>b,
  TabList,
  TabPanel,
  TabPanels,
  <PERSON>bs,
  Text,
} from "@chakra-ui/react";
import React from "react";
import { HiOutlineChevronLeft } from "react-icons/hi";
import { useCurrentOrganization } from "../hooks/organization";
import { deepBlue } from "../theme/colors";
import { TObjective, TSpread } from "../types/objectives";
import { TOrganization } from "../types/organization";
import { capitalizeFirst } from "../utils";
import { calculateObjectivePerspectiveTargetPoint } from "../utils/calculateObjectivePerspectiveTargetPoint";
import { formatDate } from "../utils/formatDate";

const ObjectiveDetails = (props: TObjective) => {
  const newTargetPoint = calculateObjectivePerspectiveTargetPoint(
    props.perspectives
  );
  return (
    <DrawerBody w="100%" mx="auto">
      <Flex alignItems="baseline" mb="4" gap="2">
        <DrawerCloseButton
          as={HiOutlineChevronLeft}
          size="sm"
          display="block"
          position="relative"
        />
        <Text fontSize="sm" fontWeight="500">
          Objective Details
        </Text>
      </Flex>
      <Tabs colorScheme="primary">
        <TabList>
          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr={8}>
            Basic Info
          </Tab>
          <Tab fontWeight="500" fontSize="sm" color="gray.600">
            Perspectives
          </Tab>
        </TabList>
        <TabPanels>
          <TabPanel>
            <ObjectiveDetail {...props} targetPoint={newTargetPoint || 0} />
          </TabPanel>
          <TabPanel>
            <ConnectedPerspectives
              perspectives={props.perspectives}
              targetPoint={newTargetPoint || props.targetPoint || 0}
            />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </DrawerBody>
  );
};
export default ObjectiveDetails;

const DetailItem: React.FC<{ label: string; value: string | number }> = ({
  label,
  value,
}) => (
  <Box mb="4">
    <Text fontSize="smaller" color="gray.500">
      {label}
    </Text>
    <Text fontSize="sm">
      {typeof value === "string" ? capitalizeFirst(value) : value}
    </Text>
  </Box>
);

const ObjectiveDetail = ({
  name,
  status,
  afterOccurrence,
  routineType,
  startDate,
  targetPoint,
  endDate,
}: TObjective) => {
  let { data: currentOrganization } = useCurrentOrganization();
  currentOrganization = currentOrganization?.data as TOrganization;
  return (
    <Box>
      <DetailItem label="Name" value={name} />
      <DetailItem label="Objective Status" value={status} />
      <DetailItem label="Routine Option" value={capitalizeFirst(routineType)} />
      <DetailItem label="Routine Round" value={afterOccurrence || 0} />
      <DetailItem label="Target Point" value={targetPoint || 0} />
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <DetailItem
          label="Start Date"
          value={formatDate(
            `${startDate}`,
            currentOrganization?.primaryDateFormat
          )}
        />
        <DetailItem
          label="End Date"
          value={formatDate(
            `${endDate}`,
            currentOrganization.primaryDateFormat
          )}
        />
      </Grid>
    </Box>
  );
};

const ObjectiveOwnerDetails: React.FC<{
  first_name: string;
  last_name: string;
  phone_number: string;
  email: string;
}> = (props) => {
  return (
    <Box>
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <DetailItem label="First Name" value={props.first_name} />
        <DetailItem label="Last Name" value={props.last_name} />
        <DetailItem label="Phone Number" value={props.phone_number} />
        <DetailItem label="Email" value={props.email} />
      </Grid>
    </Box>
  );
};

const ActiveInitiative: React.FC<{
  active_initiatives: any[];
}> = ({ active_initiatives }) => {
  return (
    <>
      {active_initiatives.length === 0 ? (
        <p>There is no Active Initiative</p>
      ) : (
        active_initiatives.map((data) => (
          <Box key={data.name}>
            <Heading as="h5" size="sm" color="primary" mb="4">
              Initiative Information
            </Heading>
            <DetailItem label="Name" value={data.name} />
            <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
              <DetailItem label="Target Point" value={data.target_point} />
              <DetailItem label="Routine Round" value={data.routine_round} />
            </Grid>
          </Box>
        ))
      )}
    </>
  );
};

const ConnectedPerspectives = ({
  perspectives,
  targetPoint,
}: {
  perspectives: TSpread[];
  targetPoint: number;
}) => {
  return (
    <>
      {perspectives.map((data) => (
        <Flex justifyContent="space-between" key={data?.name} mb="4">
          <Text color="gray.500" textTransform="capitalize" fontSize="sm">
            {data?.name}
          </Text>
          <Text as="small" color="gray.900">
            {data?.relativePoint}
          </Text>
        </Flex>
      ))}
      <Box
        style={{
          backgroundColor: deepBlue,
          height: "2px",
          width: "100%",
          margin: ".8rem 0",
          borderRadius: "10px",
        }}
      />
      <Flex justifyContent="space-between">
        <Text fontSize="sm">Target Point</Text>
        <Text as="small" color="gray.900">
          {targetPoint}
        </Text>
      </Flex>
    </>
  );
};
