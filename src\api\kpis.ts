import { TKPI } from "../types/kpis";
import { Status } from "../types/objectives";
import { CrudService } from "./crud";

const kpiCrud = new CrudService<TKPI>("kpis", {
  includeTenantAndUser: false,
});

export const getAllKPIs = async (
  page?: number,
  limit?: number,
  status?: Status
) => {
  return await kpiCrud.getAll(page, limit, status);
};

export const getKPIById = async (id: number) => {
  return await kpiCrud.getById(id);
};

export const createKPI = async (
  kpi: Omit<TKPI, "id">,
  resetForm: () => void
) => {
  return await kpiCrud.create(kpi, resetForm);
};

export const updateKPI = async (
  id: number,
  kpi: Partial<Omit<TKPI, "id">>,
  resetForm?: () => void
) => {
  return await kpiCrud.update(id, kpi, resetForm);
};

export const deleteKPI = async (id: number) => {
  return await kpiCrud.delete(id);
};

export const bulkCreateKPIs = async (formData: FormData) => {
  return await kpiCrud.bulkCreate(formData);
};
