import { Filters } from "../api/crud";
import {
  createTask,
  deleteTask,
  getAllTasks,
  getTaskById,
  getTasksWithFilters,
  updateTask,
} from "../api/tasks";
import {
  useCrudCreate,
  useCrudDelete,
  useCrudQuery,
  useCrudUpdate,
} from "./useCrud";

export const tasksQueryKey = ["tasks"];

/**
 * Fetches paginated list of tasks
 */
export const useGetTasks = (page?: number, limit?: number) =>
  useCrudQuery(tasksQueryKey, () => getAllTasks(page, limit));

/**
 * Fetches paginated list of tasks filtering by certaing params
 */
export const useGetTasksWithFilters = (
  filters: Filters,
  page?: number,
  limit?: number
) =>
  useCrudQuery(tasksQueryKey, () => getTasksWithFilters(filters, page, limit));

/**
 * Fetches a single task by ID
 * @param id task ID to retrieve
 */
export const useGetTaskById = (id: string) =>
  useCrudQuery([...tasksQueryKey, id], () => getTaskById(Number(id)));

/**
 * Hook to create a new task
 * @param onClose callback to run after creation
 */
export const useAddTask = (onClose: () => void) =>
  useCrudCreate(
    tasksQueryKey,
    ({ values, resetForm }) => createTask(values, resetForm),
    onClose
  );

/**
 * Hook to update an existing task by ID
 * @param id task ID to update
 * @param onClose optional callback after update
 */
export const useUpdateTask = (id: number, onClose?: () => void) =>
  useCrudUpdate(
    tasksQueryKey,
    ({ values, resetForm }) => updateTask(id, values, resetForm),
    onClose
  );

/**
 * Hook to delete a task by ID
 */
export const useDeleteTask = () => useCrudDelete(tasksQueryKey, deleteTask);
