import { Button, Tooltip, TooltipProps } from "@chakra-ui/react";
import { ReactNode } from "react";

type CustomTooltipProps = {
  tip?: ReactNode;
  onClick?: () => void;
} & TooltipProps;

export function CustomTooltip({
  tip,
  onClick,
  children,
  ...rest
}: CustomTooltipProps) {
  return (
    <Tooltip
      label={tip}
      bg="white"
      color="black"
      fontSize="xs"
      padding="2"
      borderRadius="md"
      hasArrow
      onClick={onClick}
      {...rest}
    >
      <Button
        variant="unstyled"
        width={rest.w}
        m="0"
        color="gray.600"
        _hover={{ color: "gray.800" }}
        transition="colors 0.35s"
        onClick={onClick}
      >
        {children}
      </Button>
    </Tooltip>
  );
}
