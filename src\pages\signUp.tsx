import { Flex } from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect } from "react";
import { HiOutlineChevronRight } from "react-icons/hi";
import { useNavigate } from "react-router-dom";
import * as Yup from "yup";
import { registerUser } from "../api/authentication";
import SignUpIllustration from "../assets/images/signup-illustration.svg";
import AuthContainer from "../components/authentication/authContainer";
import AuthSideImage from "../components/AuthSideImage";
import { InputField, ReuseableForm } from "../components/custom/form";
import { passwordErrorMessage, passwordRegex } from "../constants";

export interface SignUpInputs {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  password: string;
  confirmPassword: string;
}

const signUpSchema = Yup.object().shape({
  firstName: Yup.string().required("First name is required"),
  lastName: Yup.string().required("Last name is required"),
  phoneNumber: Yup.string()
    .matches(/^\+?[0-9]\d{1,14}$/, "Invalid phone number format")
    .required("Phone number is required"),
  email: Yup.string()
    .email("Invalid email address")
    .required("Email is required"),
  password: Yup.string()
    .min(8, "Password should be at least 8 characters long")
    .matches(passwordRegex, passwordErrorMessage)
    .required("Password is required"),
  confirmPassword: Yup.string()
    .oneOf(
      [Yup.ref("password"), undefined],
      "Password and confirm password must match"
    )
    .required("Confirm Password is required"),
});

const SignUp = () => {
  const navigate = useNavigate();

  const initialValues: SignUpInputs = {
    firstName: localStorage.getItem("firstName") || "",
    lastName: localStorage.getItem("lastName") || "",
    phoneNumber: localStorage.getItem("phoneNumber") || "",
    email: localStorage.getItem("email") || "",
    password: localStorage.getItem("password") || "",
    confirmPassword: localStorage.getItem("confirmPassword") || "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: signUpSchema,
    onSubmit: async (
      { email, firstName, lastName, password, phoneNumber },
      { resetForm }
    ) => {
      const success = await registerUser({
        email,
        password,
        name: `${firstName} ${lastName}`,
        phoneNumber,
        resetForm,
      });

      if (success) {
        navigate("/");
      }
    },
  });

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  const fields: InputField[] = [
    {
      name: "names",
      type: "grid",
      gridCol: 2,
      gridInputs: [
        {
          name: "firstName",
          type: "text",
          label: "First Name",
          placeholder: "e.g Jane",
          validate: true,
        },
        {
          name: "lastName",
          type: "text",
          label: "Last Name",
          placeholder: "e.g Doe",
          validate: true,
        },
      ],
    },
    {
      name: "phoneNumber",
      type: "phone-number",
      label: "Phone Number",
      placeholder: "e.g +2348000000000",
      validate: true,
    },
    {
      name: "email",
      type: "email",
      label: "Email",
      placeholder: "e.g <EMAIL>",
      validate: true,
    },
    {
      name: "password",
      type: "password",
      label: "Password",
      placeholder: "Enter password",
      validate: true,
    },
    {
      name: "confirmPassword",
      type: "password",
      label: "Confirm Password",
      placeholder: "Confirm your password",
      validate: true,
    },
  ];

  return (
    <Flex minH="100vh" w="100vw" overflowY="hidden">
      <AuthSideImage SignUpIllustration={SignUpIllustration} />
      <AuthContainer
        title="Welcome to E-Metric Suite"
        subtitle="Create your account below"
        seoTitle="Create an Organization in E-metric Suite"
        bottomText="Already have an account?"
        bottomLink={{ text: "Login", url: "/" }}
        alignItems="flex-start"
      >
        <ReuseableForm
          formik={formik}
          inputArray={fields}
          button={{
            text: "Register",
            icon: <HiOutlineChevronRight />,
            type: "submit",
            buttonLoadingText: "Registering account...",
          }}
        />
      </AuthContainer>
    </Flex>
  );
};

export default SignUp;
