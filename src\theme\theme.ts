import {
  extendTheme,
  Heading,
  keyframes,
  ThemeOverride,
} from "@chakra-ui/react";
import { blue200, deepBlue } from "./colors";
import { ButtonStyles as Button } from "./customComponents";

const shake = keyframes`
  0% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); }
  75% { transform: translateX(-5px); }
  100% { transform: translateX(0); }
`;

const theme: ThemeOverride = {
  colors: {
    primary: blue200,
    lightblue: "rgba(11, 49, 120, 0.05)",
    secondary: {
      100: "#FAFAFA",
      200: "#F2F2F2",
      500: "#9CA3AF",
      900: "#4B5563",
    },
    gray: {
      100: "#F5F5F5",
      200: "#E5E5E5",
      300: "#D4D4D4",
      400: "#A3A3A3",
      500: "#737373",
      600: "#525252",
      700: "#404040",
      800: "#262626",
      900: "#171717",
    },
  },
  styles: {
    global: {
      body: {
        fontFamily: "Poppins, sans-serif",
        fontWeight: 400,
      },
      heading: {
        fontFamily: "Poppins, sans-serif",
        color: deepBlue,
        fontWeight: 500,
      },
    },
  },
  components: {
    Button,
    Heading,
  },
  animations: {
    shake: `${shake} 0.5s ease-in-out`,
  },
};

const customTheme = extendTheme(theme);
export default customTheme;
