import { UserRole } from "./user";

export interface TPeople {
  id: number;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  personalPhoneNumber: string;
  officialEmail: string;
  personalEmail: string;
  address: string;
  dateOfBirth: string;
  gender: string;
  educationDetailsInstitutions: string;
  educationDetailsYears: string;
  educationDetailsQualifications: string;
  guarantor1IdCard?: string;
  guarantor1Passport?: string;
  guarantor1FirstName: string;
  guarantor1LastName: string;
  guarantor1Address: string;
  guarantor1Occupation: string;
  guarantor1Age: string;
  guarantor2IdCard?: string;
  guarantor2Passport?: string;
  guarantor2FirstName: string;
  guarantor2LastName: string;
  guarantor2Address: string;
  guarantor2Occupation: string;
  guarantor2Age: string;
  description: string;
  structuralLevel: string;
  role: UserRole;
  dateEmployed: string;
  careerLevel: string;
  designationName: string;
  tenantId: string;
  createdBy: string;
}

export interface PeopleAttributes {
  id: number;
  firstName: string;
  lastName: string;
  otherNames?: string;
  gender?: string;
  officialPhoneNumber?: string;
  officialEmail: string;
  personalPhoneNumber: string;
  personalEmail: string;
  address: string;
  dateOfBirth: string;
  educationDetailsInstitutions: string;
  educationDetailsYears: string;
  educationDetailsQualifications: string;
  guarantor1Passport?: string;
  guarantor1IdCard?: string;
  guarantor1FirstName: string;
  guarantor1LastName: string;
  guarantor1Address: string;
  guarantor1Occupation: string;
  guarantor1Age: string;
  guarantor2Passport?: string;
  guarantor2IdCard?: string;
  guarantor2FirstName: string;
  guarantor2LastName: string;
  guarantor2Address: string;
  guarantor2Occupation: string;
  guarantor2Age: string;
  description: string;
  role: string;
  dateEmployed: string;
  corporateName: string;
  divisionName: string;
  groupName: string;
  departmentName: string;
  unitName: string;
  careerLevel: string;
  designationName: string;
  structuralLevel?: string;
  tierName?: string;
  tenantId?: string;
  createdBy?: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string | null;
}
