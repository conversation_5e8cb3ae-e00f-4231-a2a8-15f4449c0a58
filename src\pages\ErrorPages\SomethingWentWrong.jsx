import {
  Alert,
  AlertDescription,
  AlertIcon,
  AlertTitle,
  Flex,
  Text,
} from "@chakra-ui/react";
import { useEffect, useState } from "react";
import { useMediaQuery } from "react-responsive";
import SignUpIllustration from "../../assets/images/signup-illustration.svg";
import AuthSideImage from "../../components/AuthSideImage";
import ErrorLoginButton, {
  RefreshButton,
} from "../../components/ErrorLoginButton";

const SomethingWentWrong = ({ message }) => {
  const [hidden, setHidden] = useState(false);
  const isTabletScreen = useMediaQuery({ maxWidth: 768 });

  useEffect(() => {
    if (isTabletScreen) {
      setHidden(true);
    } else {
      setHidden(false);
    }
  }, [isTabletScreen, hidden]);

  return (
    <Flex minH="100vh" w="100vw" overflowY="hidden">
      <AuthSideImage SignUpIllustration={SignUpIllustration} />
      <Flex h="100vh" w="100%" flex={1} align="center">
        <Alert
          status="error"
          variant="unstyled"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          textAlign="center"
          height="200px"
        >
          <AlertIcon boxSize="40px" mr={0} color="red" />
          <AlertTitle mt={4} mb={1} fontSize="lg" fontWeight="500" color="red">
            {!navigator.onLine
              ? "Network error"
              : " Something Went Wrong" || ""}
          </AlertTitle>
          <AlertDescription maxWidth="sm">
            {/* Click{" "} */}
            <Text color="gray.500" fontSize="small">
              {!navigator.onLine
                ? "Check your internet connections"
                : message ||
                  "Sorry we couldn't display content at the moment, Please report this error to our support teams if it persist"}
            </Text>
            <br />
            <RefreshButton /> <ErrorLoginButton />
          </AlertDescription>
        </Alert>
      </Flex>
    </Flex>
  );
};

export default SomethingWentWrong;
