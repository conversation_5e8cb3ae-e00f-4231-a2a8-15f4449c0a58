import {
  Alert,
  AlertDescription,
  AlertIcon,
  AlertTitle,
  Flex
} from "@chakra-ui/react";
import SignUpIllustration from "../../assets/images/signup-illustration.svg";
import AuthSideImage from "../../components/AuthSideImage";
import ErrorLoginButton, {
  RefreshButton,
} from "../../components/ErrorLoginButton";

const AccessTokenDenied = () => {
  return (
    <Flex minH="100vh" w="100vw" overflowY="hidden" bg="gray.300" gap="4">
      <AuthSideImage SignUpIllustration={SignUpIllustration} />
      <Flex h="100vh" w="100%" flex={1} align="center">
        <Alert
          status="success"
          variant="subtle"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          textAlign="center"
          height="200px"
        >
          <AlertIcon boxSize="40px" mr={0} />
          <AlertTitle mt={4} mb={1} fontSize="lg">
            Your Session has expired please re-login
          </AlertTitle>
          <AlertDescription maxWidth="sm">
            <RefreshButton /> <ErrorLoginButton />
          </AlertDescription>
        </Alert>
      </Flex>
    </Flex>
  );
};

export default AccessTokenDenied;
