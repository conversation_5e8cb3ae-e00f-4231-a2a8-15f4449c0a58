import {
  createCareerPath,
  deleteCareerPath,
  getAllCareerPaths,
  updateCareerPath,
} from "../api/careerpath";
import { TCareerPath } from "../types/careerpath";
import {
  useCrudCreate,
  useCrudDelete,
  useCrudQuery,
  useCrudUpdate,
} from "./useCrud";

export const careerPathsQueryKey = ["careerPaths"];

export const useGetCareerPaths = (page?: number, limit?: number) =>
  useCrudQuery(careerPathsQueryKey, () => getAllCareerPaths(page, limit));

export const useAddCareerPath = (onClose: () => void) =>
  useCrudCreate(
    careerPathsQueryKey,
    ({ values, resetForm }) =>
      createCareerPath(values as Omit<TCareerPath, "id">, resetForm),
    onClose
  );

export const useUpdateCareerPath = (id: number, onClose?: () => void) =>
  useCrudUpdate(
    careerPathsQueryKey,
    ({ values, resetForm }) => updateCareerPath(id, values, resetForm),
    onClose
  );

export const useDeleteCareerPath = () =>
  useCrudDelete(careerPathsQueryKey, deleteCareerPath);


