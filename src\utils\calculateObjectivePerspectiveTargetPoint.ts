import { TKPI } from "../types/kpis";
import { TObjective, TSpread } from "../types/objectives";

export const calculateObjectivePerspectiveTargetPoint = (
  perspectives: TSpread[],
  targetPoint?: number
) => {
  if (targetPoint) {
    return targetPoint;
  }
  const newTargetPoint = (perspectives || []).reduce(
    (sum, p) => sum + (p?.relativePoint || 0),
    0
  );

  return newTargetPoint;
};

export const calculateObjectiveTargetPointRemainder = (
  kpis: TKPI[],
  allPossibleObjective: TObjective,
  targetPoint: number = 0
) => {
  console.log(allPossibleObjective);

  kpis.filter((kpi) => kpi.uplineObjective.length > 0);
  const uplineObjective = kpis.reduce(
    (sum, kpi) => sum + kpi.uplineObjective[0].relativePoint,
    0
  );
  return targetPoint - uplineObjective;
};

export const calculatePerspectiveTargetPoint = (
  objectives: TObjective[],
  perspectiveId: number,
  targetPoint?: number
) => {
  if (targetPoint) {
    return targetPoint;
  }

  let total = 0;

  for (const obj of objectives) {
    const matched = obj.perspectives?.find(
      (p: any) => Number(p.id) === Number(perspectiveId)
    );
    if (matched) {
      total += matched.relativePoint || 0;
    }
  }

  return total;
};
