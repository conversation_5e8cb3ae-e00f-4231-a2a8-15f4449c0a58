import { Status, TObjective } from "../types/objectives";
import { CrudService } from "./crud";

const objectiveCrud = new CrudService<TObjective>("objectives");

export const getAllObjectives = async (
  page?: number,
  limit?: number,
  status?: Status
) => {
  return await objectiveCrud.getAll(page, limit, status);
};

export const getObjectiveById = async (id: number) => {
  return await objectiveCrud.getById(id);
};

export const createObjective = async (
  objective: Omit<TObjective, "id">,
  resetForm: () => void
) => {
  return await objectiveCrud.create(objective, resetForm);
};

export const updateObjective = async (
  id: number,
  objective: Partial<Omit<TObjective, "id">>,
  resetForm?: () => void
) => {
  return await objectiveCrud.update(id, objective, resetForm);
};

export const deleteObjective = async (id: number) => {
  return await objectiveCrud.delete(id);
};

export const bulkCreateObjectives = async (formData: FormData) => {
  return await objectiveCrud.bulkCreate(formData);
};
