import { CrudService } from "./crud";

const perspectiveCrud = new CrudService("perspectives", {
  includeTenantAndUser: true,
});

export const getAllPerspectives = async (page?: number, limit?: number) => {
  return await perspectiveCrud.getAll(page, limit);
};

export const getPerspectiveById = async (id: number) => {
  return await perspectiveCrud.getById(id);
};

export const createPerspective = async (
  name: string,
  resetForm: () => void
) => {
  return await perspectiveCrud.create({ name: name.toLowerCase() }, resetForm);
};

export const updatePerspective = async (
  id: number,
  name: string,
  resetForm?: () => void
) => {
  return await perspectiveCrud.update(
    id,
    { name: name.toLowerCase() },
    resetForm
  );
};

export const deletePerspective = async (id: number) => {
  return await perspectiveCrud.delete(id);
};

export const bulkCreatePerspectives = async (formData: FormData) => {
  return await perspectiveCrud.bulkCreate(formData);
};
