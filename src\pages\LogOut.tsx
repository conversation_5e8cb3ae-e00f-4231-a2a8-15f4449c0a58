import {
  Alert,
  AlertDescription,
  AlertIcon,
  Alert<PERSON><PERSON>le,
  Button,
  Flex,
} from "@chakra-ui/react";
import SignUpIllustration from "../assets/images/signup-illustration.svg";
import AuthSideImage from "../components/AuthSideImage";

const Logout = () => {
  return (
    <Flex minH="100vh" w="100vw" overflowY="hidden">
      <AuthSideImage SignUpIllustration={SignUpIllustration} />
      <Flex h="100vh" w="100%" bg="gray.300" flex={1} align="center">
        <Alert
          status="success"
          variant="subtle"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          textAlign="center"
          height="200px"
        >
          <AlertIcon boxSize="40px" mr={0} />
          <AlertTitle mt={4} mb={1} fontSize="lg">
            Thank you! we would love to see you again
          </AlertTitle>
          <AlertDescription maxWidth="sm">
            Click{" "}
            <a href="/">
              <Button variant="primary">here</Button>
            </a>{" "}
            {/* to proceed to login */}
          </AlertDescription>
        </Alert>
      </Flex>
    </Flex>
  );
};

export default Logout;
