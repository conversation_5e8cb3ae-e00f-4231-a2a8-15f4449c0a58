import { TStructuralLevel } from "../types/structuralLevel";
import { CrudService } from "./crud";

const structuralLevelCrud = new CrudService<TStructuralLevel>(
  "structural-level",
  { includeTenantAndUser: false }
);

export const getAllStructuralLevels = async (page?: number, limit?: number) => {
  return await structuralLevelCrud.getAll(page, limit);
};

export const getStructuralLevelById = async (id: number, name?: string) => {
  return await structuralLevelCrud.getById(id, name);
};

export const createStructuralLevel = async (
  structuralLevel: Omit<TStructuralLevel, "id">,
  resetForm?: () => void
) => {
  return await structuralLevelCrud.create(structuralLevel, resetForm);
};

export const updateStructuralLevel = async (
  id: number,
  structuralLevel: Partial<Omit<TStructuralLevel, "id">>,
  resetForm?: () => void
) => {
  return await structuralLevelCrud.update(id, structuralLevel, resetForm);
};

export const deleteStructuralLevel = async (id: number) => {
  return await structuralLevelCrud.delete(id);
};

export const bulkCreateStructuralLevels = async (formData: FormData) => {
  return await structuralLevelCrud.bulkCreate(formData);
};
