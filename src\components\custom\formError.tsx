import { Box } from "@chakra-ui/react";
import { FormikErrors, FormikTouched } from "formik";
import { motion } from "framer-motion";
import Markdown from "react-markdown";
import { errorColor } from "../../theme/colors";
import { capitalizeFirst } from "../../utils";

interface FormikErrorResponseProps {
  touched: FormikTouched<Record<string, any>>;
  errors: FormikErrors<Record<string, any>>;
  name: string;
  validate?: boolean;
}

export default function FormikErrorResponse({
  touched,
  errors,
  name,
  validate = false,
}: FormikErrorResponseProps) {
  return (
    <>
      {touched[name] && (
        <>
          <motion.div
            key={String(errors[name]) || name}
            initial={{ opacity: 0, y: -8 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Box
              fontSize="xs"
              mt="0.5"
              mx="0.5"
              color={
                errors[name] ? errorColor : validate ? "green.800" : undefined
              }
            >
              {/* {validate && !errors[name] && (
                <Flex align="center" gap="0.5">
                  <CheckCircleIcon boxSize="2.5" />
                  <Text>You are good to go</Text>
                </Flex>
              )} */}
              <Markdown
                components={{
                  a: ({ node, ...props }) => (
                    <a
                      {...props}
                      style={{
                        color: "blue",
                        textDecoration: "underline",
                      }}
                    />
                  ),
                }}
              >
                {Array.isArray(errors[name]) && errors[name].length > 0
                  ? capitalizeFirst(`${Object.values(errors[name][0])[0]}`)
                  : typeof errors[name] === "string"
                  ? capitalizeFirst(errors[name])
                  : undefined}
              </Markdown>
            </Box>
          </motion.div>
        </>
      )}
    </>
  );
}
