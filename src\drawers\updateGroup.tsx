import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  useToast,
} from "@chakra-ui/react";
import { Field, Form, Formik, FormikHelpers } from "formik";
import { useErrorBoundary } from "react-error-boundary";
import * as yup from "yup";
import InputWithLabel from "../components/InputWithLabel";

interface UpdateGroupType {
  name: string;
  uuid: string;
}

const schema = yup.object().shape({
  name: yup.string().required("Name is required"),
});

const UpdateGroup = (props: UpdateGroupType) => {
  const ORG_NAME = localStorage.getItem("current_organization_short_name");
  const { showBoundary } = useErrorBoundary();
  const toast = useToast();

  const onSubmit = (
    values: { name: string },
    actions: FormikHelpers<{ name: string }>
  ) => {
    if (ORG_NAME) {
      console.log("org name");
      // Handle your submission logic here
    }
    actions.setSubmitting(false);
  };

  return (
    <Formik
      initialValues={{ name: props.name }}
      validationSchema={schema}
      onSubmit={onSubmit}
      enableReinitialize
    >
      {({ errors, touched, isSubmitting }) => (
        <>
          <DrawerCloseButton />
          <DrawerHeader fontWeight="500" fontSize="md">
            Update Group
          </DrawerHeader>
          <DrawerBody>
            <Form id="add-perspective-form">
              <Field
                name="name"
                as={InputWithLabel}
                id="name"
                label="Name of Corporate"
                variant="filled"
                bg="secondary.200"
                mb="5"
                formErrorMessage={errors.name}
                isInvalid={touched.name && !!errors.name}
              />
            </Form>
          </DrawerBody>
          <DrawerFooter>
            <Button
              type="submit"
              form="add-perspective-form"
              variant="primary"
              w="full"
              size="sm"
              isLoading={isSubmitting}
              loadingText="Updating..."
            >
              Update
            </Button>
          </DrawerFooter>
        </>
      )}
    </Formik>
  );
};

export default UpdateGroup;