import {
  Box,
  Button,
  Flex,
  Stack,
  Td,
  Text,
  useBreakpointValue,
  useDisclosure,
} from "@chakra-ui/react";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import React, { ReactNode, useState } from "react";
import { RiDeleteBinLine } from "react-icons/ri";
import CustomDeleteModal from "./custom/DeleteModal";
import DataTable from "./custom/Table";

const MotionBox = motion.create(Box);
const MotionFlex = motion.create(Flex);
const MotionTd = motion.create(Td);

interface CrudUIProps<T> {
  title: string;
  queryKey: string;
  queryFn: (page: number) => Promise<any>;
  columns: string[];
  renderRow: (item: T) => ReactNode;
  actions?: ReactNode;
  singleRecordField?: string;
  preTableComp?: React.ReactNode; // For adding additional components after initial buttons but before table component
}

export default function CrudUI<T>({
  title,
  queryKey,
  queryFn,
  columns,
  renderRow,
  actions,
  singleRecordField,
  preTableComp,
}: CrudUIProps<T>) {
  const [pageNum, setPageNum] = useState(1);

  const { data, isLoading } = useQuery({
    queryKey: [queryKey],
    queryFn: () => queryFn(pageNum),
    enabled: !!pageNum,
  });

  const items =
    (singleRecordField ? data?.data?.[singleRecordField] : data?.data?.data) ||
    [];
  const count = (singleRecordField ? items?.length : data?.data?.count) || 0;
  const page = data?.data?.page || pageNum;
  const hasNextPage = Boolean(data?.data?.nextPage);
  const hasPreviousPage = Boolean(data?.data?.previousPage);

  // if (!data?.data) return <PageNotFound />;

  return (
    <Box overflow="hidden">
      <Flex
        direction={{ base: "column", md: "row" }}
        justifyContent={{
          base: "center",
          md: actions ? "space-between" : "center",
        }}
        alignItems="center"
        gap="4"
        mb="4"
      >
        {items?.length > 0 ? (
          <Text as="small" flexShrink="0">
            Showing {items.length} of {count} {title}
          </Text>
        ) : (
          <Box></Box>
        )}

        {actions && (
          <Stack
            direction={{ base: "column", md: "row" }}
            w={{ base: "full", md: "fit-content" }}
            spacing={4}
          >
            {actions}
          </Stack>
        )}
      </Flex>

      {preTableComp && preTableComp}

      <DataTable
        headers={columns}
        data={items}
        isLoading={isLoading}
        pageNum={page}
        hasNextPage={hasNextPage}
        hasPreviousPage={hasPreviousPage}
        onPageChange={setPageNum}
        renderRow={(item: T) => renderRow(item)}
      />
    </Box>
  );
}

export const ActionButton = ({
  icon,
  colorScheme,
  onClick,
}: {
  icon: React.ReactNode;
  colorScheme: string;
  onClick: () => void;
}) => (
  <Button
    colorScheme={colorScheme}
    color={colorScheme}
    variant={"outline"}
    size="sm"
    onClick={onClick}
  >
    {icon}
  </Button>
);

interface CrudRowProps {
  item: any;
  itemName: string;
  fields: { label: string; value: React.ReactNode }[];
  tableDataFields: string[];
  renderDrawer?: React.ReactNode;
  onDelete: (id: number) => void;
}

export function CrudRow({
  item,
  itemName,
  fields,
  renderDrawer,
  onDelete,
  tableDataFields,
}: CrudRowProps) {
  const isMobile = useBreakpointValue({ base: true, md: false });

  const { isOpen, onClose, onOpen } = useDisclosure();

  const remove = () => {
    onClose();
    onDelete(item?.id as number);
  };

  return (
    <>
      <CustomDeleteModal
        isModalOpen={isOpen}
        closeModal={onClose}
        confirmDelete={remove}
        text={item?.name || itemName}
      />

      {isMobile ? (
        <MotionBox
          borderRadius="md"
          bg="white"
          borderColor="gray.50"
          p={4}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.35, ease: "easeIn" }}
          viewport={{ once: true }}
        >
          {fields.map(({ label, value }, i) => (
            <MotionFlex
              justify="space-between"
              gap="4"
              mb="3"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, ease: "easeIn", delay: i * 0.08 }}
              viewport={{ once: true }}
              key={i}
            >
              <Text fontSize="small" color="gray" flexShrink="0">
                {label}:
              </Text>
              <Text fontSize="small" textAlign="right">
                {value}
              </Text>
            </MotionFlex>
          ))}

          {renderDrawer && (
            <Flex justify="flex-end" gap={2} mt={6} alignItems="center">
              {renderDrawer}
              <ActionButton
                icon={<RiDeleteBinLine />}
                colorScheme="red"
                onClick={onOpen}
              />
            </Flex>
          )}
        </MotionBox>
      ) : (
        <>
          {tableDataFields.map((data, index) => (
            <MotionTd
              key={index}
              fontSize="xs"
              fontWeight="300"
              color="gray.700"
              maxW="300px"
              whiteSpace="normal"
              isTruncated
              wordBreak="keep-all"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.08 }}
            >
              {data}
            </MotionTd>
          ))}
          {renderDrawer && (
            <MotionTd
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2 }}
            >
              <Flex gap="4" alignItems="center">
                {renderDrawer}
                <ActionButton
                  icon={<RiDeleteBinLine />}
                  colorScheme="red"
                  onClick={onOpen}
                />
              </Flex>
            </MotionTd>
          )}
        </>
      )}
    </>
  );
}
