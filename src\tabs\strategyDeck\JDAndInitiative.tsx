import {
  Box,
  Button,
  Checkbox,
  Flex,
  Stack,
  Td,
  Text,
  Tr,
} from "@chakra-ui/react";
import React, { useEffect, useLayoutEffect, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import { AiOutlineEdit } from "react-icons/ai";
import { BsFillTrash2Fill } from "react-icons/bs";
import { HiOutlinePlus } from "react-icons/hi";
import { RiDeleteBinLine } from "react-icons/ri";
import { toast } from "sonner";
import axiosInstance from "../../api";
import CustomTab from "../../components/custom/CustomTab";
import DataTable from "../../components/custom/Table";
import PaginatedItems from "../../components/Pagination/Pagination";
import "../../components/removeChakraTabelPadding.css";
import AddKPI from "../../drawers/AddKPI";
import CustomDrawer from "../../drawers/CustomDrawer";
import { JdDetailDrawer } from "../../drawers/JdDetailDrawer";
import UpdateKPI from "../../drawers/UpdateKPI";
import UploadInitiative from "../../drawers/UploadInitiative";
import {
  createDownloadableFile,
  setTimeFilter,
} from "../../services/extraFunctions";

interface ownerObjectType {
  full_name: string;
  email: string;
}

interface ownersOfInitiativeStateInterface {
  list_of_owners: ownerObjectType[];
  current_owner: string;
  ShouldFilter: boolean;
}

const JDAndInitiativesTab: React.FC<{
  isLoading: boolean;
  initiatives: any;
  ownersOfInitiative: ownersOfInitiativeStateInterface;
  status: string;
  StatusTabIndex: number;
  pageNum: number;
  setStatusTabIndex: (arg: any) => void;
  isInInitiativePage: boolean;
  setPageNum: (value: number) => void;
}> = ({
  isLoading,
  initiatives,
  ownersOfInitiative,
  status,
  setStatusTabIndex,
  StatusTabIndex,
  isInInitiativePage,
  pageNum,
  setPageNum,
}) => {
  useEffect(() => {
    const KPISetStatusTabIndex = localStorage.getItem("KPISetStatusTabIndex");
    if (KPISetStatusTabIndex) {
      setStatusTabIndex(JSON.parse(KPISetStatusTabIndex));
    }
  }, []);

  return (
    <CustomTab
      tabList={
        [{ label: "Pending" }, { label: "Active" }, { label: "Closed" }] as any
      }
      tabPanels={
        [
          () => (
            <JDAInitiativesTable
              isLoading={isLoading}
              initiatives={initiatives}
              ownersOfInitiative={ownersOfInitiative}
              currentTabNumber={StatusTabIndex}
              status={status}
              pageNum={pageNum}
              isInInitiativePage={isInInitiativePage}
              setPageNum={setPageNum}
            />
          ),
          () => (
            <JDAInitiativesTable
              setPageNum={setPageNum}
              isLoading={isLoading}
              initiatives={initiatives}
              ownersOfInitiative={ownersOfInitiative}
              currentTabNumber={StatusTabIndex}
              status={status}
              pageNum={pageNum}
              isInInitiativePage={isInInitiativePage}
            />
          ),
          () => (
            <JDAInitiativesTable
              pageNum={pageNum}
              setPageNum={setPageNum}
              isLoading={isLoading}
              initiatives={initiatives}
              ownersOfInitiative={ownersOfInitiative}
              currentTabNumber={StatusTabIndex}
              status={status}
              isInInitiativePage={isInInitiativePage}
            />
          ),
        ] as any
      }
      tabId="KPISetStatusTabIndex"
      variant="enclosed"
    />
  );
};

const JDAInitiativesTable = ({
  currentTabNumber,
  isLoading,
  initiatives,
  ownersOfInitiative,
  pageNum,
  status,
  isInInitiativePage = true,
  setPageNum,
}: {
  currentTabNumber: number;
  pageNum: number;
  isLoading: boolean;
  initiatives: any;
  ownersOfInitiative: ownersOfInitiativeStateInterface;
  status: string;
  isInInitiativePage: boolean;
  setPageNum: (x: number) => void;
}) => {
  const { showBoundary } = useErrorBoundary();
  const org_name = localStorage.getItem("current_organization_short_name");

  const handleDelete = (item: any) => {
    if (!org_name) return;
    if (window.confirm("Are Sure You Want to Delete")) {
      const reOccurring =
        item.routine_option !== "once" &&
        window.confirm(
          "Do you only want to delete this item and following re-occurring item "
        );
    }
  };

  const tableHeaders = [
    "",
    "KPI Name",
    "TaT(EndTime)",
    "Target Point",
    "Qly Target Brief",
    "Routine",
    "",
    "Update",
    "Deleted",
  ];

  return (
    <DataTable
      headers={tableHeaders}
      data={initiatives}
      isLoading={isLoading}
      onPageChange={(page) => {
        setPageNum(page);
      }}
      pageNum={pageNum}
      hasNextPage={false}
      hasPreviousPage={false}
      renderRow={(item: any) => (
        <Tr key={item.initiative_id} style={{ textTransform: "capitalize" }}>
          <Td>
            <Checkbox
              disabled={status === "closed"}
              isChecked={false}
              onChange={() => console.log("")}
            />
          </Td>
          <Td fontSize="xs" style={{ width: "100%" }}>
            <Text mb="2" style={{ width: "100%" }}>
              {item.name}
            </Text>
          </Td>
          <Td fontSize="xs" style={{ textAlign: "center" }}>
            <Text>{item.end_date}</Text>
          </Td>
          <Td style={{ textAlign: "center" }}>{item.target_point}</Td>
          <Td fontSize="xs">
            <a
              href={createDownloadableFile(item.initiative_brief)}
              style={{
                color: item.initiative_brief ? "blue" : "gray",
                cursor: "pointer",
              }}
              rel="nofollow noreferrer"
              download
            >
              Download Brief
            </a>
          </Td>
          <Td fontSize="xs" style={{ textAlign: "center" }}>
            {item.routine_round}
          </Td>
          <Td>
            <CustomDrawer showModalBtnText="View" drawerSize="md">
              <JdDetailDrawer {...item} />
            </CustomDrawer>
          </Td>
          <Td>
            {item.initiative_status !== "closed" ? (
              <CustomDrawer
                showModalBtnText=""
                leftIcon={<AiOutlineEdit />}
                drawerSize="md"
              >
                <UpdateKPI {...item} />
              </CustomDrawer>
            ) : (
              <Button isDisabled={false}>Edit</Button>
            )}
          </Td>
          <Td>
            <Button
              leftIcon={<RiDeleteBinLine />}
              loadingText="deleting"
              isLoading={status === "deleting"}
              disabled={!isInInitiativePage}
              onClick={() => handleDelete(item)}
            />
          </Td>
        </Tr>
      )}
    />
  );
};

type JDAndInitiativesPropType = {
  isInInitiativePage?: boolean;
  team_lead_lookUp?: string;
  team_info?: {
    level_name: string;
    level_id: string;
  };
};

const JDAndInitiatives = ({
  isInInitiativePage = true,
  team_lead_lookUp = "",
  team_info,
}: JDAndInitiativesPropType) => {
  const [pageNum, setPageNum] = useState(1);
  const [nextPage, setNextPage] = useState<string | null | undefined>(null);
  const [previousPage, setPreviousPage] = useState<string | null | undefined>(
    null
  );
  const [NumOfData, setNumOfData] = useState<number | undefined>(0);
  const [initiatives, setInitiative] = useState<any[]>([]);
  const [num_of_pages, setNum_of_pages] = useState(0);
  const { showBoundary } = useErrorBoundary();
  const [start_date_before__Future, setStart_date_before__Future] =
    useState<string>("");
  const [start_date_after__Future, setStart_date_after__Future] =
    useState<string>("");
  const [start_date_before__Past, setStart_date_before__Past] =
    useState<string>("");
  const [start_date_after__Past, setStart_date_after__Past] =
    useState<string>("");
  const [StatusTabIndex, setStatusTabIndex] = useState<number>(1);
  const ORG_NAME = localStorage.getItem("current_organization_short_name");
  const [isLoading, setIsLoading] = useState(false);
  const [ownersOfInitiative, setOwnersOfInitiative] =
    useState<ownersOfInitiativeStateInterface>({
      list_of_owners: [],
      current_owner: "",
      ShouldFilter: false,
    });

  const getInitiatives = async (pageNumber = 1, statusNum: number = 0) => {
    const InitiativeStatus: any = {
      0: "pending",
      1: "active",
      2: "closed",
    };

    try {
      setIsLoading(true);
      let url = `/client/${ORG_NAME}/initiative/?initiative_status=${InitiativeStatus[statusNum]}${team_lead_lookUp}&page=${pageNumber}`;
      if (InitiativeStatus[statusNum] === "closed") {
        url += `&start_date_before=${start_date_before__Past}&start_date_after=${start_date_after__Past}`;
      } else if (InitiativeStatus[statusNum] === "pending") {
        url += `&start_date_before=${start_date_before__Future}&start_date_after=${start_date_after__Future}`;
      }
      const response = await axiosInstance.get(url);
      setInitiative(response.data.data);
      setNum_of_pages(response.data.page_count);
      setNumOfData(response.data.count);
      setNextPage(response.data.next);
      setPreviousPage(response.data.previous);
      setIsLoading(false);
    } catch (err: any) {
      if (err?.response?.status == 401) {
        showBoundary(err);
      }
      console.log(err);
      setInitiative([]);
      setIsLoading(false);
    }
  };

  const handleBulkDelete = () => {
    if ([].length === 0) {
      toast.error("Please at least select one Initiative");
      return;
    }
    if (ORG_NAME) {
      console.log("org name");
    }
  };

  useLayoutEffect(() => {
    setTimeFilter({
      setStart_date_after: setStart_date_after__Future,
      setStart_date_before: setStart_date_before__Future,
      timeType: "future",
    });

    setTimeFilter({
      setStart_date_after: setStart_date_after__Past,
      setStart_date_before: setStart_date_before__Past,
      timeType: "past",
    });
  }, []);

  useEffect(() => {
    if (status === "updated" || status === "deleted") {
      getInitiatives();
      toast.success(
        `${status.charAt(0).toUpperCase() + status.slice(1)} Successfully`
      );
    }
  }, [status]);

  useEffect(() => {
    if (
      start_date_before__Future &&
      start_date_after__Future &&
      start_date_before__Past &&
      start_date_after__Past &&
      ORG_NAME
    ) {
      getInitiatives(pageNum, StatusTabIndex);
    }
  }, [
    start_date_before__Future,
    start_date_after__Future,
    start_date_before__Past,
    start_date_after__Past,
    pageNum,
    StatusTabIndex,
  ]);

  return (
    <Box>
      <Flex
        direction={{ base: "column", md: "row" }}
        justifyContent={{ base: "center", md: "space-between" }}
        alignItems="center"
        gap="4"
        mb="4"
      >
        {initiatives.length > 0 && (
          <Text
            as="small"
            display="inline-block"
            fontWeight="500"
            flexShrink={0}
          >
            Showing {initiatives.length} of {NumOfData} KPI
          </Text>
        )}

        {isInInitiativePage && (
          <Stack
            direction={{ base: "column", md: "row" }}
            w={{ base: "full", md: "fit-content" }}
            spacing={4}
          >
            <CustomDrawer
              showModalBtnText="Add KPI"
              showModalBtnVariant="primary"
              showModalBtnColor="white"
              leftIcon={<HiOutlinePlus />}
              drawerSize="md"
            >
              <AddKPI />
            </CustomDrawer>
            <CustomDrawer
              showModalBtnText="Upload KPI"
              showModalBtnVariant="outline"
              showModalBtnColor="primary"
              leftIcon={<HiOutlinePlus />}
              drawerSize="md"
            >
              <UploadInitiative />
            </CustomDrawer>
            {initiatives.length > 0 && (
              <Button
                leftIcon={<BsFillTrash2Fill />}
                color="white"
                backgroundColor="red.600"
                mr="2"
                size="sm"
                fontWeight="500"
                onClick={handleBulkDelete}
              >
                Delete Selected
              </Button>
            )}
          </Stack>
        )}
      </Flex>

      <CustomTab
        tabList={
          [
            { label: "Day" },
            { label: "Week" },
            { label: "Month" },
            { label: "Quarter" },
            { label: "Bi-Annual" },
            { label: "Annual" },
          ] as any
        }
        tabPanels={
          [
            () => (
              <JDAndInitiativesTab
                isLoading={isLoading}
                initiatives={initiatives}
                ownersOfInitiative={ownersOfInitiative}
                status={status}
                StatusTabIndex={StatusTabIndex}
                pageNum={pageNum}
                setStatusTabIndex={setStatusTabIndex}
                isInInitiativePage={isInInitiativePage}
                setPageNum={setPageNum}
              />
            ),
            () => (
              <JDAndInitiativesTab
                isLoading={isLoading}
                initiatives={initiatives}
                ownersOfInitiative={ownersOfInitiative}
                status={status}
                StatusTabIndex={StatusTabIndex}
                setStatusTabIndex={setStatusTabIndex}
                pageNum={pageNum}
                isInInitiativePage={isInInitiativePage}
                setPageNum={setPageNum}
              />
            ),
            () => (
              <JDAndInitiativesTab
                pageNum={pageNum}
                isLoading={isLoading}
                initiatives={initiatives}
                ownersOfInitiative={ownersOfInitiative}
                status={status}
                StatusTabIndex={StatusTabIndex}
                setStatusTabIndex={setStatusTabIndex}
                isInInitiativePage={isInInitiativePage}
                setPageNum={setPageNum}
              />
            ),
            () => (
              <JDAndInitiativesTab
                isLoading={isLoading}
                initiatives={initiatives}
                pageNum={pageNum}
                ownersOfInitiative={ownersOfInitiative}
                status={status}
                StatusTabIndex={StatusTabIndex}
                setStatusTabIndex={setStatusTabIndex}
                isInInitiativePage={isInInitiativePage}
                setPageNum={setPageNum}
              />
            ),

            () => (
              <JDAndInitiativesTab
                isLoading={isLoading}
                initiatives={initiatives}
                pageNum={pageNum}
                ownersOfInitiative={ownersOfInitiative}
                status={status}
                StatusTabIndex={StatusTabIndex}
                setStatusTabIndex={setStatusTabIndex}
                isInInitiativePage={isInInitiativePage}
                setPageNum={setPageNum}
              />
            ),
            () => (
              <JDAndInitiativesTab
                isLoading={isLoading}
                pageNum={pageNum}
                initiatives={initiatives}
                ownersOfInitiative={ownersOfInitiative}
                status={status}
                StatusTabIndex={StatusTabIndex}
                setStatusTabIndex={setStatusTabIndex}
                isInInitiativePage={isInInitiativePage}
                setPageNum={setPageNum}
              />
            ),
          ] as any
        }
        tabId="TimeFilterTabIndex"
        variant="enclosed"
      />

      <PaginatedItems
        pageCount={num_of_pages}
        onPageClick={(pageNumberClicked) => setPageNum(pageNumberClicked)}
      />
    </Box>
  );
};

export default JDAndInitiatives;
