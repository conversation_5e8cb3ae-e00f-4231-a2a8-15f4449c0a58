import {
  Box,
  Drawer<PERSON>ody,
  <PERSON>er<PERSON><PERSON><PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lex,
  <PERSON>rid,
  <PERSON><PERSON>,
  <PERSON>b,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
} from "@chakra-ui/react";
import { HiOutlineChevronLeft } from "react-icons/hi";
import { useCurrentOrganization } from "../hooks/organization";
import { TOrganization } from "../types/organization";
import { PeopleAttributes } from "../types/people";
import { capitalizeFirst } from "../utils";
import { formatDate } from "../utils/formatDate";

const EmployeeDetails = (props: PeopleAttributes) => {
  return (
    <>
      <DrawerHeader fontWeight="500" w="95%" mx="auto" mt="5">
        <Flex justify="space-between" alignItems="center">
          <Flex alignItems="baseline">
            <DrawerCloseButton
              as={HiOutlineChevronLeft}
              display="block"
              position="relative"
            />
            <Text mt={-1} ml={5} fontSize="lg">
              People Details
            </Text>
          </Flex>
        </Flex>
      </DrawerHeader>
      <DrawerBody w="100%" mx="auto">
        <Tabs colorScheme="primary">
          <TabList>
            <Tab fontWeight="500" color="gray.600" mr={8}>
              Basic Information
            </Tab>
            <Tab fontWeight="500" color="gray.600">
              Contact Information
            </Tab>
            <Tab fontWeight="500" color="gray.600">
              Employment Details
            </Tab>
          </TabList>
          <TabPanels>
            <TabPanel>
              <EmployeeBasicInfo {...props} />
            </TabPanel>
            <TabPanel>
              <EmployeeContactInfo {...props} />
            </TabPanel>
            <TabPanel>
              <EmployeeWorkInfo {...props} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </DrawerBody>
    </>
  );
};

export default EmployeeDetails;

const DetailItem: React.FC<{ label: string; value: string | number }> = ({
  label,
  value,
}) => (
  <Box mb="4">
    <Text fontSize="smaller" color="gray.500">
      {label}
    </Text>
    <Text>{value || "N/A"}</Text>
  </Box>
);

const EmployeeBasicInfo = (props: PeopleAttributes) => {
  let { data: currentOrganization } = useCurrentOrganization();
  currentOrganization = currentOrganization?.data as TOrganization;

  return (
    <Box>
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <DetailItem label="First Name" value={props.firstName} />
        <DetailItem label="Last Name" value={props.lastName} />
        <DetailItem label="Other Names" value={props?.otherNames || "N/A"} />
        <DetailItem label="Gender" value={props?.gender || "N/A"} />
        <DetailItem
          label="Date of Birth"
          value={formatDate(
            props.dateOfBirth,
            currentOrganization?.primaryDateFormat
          )}
        />
        <DetailItem label="Role" value={capitalizeFirst(props.role)} />
      </Grid>

      <Heading as="h5" size="sm" color="primary" mb="4">
        Education Details
      </Heading>
      {props.educationDetailsInstitutions
        .split(",")
        .map((institution, index) => (
          <Box key={index} mb="4">
            <Text fontSize="smaller" color="gray.500">
              Institution
            </Text>
            <Text>{institution.trim()}</Text>
            <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mt="2">
              <Box>
                <Text fontSize="smaller" color="gray.500">
                  Year
                </Text>
                <Text>
                  {props.educationDetailsYears.split(",")[index]?.trim() ||
                    "N/A"}
                </Text>
              </Box>
              <Box>
                <Text fontSize="smaller" color="gray.500">
                  Qualification
                </Text>
                <Text>
                  {props.educationDetailsQualifications
                    .split(",")
                    [index]?.trim() || "N/A"}
                </Text>
              </Box>
            </Grid>
          </Box>
        ))}
    </Box>
  );
};

const EmployeeContactInfo = (props: PeopleAttributes) => {
  return (
    <Box>
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <DetailItem label="Personal Email" value={props.personalEmail} />
        <DetailItem label="Official Email" value={props.officialEmail} />
        <DetailItem
          label="Official Phone Number"
          value={props?.officialPhoneNumber || "N/A"}
        />
        <DetailItem label="Personal Phone" value={props.personalPhoneNumber} />
        <DetailItem label="Address" value={props.address} />
      </Grid>

      <Heading as="h5" size="sm" color="primary" mb="4">
        Guarantors
      </Heading>
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <DetailItem
          label="Guarantor 1"
          value={`${props.guarantor1FirstName} ${props.guarantor1LastName}`}
        />
        <DetailItem
          label="Guarantor 1 Occupation"
          value={props.guarantor1Occupation}
        />
        <DetailItem label="Guarantor 1 Age" value={props.guarantor1Age} />
        <DetailItem
          label="Guarantor 1 Address"
          value={props.guarantor1Address}
        />
      </Grid>
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <DetailItem
          label="Guarantor 2"
          value={`${props.guarantor2FirstName} ${props.guarantor2LastName}`}
        />
        <DetailItem
          label="Guarantor 2 Occupation"
          value={props.guarantor2Occupation}
        />
        <DetailItem label="Guarantor 2 Age" value={props.guarantor2Age} />
        <DetailItem
          label="Guarantor 2 Address"
          value={props.guarantor2Address}
        />
      </Grid>
    </Box>
  );
};

const EmployeeWorkInfo = (props: PeopleAttributes) => {
  let { data: currentOrganization } = useCurrentOrganization();
  currentOrganization = currentOrganization?.data as TOrganization;

  return (
    <Box>
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <DetailItem
          label="Date Employed"
          value={formatDate(
            props.dateEmployed,
            currentOrganization?.primaryDateFormat
          )}
        />
        <DetailItem label="Designation" value={props.designationName} />
        {/* <DetailItem label="Corporate" value={props.corporateName} />
        <DetailItem label="Division" value={props.divisionName} />
        <DetailItem label="Department" value={props.departmentName} />
        <DetailItem label="Unit" value={props.unitName} />
        <DetailItem label="Group" value={props.groupName} /> */}
        <DetailItem label="Career Grade" value={props.careerLevel} />
      </Grid>
      <DetailItem label="Description" value={props.description} />
    </Box>
  );
};
