import { jwtDecode } from "jwt-decode";
import TokenService from "../services/token.service";

export type userTypeForThisPageInput =
  | "team_lead"
  | "super_admin"
  | "admin"
  | "employee"
  | "admin_hr";

interface decodedUserObjectType {
  token_type: string;
  exp: number;
  jti: string;
  user_id: number;
  email: string;
  uuid: string;
  user_role: userTypeForThisPageInput;
}

type tokenTypeInput = "tokens" | "client_tokens";

const TypeVerifierUserChecker = (
  userTypeForThisPage: userTypeForThisPageInput[],
  tokenType: tokenTypeInput = "tokens"
): boolean => {
  const accessToken = TokenService.getLocalAccessToken(tokenType);

  try {
    const decoded = jwtDecode<decodedUserObjectType>(accessToken);

    return userTypeForThisPage.includes(decoded.user_role);
  } catch {
    // Handle token errors by redirecting to the appropriate login page
    // Uncomment and use the navigation logic as needed
    // const navigate = useNavigate();
    // if (userTypeForThisPage.includes("super_admin") || userTypeForThisPage.includes("admin")) {
    //   navigate('/admin/login');
    // } else {
    //   navigate('/login');
    // }
    return false;
  }
};

export default TypeVerifierUserChecker;
