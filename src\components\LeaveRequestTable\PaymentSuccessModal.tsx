import React from "react";
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  Box,
  Text,
  IconButton,
  Flex,
} from "@chakra-ui/react";
import { CloseIcon, CheckIcon } from "@chakra-ui/icons";

interface PaymentSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const PaymentSuccessModal: React.FC<PaymentSuccessModalProps> = ({
  isOpen,
  onClose,
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay bg="blackAlpha.600" />
      <ModalContent
        maxW="400px"
        borderRadius="12px"
        p={6}
        position="relative"
        bg="white"
      >
        {/* Close button in top right */}
        <IconButton
          aria-label="Close modal"
          icon={<CloseIcon />}
          size="sm"
          variant="ghost"
          position="absolute"
          top={4}
          right={4}
          onClick={onClose}
          _hover={{ bg: "gray.100" }}
        />

        <ModalBody p={0}>
          <Flex direction="column" align="center" justify="center" py={8}>
            {/* Blue checkmark circle */}
            <Box
              w="40px"
              h="40px"
              borderRadius="50%"
              bg="#0B3178"
              display="flex"
              alignItems="center"
              justifyContent="center"
              mb={6}
            >
              <CheckIcon color="white" boxSize={4} />
            </Box>

            {/* Success text */}
            <Text
              fontSize="md"
              fontWeight={500}
              color="gray.800"
              textAlign="center"
              fontFamily="Inter"
            >
              Leave Payment
            </Text>
            <Text
              fontSize="md"
              fontWeight={500}
              color="gray.800"
              textAlign="center"
              fontFamily="Inter"
            >
              Successfully Triggered
            </Text>
          </Flex>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default PaymentSuccessModal;
