import { createKPI, deleteKPI, getAllKPIs, updateKPI } from "../api/kpis";
import { uploadFile } from "../api/storage";
import {
  useCrudCreate,
  useCrudDelete,
  useCrudQuery,
  useCrudUpdate,
} from "./useCrud";

export const kpisQueryKey = ["kpis"];

export const useGetKPIs = (page?: number, limit?: number) =>
  useCrudQuery(kpisQueryKey, () => getAllKPIs(page, limit));

export const useAddKPI = (onClose: () => void) =>
  useCrudCreate(
    kpisQueryKey,
    async ({ values, resetForm }) => {
      const formData = new FormData();
      if (values.brief) formData.append("file", values.brief);

      const brief = values?.brief ? (await uploadFile(formData))?.data : "";

      return createKPI({ ...values, brief }, resetForm);
    },
    onClose
  );

export const useUpdateKPI = (id: number, onClose?: () => void) =>
  useCrudUpdate(
    kpisQueryKey,
    async ({ values, resetForm }) => {
      const formData = new FormData();
      if (values.brief) formData.append("file", values.brief);

      const brief =
        typeof values?.brief === "string"
          ? values?.brief
          : values?.brief
          ? (await uploadFile(formData))?.data
          : "";
      return updateKPI(id, { ...values, brief }, resetForm);
    },
    onClose
  );

export const useDeleteKPI = () => useCrudDelete(kpisQueryKey, deleteKPI);
