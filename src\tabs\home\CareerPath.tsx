import { UploadIcon } from "@radix-ui/react-icons";
import { HiOutlinePlus } from "react-icons/hi";
import { RiSendPlane2Fill } from "react-icons/ri";

import AddCareerPath from "../../drawers/AddCareerPath";
import CustomDrawer from "../../drawers/CustomDrawer";
import UpdateCareerPath from "../../drawers/UpdateCareerPath";
import UploadCareerpath from "../../drawers/uploadCareerpath";

import { getAllCareerPaths } from "../../api/careerpath";
import CrudUI, { CrudRow } from "../../components/CrudUi";
import { useDeleteCareerPath } from "../../hooks/useCareerPaths";
import { TCareerPath } from "../../types/careerpath";

const CareerPath = () => {
  const deleteMutation = useDeleteCareerPath();

  const renderRow = (path: TCareerPath) => {
    const fields = [
      { label: "Name of Grade Level", value: path.name },
      { label: "Grade Level", value: path.level },
      {
        label: "Educational Qualification",
        value: path.educationalQualification,
      },
      { label: "Age Range", value: `${path.minAge} - ${path.maxAge}` },
      {
        label: "Experience Required (Years)",
        value: path.yearsOfExperience,
      },
      { label: "Position Lifespan", value: path.positionLifespan },
      { label: "Slots Available", value: path.slotsAvailable },
      { label: "Annual Package", value: path.annualPackage },
    ];

    return (
      <CrudRow
        item={path}
        itemName="Career Path"
        fields={fields}
        tableDataFields={fields.map((f) => String(f.value))}
        renderDrawer={
          <>
            {/* <CustomDrawer showModalBtnText="View" drawerSize="sm">
              <CareerPathDetails {...path} />
            </CustomDrawer> */}

            <CustomDrawer
              showModalBtnText=""
              showModalBtnVariant="primary"
              showModalBtnColor="white"
              leftIcon={<RiSendPlane2Fill />}
              drawerSize="sm"
            >
              <UpdateCareerPath careerPath={path} />
            </CustomDrawer>
          </>
        }
        onDelete={() => deleteMutation.mutate(path.id)}
      />
    );
  };

  return (
    <>
      <CrudUI
        title="Career Paths"
        queryKey="career-paths"
        queryFn={(page) => getAllCareerPaths(page)}
        columns={[
          "Name of Grade Level",
          "Grade Level",
          "Educational Qualification",
          "Age Range",
          "Experience Required (Years)",
          "Position Lifespan",
          "Slots Available",
          "Annual Package",
          "Actions",
        ]}
        renderRow={renderRow}
        actions={
          <>
            <CustomDrawer
              showModalBtnText="Upload Career Paths"
              showModalBtnVariant="outline"
              showModalBtnColor="primary"
              drawerSize="sm"
              leftIcon={<UploadIcon />}
            >
              <UploadCareerpath />
            </CustomDrawer>
            <CustomDrawer
              showModalBtnText="Add New Career Path"
              showModalBtnVariant="primary"
              showModalBtnColor="white"
              drawerSize="sm"
              leftIcon={<HiOutlinePlus />}
            >
              <AddCareerPath />
            </CustomDrawer>
          </>
        }
      />
    </>
  );
};

export default CareerPath;
