export interface TTasks {
  id: number;
  name: string;
  uplineKpi: string; // Renamed from 'uplineInitiative' to 'uplineKpi' to match the example
  ownerEmail: string;
  createdBy: string;
  tenantId: string;
  taskType: string;
  routineType: string;
  startDate: string;
  startTime: string;
  duration: string;
  repeatEvery: string;
  occursOnDaysWeekly: string;
  occursOnDayNumberMonthly: string;
  occursDayPositionMonthly: string;
  occursOnDayMonthly: string;
  endDate: string;
  afterOccurrence: string;
  reworkLimit: string;
  qualityTargetPoint: string;
  quantityTargetPoint: string;
  quantityTargetUnit: string;
  turnAroundTimeTargetPoint: string;
  createdAt?: string; // New field to capture the creation timestamp
  updatedAt?: string; // New field to capture the update timestamp
}
