export const dateTimeFormats = [
  // Standard Date Formats
  { value: "yyyy-MM-dd", label: "2025-03-31" },
  { value: "dd-MM-yyyy", label: "31-03-2025" },
  { value: "MM/dd/yyyy", label: "03/31/2025" },
  { value: "MM-dd-yyyy", label: "03-31-2025" },
  { value: "dd/MM/yyyy", label: "31/03/2025" },
  { value: "MMMM dd, yyyy", label: "March 31, 2025" },
  { value: "dd MMMM yyyy", label: "31 March 2025" },
  { value: "MMM dd, yyyy", label: "Mar 31, 2025" },

  // Time Formats
  { value: "HH:mm", label: "14:30" },
  { value: "HH:mm:ss", label: "14:30:45" },
  { value: "HH:mm:ss.SSS", label: "14:30:45.123" },
  { value: "hh:mm a", label: "02:30 PM" },
  { value: "h:mm a", label: "2:30 PM" },

  // Date-Time Without Timezone
  { value: "yyyy-MM-dd HH:mm", label: "2025-03-31 14:30" },
  { value: "yyyy-MM-dd HH:mm:ss", label: "2025-03-31 14:30:45" },
  { value: "yyyy-MM-dd HH:mm:ss.SSS", label: "2025-03-31 14:30:45.123" },
  { value: "yyyy/MM/dd HH:mm:ss", label: "2025/03/31 14:30:45" },
  { value: "dd-MM-yyyy HH:mm:ss", label: "31-03-2025 14:30:45" },

  // ISO 8601 Formats
  { value: "yyyy-MM-dd'T'HH:mm:ss'Z'", label: "2025-03-31T14:30:00Z" },
  { value: "yyyy-MM-dd'T'HH:mm:ssXXX", label: "2025-03-31T14:30:00+02:00" },
  {
    value: "yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
    label: "2025-03-31T14:30:00.123+02:00",
  },

  // RFC 2822 Formats
  {
    value: "EEE, dd MMM yyyy HH:mm:ss Z",
    label: "Mon, 31 Mar 2025 14:30:00 +0200",
  },
  {
    value: "EEE, dd MMM yyyy HH:mm:ss z",
    label: "Mon, 31 Mar 2025 14:30:00 GMT",
  },

  // Named Timezones
  { value: "MMMM dd, yyyy HH:mm z", label: "March 31, 2025 14:30 UTC" },
  { value: "MMMM dd, yyyy HH:mm zzzz", label: "March 31, 2025 14:30 GMT+2" },
  { value: "MMMM dd, yyyy hh:mm a z", label: "March 31, 2025 02:30 PM EST" },

  // Human-Readable Formats
  { value: "EEEE, MMMM dd, yyyy HH:mm", label: "Monday, March 31, 2025 14:30" },
  { value: "EEE, dd MMM yyyy HH:mm", label: "Mon, 31 Mar 2025 14:30" },
  {
    value: "EEE, dd MMM yyyy HH:mm:ss z",
    label: "Mon, 31 Mar 2025 14:30:45 GMT",
  },

  // Unix Timestamp Formats
  { value: "X", label: "1711885800 (Seconds)" },
  { value: "x", label: "1711885800000 (Milliseconds)" },
];

export const daysOfWeek = [
  "monday",
  "tuesday",
  "wednesday",
  "thursday",
  "friday",
  "saturday",
  "sunday",
];
