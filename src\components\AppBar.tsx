import { Box, Flex, Image, Text } from "@chakra-ui/react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import EmetricsLogo from "../assets/images/logo.png";
import { useCurrentOrganization } from "../hooks/organization";
import { deepBlue } from "../theme/colors";
import SEO from "./SEO";

interface AppBarProps {
  heading: string;
  subtitle?: string;
  avatar?: string;
  imgAlt?: string;
}

const MotionBox = motion.create(Box);

const AppBar: React.FC<AppBarProps> = ({ heading, subtitle }) => {
  const { data, isLoading } = useCurrentOrganization();
  const currentOrganization = data?.data;

  const organizationName = currentOrganization?.name;

  return (
    <>
      <SEO
        title={`${heading ? heading + " | " : ""} ${
          organizationName || "E-metrics suite"
        }`}
      />
      <MotionBox
        as="header"
        py="6"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Flex alignItems="center" gap="2">
          {isLoading ? (
            <Box
              className="skeleton"
              height="40px"
              width="40px"
              borderRadius="6"
            />
          ) : (
            <Link to="/dashboard">
              <Image
                src={currentOrganization?.logo || EmetricsLogo}
                height={8}
                width={currentOrganization?.logo ? 8 : "auto"}
                aspectRatio="auto"
                borderRadius="60000000"
                display={{ base: "block", md: "none" }}
                alt={organizationName || "E-Metrics Suite"}
              />{" "}
            </Link>
          )}

          <Box>
            {isLoading ? (
              <Box
                className="skeleton"
                height="16px"
                width="150px"
                mb="2"
                borderRadius="6"
              />
            ) : (
              <Text fontSize="sm" textTransform="uppercase" color={deepBlue}>
                <Text as="span">
                  {currentOrganization?.shortName?.toUpperCase()}
                </Text>
                <Text
                  as="span"
                  color="gray.200"
                  display={
                    currentOrganization?.shortName
                      ? "inline"
                      : { base: "inline", md: "none" }
                  }
                >
                  {" | "}
                </Text>
                {heading}
              </Text>
            )}
            {subtitle &&
              (isLoading ? (
                <Box className="skeleton" height="12px" width="100px" />
              ) : (
                <Text fontSize="xs" color="gray.500">
                  {subtitle}
                </Text>
              ))}
          </Box>
        </Flex>
      </MotionBox>
    </>
  );
};

export default AppBar;
