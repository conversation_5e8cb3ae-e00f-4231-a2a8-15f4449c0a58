import {
  Flex,
  <PERSON>ert,
  AlertIcon,
  AlertT<PERSON>le,
  AlertDescription,
  Button,
} from "@chakra-ui/react";
import SignUpIllustration from "../../assets/images/signup-illustration.svg";

import AuthSideImage from "../../components/AuthSideImage";

const ServerError = () => {
  return (
    <Flex minH="100vh" w="100vw" overflowY="hidden">
      <AuthSideImage SignUpIllustration={SignUpIllustration} />
      <Flex h="100vh" w="100%" bg="gray.300" flex={1} align="center">
        <Alert
          status="success"
          variant="subtle"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          textAlign="center"
          height="200px"
        >
          <AlertIcon boxSize="40px" mr={0} />
          <AlertTitle mt={4} mb={1} fontSize="lg">
            This was an unexpected server error
          </AlertTitle>
          <AlertDescription maxWidth="sm">
            {/* Click{" "} */}
            <p>
              Please contact the development team. tips of what you can do are
              below.
            </p>
            <ol>
              <li>try to refresh the page if it persist then re-login</li>
              <li>if the above are not working for you contact us!!</li>
            </ol>
            <br />
            <RefreshButton /> <ErrorLoginButton />
            <br />
          </AlertDescription>
        </Alert>
      </Flex>
    </Flex>
  );
};

export default ServerError;
