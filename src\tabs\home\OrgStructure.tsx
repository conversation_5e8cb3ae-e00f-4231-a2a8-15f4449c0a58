import { QuestionIcon } from "@chakra-ui/icons";
import {
  Box,
  Divider,
  Flex,
  Grid,
  Link,
  List,
  ListIcon,
  ListItem,
  Text,
} from "@chakra-ui/react";
import { useState } from "react";
import { IoIosArrowForward } from "react-icons/io";
import { RiCheckboxBlankCircleFill } from "react-icons/ri";
import { Link as ReactLink } from "react-router-dom";
import { setupOrganization } from "../../api/organization";
import { CustomSwitcher } from "../../components/custom/CustomSwitcher";
import CustomTab from "../../components/custom/CustomTab";
import { CustomTooltip } from "../../components/custom/Tooltip";
import { queryClient } from "../../context/CustomProvider";
import {
  currentOrganizationKey,
  useCurrentOrganization,
  useGetOrganizationStructures,
} from "../../hooks/organization";
import { useGetPeople } from "../../hooks/usePeople";
import {
  OrganizationStructureType,
  TOrganization,
} from "../../types/organization";
import { capitalizeFirst } from "../../utils";

const OrgStructureItem = ({ to, color, title, count, label }: any) => (
  <>
    <ListItem display="flex" alignItems="center" px="2px">
      <ListIcon as={RiCheckboxBlankCircleFill} boxSize={2} color={color} />
      <Link as={ReactLink} to={to} flex="1" _hover={{ textDecoration: "none" }}>
        <Grid flex="1" gridTemplateColumns="1fr 1fr auto">
          <Text color={color} fontWeight="500">
            {title}
          </Text>
          <Text isTruncated fontWeight="500" color="gray.500">
            {count} {label}
          </Text>
          <IoIosArrowForward size="14" />
        </Grid>
      </Link>
    </ListItem>
    <Divider mb="4" borderColor="gray.200" _last={{ border: 0 }} />
  </>
);

const buildStructureItems = (structures: any[], peopleCount: number): any[] => [
  ...structures
    .sort((a, b) => a.priority - b.priority)
    .map((os) => {
      const tiers = os.tier || [];
      const title = capitalizeFirst(os.name);
      const count = tiers.length;
      return {
        to: `/organization/structure/${os.name}`,
        color: "primary",
        title,
        count,
        label: `${title}${count > 1 ? "s" : ""}`,
      };
    }),
  {
    to: "/organization/people",
    color: "tomato",
    title: "People",
    count: peopleCount,
    label: peopleCount > 1 ? "Person" : "People",
  },
];

const OrganizationStructure = () => {
  const people = useGetPeople().data?.data?.data || [];
  const peopleCount = people.length || 0;

  const { customStructures, defaultStructures } =
    useGetOrganizationStructures();

  const { data } = useCurrentOrganization();
  const currentOrganization = data?.data as TOrganization;

  const [isDefaultSelected, setIsDefaultSelected] = useState(
    currentOrganization?.structureType === OrganizationStructureType.default
  );
  const [loading, setLoading] = useState(false);

  if (!currentOrganization?.name) return null;

  const handleSwitchChange = async (state: boolean) => {
    setIsDefaultSelected(state);
    setLoading(true);

    try {
      const res = await setupOrganization(
        undefined,
        undefined,
        undefined,
        !state
          ? OrganizationStructureType.custom
          : OrganizationStructureType.default
      );

      if (res) {
        queryClient.invalidateQueries({
          queryKey: currentOrganizationKey,
        });
      } else {
        setIsDefaultSelected(!state);
      }
    } catch (error) {
      setIsDefaultSelected(!state);
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const tabItems = [
    {
      label: "Default",
      items: buildStructureItems(defaultStructures, peopleCount),
    },
    {
      label: "Custom",
      items: buildStructureItems(customStructures, peopleCount),
    },
  ];

  return (
    <Box>
      <Flex
        gap="1"
        alignItems="center"
        justify="flex-end"
        fontSize="sm"
        mb="5"
        mt="-2"
      >
        <Text> Custom</Text>
        <CustomSwitcher
          disabled={loading}
          isChecked={isDefaultSelected}
          onChange={handleSwitchChange}
        />
        <Text>Default</Text>

        <CustomTooltip
          hasArrow
          tip={
            <>
              An organization structure defines how your teams, departments, or
              groups are arranged within your company.
              <br />
              <br />
              <b>Custom</b>: Build your own structure to match your unique
              organization.
              <br />
              <b>Default</b>: Use our recommended structure for a quick start.
              <br />
              <br />
              Switch between custom and default to choose what fits your needs
              best.
            </>
          }
        >
          <QuestionIcon />
        </CustomTooltip>
      </Flex>
      <CustomTab
        tabList={tabItems.map(({ label }) => ({ label, showForAll: true }))}
        tabPanels={tabItems.map(({ items }) => () => (
          <List spacing={4} fontSize="sm">
            {items.map((item, index) => (
              <OrgStructureItem key={index} {...item} />
            ))}
          </List>
        ))}
        tabId="viewAllStructuresTab"
      />
    </Box>
  );
};

export default OrganizationStructure;
