import {
  Box,
  Button,
  Checkbox,
  CheckboxGroup,
  Flex,
  FormControl,
  FormLabel,
  Grid,
  HStack,
  Input,
  Text,
  useToast,
} from "@chakra-ui/react";
import { Form, Formik } from "formik";
import { useEffect, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import { useNavigate } from "react-router-dom";
import * as Yup from "yup";
import axiosInstance, { baseURL } from "../../../api";
import DateInputWithLabel from "../../../components/DateInputWithLabel";
import InputWithLabel from "../../../components/InputWithLabel";
import SelectWithLabel from "../../../components/selectWithLabels";

interface DataType {
  company_name: string;
  company_logo?: null | string;
  ownerEmail: string;
  owner_first_name: string;
  owner_last_name: string;
  company_short_name: string;
  owner_phone_number: string;
  work_start_time: string;
  work_stop_time: string;
  work_break_start_time: string;
  work_break_stop_time: string;
  work_days: number[];
  timezone: string;
}

const UpdateOrganisation = () => {
  const toast = useToast();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadLogo, setUploadLogo] = useState(false);
  const { showBoundary } = useErrorBoundary();
  const [data, setData] = useState<null | DataType>();

  const getCurrentOrgInfo = async () => {
    const org_name = localStorage.getItem("current_organization_short_name");
    const client_token = localStorage.getItem("client_tokens");
    if (!org_name || !client_token) return;

    try {
      const response = await fetch(
        `${baseURL}client/${org_name}/organisation/current/`,
        {
          method: "get",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
            Authorization: "Bearer " + JSON.parse(client_token)?.access,
          },
        }
      );
      const respData = await response.json();
      if (respData?.company_short_name) {
        setData(respData);
      } else {
        showBoundary(respData);
      }
    } catch (err: any) {
      if (err.status === 401 || err.response.status === 401) {
        showBoundary(err);
      } else {
        toast({
          title: "Network Error",
          status: "error",
          position: "top",
          duration: 5000,
          isClosable: true,
        });
      }
    }
  };

  useEffect(() => {
    getCurrentOrgInfo();
  }, []);

  const validationSchema = Yup.object().shape({
    organisation_short_name: Yup.string().required("Required"),
    organisation_name: Yup.string().required("Required"),
    timezone: Yup.string().required("Required"),
    work_start_time: Yup.string().required("Required"),
    work_stop_time: Yup.string().required("Required"),
    work_break_start_time: Yup.string().required("Required"),
    work_break_stop_time: Yup.string().required("Required"),
    work_days: Yup.array().of(Yup.number()).min(1, "Select at least one day"),
  });

  const onSubmit = async (formData: any) => {
    const org_name = localStorage.getItem("current_organization_short_name");
    const client_token = localStorage.getItem("client_tokens");
    if (!org_name || !client_token) return;

    setIsSubmitting(true);
    const form = new FormData();
    form.append("work_days", JSON.stringify(formData.work_days));
    form.append("organisation_short_name", formData.organisation_short_name);
    form.append("organisation_name", formData.organisation_name);
    if (uploadLogo && formData.organisation_logo) {
      form.append("organisation_logo", formData.organisation_logo);
    }
    form.append("work_start_time", formData.work_start_time);
    form.append("work_stop_time", formData.work_stop_time);
    form.append("work_break_start_time", formData.work_break_start_time);
    form.append("work_break_stop_time", formData.work_break_stop_time);
    form.append("timezone", formData.timezone);

    try {
      const resp = await axiosInstance.put(
        `/user/organisation/update/${org_name}/`,
        form
      );
      localStorage.setItem("org_info", JSON.stringify(resp.data.data));
      toast({
        title: "Updated Successfully",
        status: "success",
        position: "top",
        duration: 5000,
        isClosable: true,
      });
    } catch (err: any) {
      if (err.response.status === 401) {
        showBoundary(err);
      }
      toast({
        title: "Please check the fields",
        status: "error",
        position: "top",
        duration: 5000,
        isClosable: true,
      });
    }
    setIsSubmitting(false);
  };

  return (
    <Flex minH="100vh" w="100%" overflowY="hidden">
      <Flex bg="white" flex={4} align="center" justifyContent="center">
        <Box width="100%">
          <Formik
            initialValues={{
              organisation_short_name: data?.company_short_name || "",
              organisation_name: data?.company_name || "",
              timezone: data?.timezone || "",
              work_start_time: data?.work_start_time || "",
              work_stop_time: data?.work_stop_time || "",
              work_break_start_time: data?.work_break_start_time || "",
              work_break_stop_time: data?.work_break_stop_time || "",
              work_days: data?.work_days || [],
              organisation_logo: null,
            }}
            validationSchema={validationSchema}
            onSubmit={onSubmit}
            enableReinitialize
          >
            {({ values, setFieldValue, errors, touched }) => (
              <Form>
                <Grid
                  gridTemplateColumns="1fr 1fr"
                  columnGap={4}
                  rowGap={8}
                  mb="10"
                >
                  <InputWithLabel
                    id="organizationShortName"
                    label="Organization Short Name"
                    size="lg"
                    variant="filled"
                    placeholder="e.g UAC"
                    bg="secondary.200"
                    name="organisation_short_name"
                    value={values.organisation_short_name}
                    isDisabled={true}
                    formErrorMessage={errors.organisation_short_name}
                  />
                  <InputWithLabel
                    isDisabled={true}
                    id="organizationName"
                    label="Organization Name"
                    size="lg"
                    variant="filled"
                    placeholder="e.g Godwin"
                    bg="secondary.200"
                    name="organisation_name"
                    value={values.organisation_name}
                    formErrorMessage={errors.organisation_name}
                  />
                  <SelectWithLabel
                    name="timezone"
                    id="timeZone"
                    label="Time Zone"
                    size="lg"
                    variant="filled"
                    placeholder={data?.timezone}
                    bg="secondary.200"
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setFieldValue(
                        "timezone",
                        (e.target as unknown as HTMLSelectElement).value
                      )
                    }
                    formErrorMessage={errors.timezone}
                  />
                  <CheckboxGroup
                    colorScheme="blue"
                    value={values.work_days.map(String)}
                    onChange={(value) =>
                      setFieldValue("work_days", value.map(Number))
                    }
                  >
                    <Box>
                      <FormLabel fontSize="xs" fontWeight="500">
                        Work Days
                      </FormLabel>
                      <Text fontSize="xs" color="crimson">
                        {errors.work_days}
                      </Text>
                      <Grid
                        gridTemplateColumns="1fr 1fr"
                        columnGap={4}
                        rowGap={8}
                      >
                        {[
                          "Monday",
                          "Tuesday",
                          "Wednesday",
                          "Thursday",
                          "Friday",
                          "Saturday",
                          "Sunday",
                        ].map((day, index) => (
                          <Checkbox key={day} value={String(index)}>
                            {day}
                          </Checkbox>
                        ))}
                      </Grid>
                    </Box>
                  </CheckboxGroup>
                  <DateInputWithLabel
                    type="time"
                    id="DailyResumption"
                    label="Resumption Time(Start)"
                    size="lg"
                    variant="filled"
                    value={values.work_start_time}
                    placeholder="e.g 08:00"
                    bg="secondary.200"
                    name="work_start_time"
                    onChange={(e) =>
                      setFieldValue("work_start_time", e.target.value)
                    }
                    formErrorMessage={errors.work_start_time}
                  />
                  <DateInputWithLabel
                    type="time"
                    value={values.work_stop_time}
                    id="DailyClose/End"
                    label="Resumption Time(end)"
                    size="lg"
                    variant="filled"
                    placeholder="e.g 16:00"
                    bg="secondary.200"
                    name="work_stop_time"
                    onChange={(e) =>
                      setFieldValue("work_stop_time", e.target.value)
                    }
                    formErrorMessage={errors.work_stop_time}
                  />
                  <DateInputWithLabel
                    type="time"
                    id="DailyBreakTime"
                    label="Break Time(start)"
                    size="lg"
                    variant="filled"
                    value={values.work_break_start_time}
                    placeholder="e.g 12:00"
                    bg="secondary.200"
                    name="work_break_start_time"
                    onChange={(e) =>
                      setFieldValue("work_break_start_time", e.target.value)
                    }
                    formErrorMessage={errors.work_break_start_time}
                  />
                  <DateInputWithLabel
                    type="time"
                    id="DailyBreakClose/End"
                    label="Break Time(end)"
                    size="lg"
                    variant="filled"
                    value={values.work_break_stop_time}
                    placeholder="e.g 13:00"
                    bg="secondary.200"
                    name="work_break_stop_time"
                    onChange={(e) =>
                      setFieldValue("work_break_stop_time", e.target.value)
                    }
                    formErrorMessage={errors.work_break_stop_time}
                  />
                  <Box>
                    <Button onClick={() => setUploadLogo(!uploadLogo)}>
                      {uploadLogo ? "Keep Current Logo" : "Update Logo"}
                    </Button>
                    {uploadLogo && (
                      <>
                        <Text fontWeight="500" fontSize="sm" mb="4">
                          Upload Organization Logo
                        </Text>
                        <HStack mb="5">
                          <FormControl>
                            <Input
                              type="file"
                              accept=".png, .jpg, .jpeg"
                              variant="filled"
                              bg="transparent"
                              onChange={(e) =>
                                setFieldValue(
                                  "organisation_logo",
                                  e.target.files?.[0]
                                )
                              }
                            />
                          </FormControl>
                        </HStack>
                      </>
                    )}
                  </Box>
                </Grid>
                <Flex justifyContent={"space-between"} alignItems={"center"}>
                  <Button
                    fontWeight="500"
                    variant="primary"
                    size="lg"
                    py={7}
                    type="submit"
                    isLoading={isSubmitting}
                    loadingText="Updating..."
                    my="6"
                    width={"40%"}
                  >
                    Update Organization
                  </Button>
                </Flex>
              </Form>
            )}
          </Formik>
        </Box>
      </Flex>
    </Flex>
  );
};

export default UpdateOrganisation;
