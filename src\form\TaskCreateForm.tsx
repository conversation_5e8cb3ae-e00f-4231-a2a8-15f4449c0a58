import {
  Box,
  Button,
  Checkbox,
  CheckboxGroup,
  Flex,
  FormControl,
  FormLabel,
  Grid,
  HStack,
  Input,
  Radio,
  Select,
  Text,
  Tooltip,
  useToast,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import moment from "moment";
import { useEffect, useState } from "react";
import DurationPicker from "react-duration-picker";
import { useErrorBoundary } from "react-error-boundary";
import * as yup from "yup";
import axiosInstance from "../api";
import SelectAsyncPaginate from "../components/AsyncSelect";
import { ExcludeDaysInCalendar } from "../components/DateInput";
import InputWithLabel from "../components/InputWithLabel";
import { CurrentOrgnisationSettingsType } from "../services/list.service";

type taskTypeInput =
  | "qualitative"
  | "quantitative"
  | "quantitative_and_qualitative"
  | string;
type routineOptionInput = "weekly" | "daily" | "monthly" | "once" | string;

interface TaskFormInput {
  name: string;
  owner: string;
  task_type: taskTypeInput;
  initiative_id: string;
  start_date: string;
  start_time: string;
  duration: string;
  routine_option: routineOptionInput;
  repeat_every?: number;
  after_occurrence?: number;
  end_date?: string;
  target_brief?: any;
  rework_limit?: number;
  turn_around_time_target_point: number;
  quality_target_point?: number;
  quantity_target_point?: number;
  quantity_target_unit?: number;

  // for weekly
  monday?: boolean;
  tuesday?: boolean;
  wednesday?: boolean;
  thursday?: boolean;
  friday?: boolean;
  saturday?: boolean;
  sunday?: boolean;

  // for monthly
  occurs_month_day_number?: number;
  occurs_month_day_position?: string;
  occurs_month_day?: string;
}

const schema = yup
  .object()
  .shape({
    name: yup.string().required("you need to name your task!"),
    owner: yup.string().required("You need a owner email"),
    task_type: yup.string().required("Pick a Task Type"),
    initiative_id: yup
      .string()
      .required("You need to connect an initiative to the task"),
    start_date: yup.string().required("you need a start date"),
    start_time: yup.string().required("you need a start time"),
    duration: yup.string().required("Pick a duration"),
    routine_option: yup
      .string()
      .required("you need to pick between weekly,monthly or daily"),
    repeat_every: yup.number().typeError("repeat how many time in a routine"),
    after_occurrence: yup.number(),
    end_date: yup.string(),
    rework_limit: yup.mixed(),
    target_brief: yup.mixed(),
    turn_around_time_target_point: yup
      .number()
      .typeError("Must be a number")
      .required("you need to specify this field"),
    quality_target_point: yup.mixed(),
    quantity_target_point: yup.mixed(),
    quantity_target_unit: yup.mixed(),
    monday: yup.boolean(),
    tuesday: yup.boolean(),
    wednesday: yup.boolean(),
    thursday: yup.boolean(),
    friday: yup.boolean(),
    saturday: yup.boolean(),
    sunday: yup.boolean(),
    occurs_month_day_number: yup
      .number()
      .typeError("must be a number and cant be empty"),
    occurs_month_day_position: yup
      .string()
      .typeError("must be a number and cant be empty"),
    occurs_month_day: yup
      .string()
      .typeError("must be a number and cant be empty"),
  })
  .test("chooseAtLeastEndBy_AfterOccurence_or_endDate", "", (obj) => {
    if (obj.routine_option === "once") return true;

    if (
      (obj.end_date === undefined && obj.after_occurrence !== undefined) ||
      (obj.end_date !== undefined && obj.after_occurrence === undefined)
    )
      return true;

    return new yup.ValidationError(
      "You Have to Choose At Least 'End Date' or 'After Occurrence' ",
      null,
      "end_date"
    );
  })
  .test("validate_dates", "", (obj) => {
    if (obj.routine_option === "weekly") {
      if (
        obj.monday ||
        obj.tuesday ||
        obj.wednesday ||
        obj.thursday ||
        obj.friday
      ) {
        return true;
      }
      return new yup.ValidationError("At least pick a day!", null, "monday");
    }
    return true;
  })
  .test("validate_target_points", "", (obj) => {
    if (obj.task_type === "qualitative") {
      if (
        obj.quality_target_point == undefined ||
        obj.quality_target_point < 1
      ) {
        return new yup.ValidationError(
          "Quality Target Point is required and must be greater than 0 ",
          null,
          "quality_target_point"
        );
      }

      if (
        obj.rework_limit == undefined ||
        obj.rework_limit < 1 ||
        obj.rework_limit === ""
      ) {
        return new yup.ValidationError(
          " Rework Limit is required and must be greater than 0 ",
          null,
          "rework_limit"
        );
      }
    }
    if (obj.task_type == "quantitative") {
      if (
        obj.quantity_target_point == undefined ||
        obj.quantity_target_point < 1 ||
        obj.quantity_target_point === ""
      ) {
        return new yup.ValidationError(
          "Quantity Target Point is required and must be greater than 0 ",
          null,
          "quantity_target_point"
        );
      }
      if (
        obj.quantity_target_unit == undefined ||
        obj.quantity_target_unit < 1 ||
        obj.quantity_target_unit === ""
      ) {
        return new yup.ValidationError(
          "Quantity Target Unit is required and must be greater than 0 ",
          null,
          "quantity_target_unit"
        );
      }
    }

    if (obj.task_type == "quantitative_and_qualitative") {
      if (
        obj.quantity_target_point == undefined ||
        obj.quantity_target_point < 1 ||
        obj.quantity_target_point === ""
      ) {
        return new yup.ValidationError(
          "Quantity Target Point is required and must be greater than 0 ",
          null,
          "quantity_target_point"
        );
      }
      if (
        obj.quantity_target_unit == undefined ||
        obj.quantity_target_unit < 1 ||
        obj.quantity_target_unit === ""
      ) {
        return new yup.ValidationError(
          "Quantity Target Unit is required and must be greater than 0 ",
          null,
          "quantity_target_unit"
        );
      }
      if (
        obj.quality_target_point == undefined ||
        obj.quality_target_point < 1 ||
        obj.quality_target_point == ""
      ) {
        return new yup.ValidationError(
          "Quality Target Point is required and must be greater than 0 ",
          null,
          "quality_target_point"
        );
      }
      if (
        obj.rework_limit == undefined ||
        obj.rework_limit < 1 ||
        obj.rework_limit === ""
      ) {
        return new yup.ValidationError(
          " Rework Limit is required and must be greater than 0 ",
          null,
          "rework_limit"
        );
      }
    }
    return true;
  });

const getEmployeeByEmail = async (email: string) => {
  const org_name = localStorage.getItem("current_organization_short_name");
  const DoesTeamLeadApi = await axiosInstance.get(
    `/client/${org_name}/employee/?user__email=${email}`
  );
  return DoesTeamLeadApi.data.data || [];
};

const TaskCreateForm = () => {
  const toast = useToast();
  const org_name = localStorage.getItem("current_organization_short_name");
  const levelToQuery: any = {
    "corporate-level": "corporate_level__uuid",
    "divisional-level": "division__uuid",
    "group-level": "group__uuid",
    "departmental-level": "department__uuid",
    "unit-level": "unit__uuid",
    upline__email: "upline__email",
  };
  const [CurrentOrgnisationSettings, setCurrentOrgnisationSettings] =
    useState<CurrentOrgnisationSettingsType>({
      company_name: "",
      ownerEmail: "",
      owner_first_name: "",
      owner_last_name: "",
      company_short_name: "",
      owner_phone_number: "",
      work_start_time: "",
      work_stop_time: "",
      work_break_start_time: "",
      work_break_stop_time: "",
      work_days: [],
      timezone: "",
    });
  const [isCorporateTeamLead, setisCorporateTeamLead] =
    useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [initiative, setInitiative] = useState<any[]>([]);
  const [listOfEmployees, setListOfEmployees] = useState<any[]>([]);
  const [IsemployeeLoading, setIsEmployeeLoading] = useState<boolean>();
  const [hasGottenEmployees, setHasGottenEmployees] = useState<boolean>(false);
  const [currentEmployueeUUid, setCurrentEmployueeUUid] = useState<
    string | null
  >(null);
  const [currentOwner, setCurrentOwner] = useState<any>();

  const List_of_start_time = [
    "00:00",
    "00:30",
    "01:00",
    "01:30",
    "02:00",
    "02:30",
    "03:00",
    "03:30",
    "04:00",
    "03:30",
    "05:00",
    "05:30",
    "06:00",
    "06:30",
    "07:00",
    "07:30",
    "08:00",
    "08:30",
    "09:00",
    "09:30",
    "10:00",
    "10:30",
    "11:00",
    "11:30",
    "12:00",
    "12:30",
    "13:00",
    "13:30",
    "14:00",
    "14:30",
    "15:00",
    "15:30",
    "16:00",
    "16:30",
    "17:00",
    "17:30",
    "18:00",
    "18:30",
    "19:00",
    "19:30",
    "20:00",
    "20:30",
    "21:00",
    "21:30",
    "22:00",
    "22:30",
    "23:00",
    "23:30",
    "24:00",
    "24:30",
  ];

  const getInitiatives = async (ownerEmail: string) => {
    const ORG_NAME = localStorage.getItem("current_organization_short_name");
    if (ORG_NAME) {
      try {
        const response = await axiosInstance.get(
          `/client/${ORG_NAME}/initiative/?ownerEmail=${ownerEmail}`
        );
        setInitiative(response.data.data);
        setIsLoading(false);
        return response.data.data;
      } catch (err: any) {
        setIsLoading(false);
        return err.response;
      }
    }
  };

  const [structureLevels, setStructureLevels] = useState<any[]>([]);
  const [currentlevel, setCurrentlevel] = useState<string>("upline__email");
  const [LevelIsLoading, setLevelIsLoading] = useState<boolean>();

  const handleLevelChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    setLevelIsLoading(true);
    let selectedLevel = e.target.value;
    setCurrentlevel(selectedLevel);
    const orgName = localStorage.getItem("current_organization_short_name");
    try {
      const response = await axiosInstance.get(
        `/organization/setup/${selectedLevel}/list/${orgName}/`
      );
      setStructureLevels(response.data.data);
      setLevelIsLoading(false);
    } catch (err: any) {
      setLevelIsLoading(false);
    }
  };

  // State for conditional inputs
  const [currentTaskType, setCurrentTaskType] = useState<taskTypeInput>();
  const [isReoccuring, setIsReoccuring] = useState<boolean>(false);
  const [isuploadTaskBrief, setIsuploadTaskBrief] = useState(false);
  const [selectedRoutine, setSelectedRoutine] =
    useState<routineOptionInput>("once");
  const [endByCalendarIsDisable, setEndByCalendarIsDisable] =
    useState<boolean>(false);
  const [endByOccurrenceIsDisable, setEndByOccurrenceIsDisable] =
    useState<boolean>(true);
  const [occursByMonthDayNumber, setOccursByMonthDayNumber] = useState(false);
  const [occursByMonthPostion, setOccursByMonthPostion] = useState(true);

  const { showBoundary } = useErrorBoundary();

  const formik = useFormik({
    initialValues: {
      name: "",
      owner: "",
      task_type: "",
      initiative_id: "",
      start_date: "",
      start_time: "",
      duration: "",
      routine_option: "once",
      repeat_every: undefined,
      after_occurrence: undefined,
      end_date: undefined,
      target_brief: null,
      rework_limit: undefined,
      turn_around_time_target_point: 0,
      quality_target_point: undefined,
      quantity_target_point: undefined,
      quantity_target_unit: undefined,
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
      saturday: false,
      sunday: false,
      occurs_month_day_number: undefined,
      occurs_month_day_position: undefined,
      occurs_month_day: undefined,
    },
    validationSchema: schema,
    onSubmit: (values) => {
      const formData = new FormData();
      formData.append("name", values.name);
      formData.append("upline_initiative[initiative_id]", values.initiative_id);
      formData.append("task_type", values.task_type);

      if (values.target_brief !== null && isuploadTaskBrief) {
        formData.append("target_brief", values.target_brief);
      }

      if (values.start_date) {
        formData.append(
          "start_date",
          moment(values.start_date).format("YYYY-MM-DD")
        );
      }

      formData.append("start_time", values.start_time);
      formData.append("duration", values.duration);

      if (values.repeat_every) {
        formData.append("repeat_every", JSON.stringify(values.repeat_every));
      }

      if (values.after_occurrence) {
        formData.append(
          "after_occurrence",
          JSON.stringify(values.after_occurrence)
        );
      }

      if (values.end_date) {
        formData.append(
          "end_date",
          moment(values.end_date).format("YYYY-MM-DD")
        );
      }

      formData.append("routine_option", values.routine_option);

      if (values.routine_option === "weekly") {
        const occurs_days: number[] = [];
        if (values.monday) occurs_days.push(0);
        if (values.tuesday) occurs_days.push(1);
        if (values.wednesday) occurs_days.push(2);
        if (values.thursday) occurs_days.push(3);
        if (values.friday) occurs_days.push(4);
        if (values.saturday) occurs_days.push(5);
        if (values.sunday) occurs_days.push(6);
        formData.append("occurs_days", JSON.stringify(occurs_days));
      }

      if (values.routine_option === "monthly") {
        if (values.occurs_month_day_number) {
          formData.append(
            "occurs_month_day_number",
            JSON.stringify(values.occurs_month_day_number)
          );
        }
        if (values.occurs_month_day_position) {
          formData.append(
            "occurs_month_day_position",
            values.occurs_month_day_position
          );
        }
        if (values.occurs_month_day) {
          formData.append("occurs_month_day", values.occurs_month_day);
        }
      }

      if (
        values.task_type === "qualitative" ||
        values.task_type === "quantitative_and_qualitative"
      ) {
        if (values.rework_limit) {
          formData.append("rework_limit", JSON.stringify(+values.rework_limit));
        }
        if (values.quality_target_point) {
          formData.append(
            "quality_target_point",
            JSON.stringify(+values.quality_target_point)
          );
        }
      }

      if (
        values.task_type === "quantitative" ||
        values.task_type === "quantitative_and_qualitative"
      ) {
        if (values.quantity_target_point) {
          formData.append(
            "quantity_target_point",
            JSON.stringify(+values.quantity_target_point)
          );
        }
        if (values.quantity_target_unit) {
          formData.append(
            "quantity_target_unit",
            JSON.stringify(+values.quantity_target_unit)
          );
        }
      }

      if (values.turn_around_time_target_point) {
        formData.append(
          "turn_around_time_target_point",
          JSON.stringify(values.turn_around_time_target_point)
        );
      }

      // Submit formData to your API
      console.log("Form data prepared:", formData);
      // axiosInstance.post('/your-endpoint', formData)...
    },
  });

  useEffect(() => {
    if (isReoccuring === false) {
      formik.setFieldValue("routine_option", "once");
      formik.setFieldValue("repeat_every", undefined);
    }
  }, [isReoccuring]);

  useEffect(() => {
    if (endByCalendarIsDisable === true) {
      formik.setFieldValue("end_date", undefined);
    }
  }, [endByCalendarIsDisable]);

  useEffect(() => {
    if (endByOccurrenceIsDisable === true) {
      formik.setFieldValue("after_occurrence", undefined);
    }
  }, [endByOccurrenceIsDisable]);

  useEffect(() => {
    let org_settings = localStorage.getItem("org_info");
    if (org_settings) {
      setCurrentOrgnisationSettings(JSON.parse(org_settings));
    }
    formik.setFieldValue("target_brief", null);
  }, []);

  useEffect(() => {
    if (currentOwner) {
      getInitiatives(currentOwner);
    }
  }, [currentOwner]);

  useEffect(() => {
    if (occursByMonthDayNumber === true) {
      formik.setFieldValue("occurs_month_day_number", undefined);
    }
  }, [occursByMonthDayNumber]);

  useEffect(() => {
    if (occursByMonthPostion === true) {
      formik.setFieldValue("occurs_month_day_position", undefined);
      formik.setFieldValue("occurs_month_day", undefined);
    }
  }, [occursByMonthPostion]);

  return (
    <>
      <Box as="form" onSubmit={formik.handleSubmit}>
        {true ? (
          <>
            <FormControl>
              <FormLabel
                htmlFor="structure_level"
                fontSize="xs"
                fontWeight="500"
              >
                Pick a Structure Level
              </FormLabel>
              <Select
                placeholder="Select Structure Level"
                variant="filled"
                bg="secondary.200"
                color="gray.400"
                id="structure_level"
                onChange={handleLevelChange}
              >
                <option value="corporate-level">Corporate</option>
                <option value="divisional-level">Division</option>
                <option value="group-level">Group</option>
                <option value="departmental-level">Department</option>
                <option value="unit-level">Unit</option>
              </Select>
            </FormControl>
            <br />

            <FormControl>
              <FormLabel
                htmlFor="Structure name"
                fontSize="xs"
                fontWeight="500"
              >
                Structure Name
              </FormLabel>
              <SelectAsyncPaginate
                onPointChange={null}
                key={currentlevel}
                url={`/organization/setup/${currentlevel}/list/${org_name}/?me=1`}
                value={structureLevels}
                onChange={(value: any) => {
                  setStructureLevels(value);
                  return setCurrentEmployueeUUid(value?.uuid);
                }}
                SelectLabel={(option: any) => `${option.name}`}
                SelectValue={(option: any) => option.uuid}
                placeholder={""}
              />
            </FormControl>
          </>
        ) : null}

        <br />

        <HStack mb="5">
          <FormControl id="end_time">
            <FormLabel fontSize="xs" fontWeight="500" htmlFor="end_time">
              Choose Owner
            </FormLabel>
            <SelectAsyncPaginate
              onPointChange={null}
              key={currentEmployueeUUid}
              url={() => {}}
              value={currentOwner}
              onChange={(value: any) => {
                formik.setFieldValue("owner", value?.user.email);
                return setCurrentOwner(value);
              }}
              SelectLabel={(option: any) => `${option?.user.email}`}
              SelectValue={(option: any) => option?.user.email}
              placeholder={""}
            />
            <Text fontSize="xs" color="crimson">
              {formik.errors.initiative_id}
            </Text>
          </FormControl>
        </HStack>
        <br />

        <HStack mb="5">
          <FormControl id="end_time">
            <FormLabel fontSize="xs" fontWeight="500" htmlFor="end_time">
              Upline Initiatives
            </FormLabel>
            <SelectAsyncPaginate
              onPointChange={null}
              key={currentOwner?.user.email}
              url={`/client/${org_name}/initiative/?ownerEmail=${currentOwner?.user.email}&initiative_status=active&initiative_status=pending`}
              value={initiative}
              onChange={(value: any) => {
                formik.setFieldValue("initiative_id", value.initiative_id);
                setInitiative(value);
              }}
              SelectLabel={(option: any) =>
                `${option.name} , Current Running Initiative Routine (${option.routine_round})`
              }
              SelectValue={(option: any) => option?.initiative_id}
              placeholder={""}
            />
            <Text fontSize="xs" color="crimson">
              {formik.errors.initiative_id}
            </Text>
          </FormControl>
        </HStack>
        <br />

        <InputWithLabel
          id="taskName"
          label="Task Name"
          size="lg"
          variant="filled"
          placeholder="Get the Mop Stick"
          bg="secondary.200"
          name="name"
          value={formik.values.name}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          formErrorMessage={formik.errors.name}
          style={{ marginBottom: "10px" }}
        />

        <br />

        <Grid
          gridTemplateColumns="20%  15% 15%  5"
          alignItems="center"
          columnGap={4}
          rowGap={8}
        >
          <Box>
            <Select
              id="task_type"
              name="task_type"
              placeholder={"Task Type"}
              value={formik.values.task_type}
              onChange={(e) => {
                formik.handleChange(e);
                setCurrentTaskType(e.target.value);
              }}
              onBlur={formik.handleBlur}
              required
            >
              <option value={"qualitative"}>Qualitative</option>
              <option value={"quantitative"}>Quantitative</option>
              <option value={"quantitative_and_qualitative"}>
                Quantitative and Qualitative
              </option>
            </Select>
          </Box>

          {(currentTaskType === "qualitative" ||
            currentTaskType === "quantitative_and_qualitative") && (
            <InputWithLabel
              id="quality_target_point"
              name="quality_target_point"
              type="number"
              label="Quality Target Point"
              value={formik.values.quality_target_point}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              formErrorMessage={formik.errors.quality_target_point}
            />
          )}

          {(currentTaskType === "quantitative" ||
            currentTaskType === "quantitative_and_qualitative") && (
            <>
              <InputWithLabel
                id="quantity_target_point"
                name="quantity_target_point"
                type="number"
                label="Quantity Target Point"
                value={formik.values.quantity_target_point}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                formErrorMessage={formik.errors.quantity_target_point}
              />

              <InputWithLabel
                id="quantity_target_unit"
                name="quantity_target_unit"
                type="number"
                label="Quantity Target Unit"
                value={formik.values.quantity_target_unit}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                formErrorMessage={formik.errors.quantity_target_unit}
              />
            </>
          )}

          {(currentTaskType === "qualitative" ||
            currentTaskType === "quantitative_and_qualitative") && (
            <InputWithLabel
              id="rework_limit"
              name="rework_limit"
              label="ReWork Limit"
              size="lg"
              type="number"
              variant="filled"
              bg="secondary.200"
              value={formik.values.rework_limit}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              formErrorMessage={formik.errors.rework_limit}
              style={{ marginBottom: "10px" }}
            />
          )}

          <Box>
            <InputWithLabel
              id="turn_around_time_target_point"
              name="turn_around_time_target_point"
              label="Turn around time target point"
              type="number"
              value={formik.values.turn_around_time_target_point}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            <Text fontSize="xs" color="crimson">
              {formik.errors.turn_around_time_target_point}
            </Text>
          </Box>
        </Grid>
        <Text fontSize="xs" color="crimson">
          {formik.errors.task_type}
        </Text>

        <br />
        <Grid
          gridTemplateColumns="10%  1fr 1fr 1fr"
          alignItems="center"
          columnGap={4}
          rowGap={8}
        >
          <FormLabel fontSize="xs" htmlFor={"d"} fontWeight="500">
            Start Date
          </FormLabel>

          <ExcludeDaysInCalendar
            days_array={[...CurrentOrgnisationSettings.work_days].map(
              (num) => num + 1
            )}
            disabled={false}
            name="start_date"
            value={formik.values.start_date}
            onChange={(date) => formik.setFieldValue("start_date", date)}
            placeholder="Enter Start Date"
            formErrorMessage={
              formik.errors.start_date ? "Start Date Can Not Be empty" : ""
            }
            dateFormat="yyyy/MM/dd"
          />

          <Tooltip
            label={`Work Starts at "${CurrentOrgnisationSettings.work_start_time}",\n\n\n
                    Work Ends at:"${CurrentOrgnisationSettings.work_stop_time}", \n\n\n
                    Break Start Time:" ${CurrentOrgnisationSettings.work_break_start_time},\n\n\n
                    Break ClosingTime :" ${CurrentOrgnisationSettings.work_break_stop_time},\n\n\n
                    \n\n\n
                    Note You Can't Put Any of this time As Start Time
                    `}
          >
            <Box>
              <Input
                type="time"
                name="start_time"
                value={formik.values.start_time}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              <Text fontSize="xs" color="crimson">
                {formik.errors.start_time}
              </Text>
            </Box>
          </Tooltip>
        </Grid>

        <br />

        <Grid
          gridTemplateColumns="9% 1fr 1fr 1fr 1fr"
          alignItems="center"
          columnGap={4}
          rowGap={8}
        >
          <FormLabel fontSize="xs" htmlFor={"d"} fontWeight="500">
            Duration
          </FormLabel>

          <Box>
            <DurationPicker
              onChange={(e) => {
                formik.setFieldValue(
                  "duration",
                  `${e.hours}:${e.minutes}:${e.seconds}`
                );
              }}
            />
            <Text fontSize="xs" color="crimson">
              {formik.errors.duration}
            </Text>
          </Box>
        </Grid>
        <br />

        <Box>
          <Checkbox onChange={() => setIsuploadTaskBrief(!isuploadTaskBrief)}>
            Upload Task Brief
          </Checkbox>
          {isuploadTaskBrief && (
            <Input
              type="file"
              onChange={(event) => {
                if (event.currentTarget.files) {
                  formik.setFieldValue(
                    "target_brief",
                    event.currentTarget.files[0]
                  );
                }
              }}
            />
          )}
        </Box>
        <br />
        <br />
        <br />

        <div style={{ marginRight: "10px", width: "80%", margin: "0 auto" }}>
          <Checkbox onChange={() => setIsReoccuring(!isReoccuring)}>
            Recurring Tasks
          </Checkbox>

          {isReoccuring && (
            <Box>
              <Grid
                gridTemplateColumns="20% 1fr 1fr 1fr"
                alignItems="center"
                columnGap={4}
                rowGap={8}
              >
                <FormLabel fontSize="xs" htmlFor={"d4"} fontWeight="500">
                  Routine Options
                </FormLabel>

                <Box>
                  <Select
                    name="routine_option"
                    value={formik.values.routine_option}
                    onChange={(e) => {
                      formik.handleChange(e);
                      setSelectedRoutine(e.target.value);
                    }}
                    onBlur={formik.handleBlur}
                    placeholder="pick routine"
                    variant="filled"
                    isDisabled={!isReoccuring}
                  >
                    <option value={"daily"}>Daily</option>
                    <option value={"weekly"}>Weekly</option>
                    <option value={"monthly"}>Monthly</option>
                  </Select>
                  <Text fontSize="xs" color="crimson">
                    {formik.errors.routine_option}
                  </Text>
                </Box>
              </Grid>
              <br />

              <Grid
                gridTemplateColumns="20% 1fr 1fr 1fr"
                alignItems="center"
                columnGap={4}
                rowGap={8}
              >
                <FormLabel fontSize="xs" htmlFor={"d41"} fontWeight="500">
                  Repeat every
                </FormLabel>

                <Box>
                  <Select
                    name="repeat_every"
                    value={formik.values.repeat_every}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    id="repeat_every_day"
                    variant="filled"
                    placeholder="pick choice"
                  >
                    {[...new Array(15)].map((_, index) => (
                      <option value={index + 1} key={index * 2}>
                        {index + 1}
                      </option>
                    ))}
                  </Select>
                  <Text fontSize="xs" color="crimson">
                    {formik.errors.repeat_every}
                  </Text>
                </Box>

                <Text>
                  {selectedRoutine === "daily" && "Day"}
                  {selectedRoutine === "weekly" && "week"}
                  {selectedRoutine === "monthly" && "of the month"}
                </Text>
              </Grid>

              {selectedRoutine === "monthly" && (
                <>
                  <br />
                  <Grid
                    gridTemplateColumns="10% 50% 1fr"
                    alignItems="center"
                    columnGap={2}
                    rowGap={8}
                  >
                    <FormLabel fontSize="xs" htmlFor={"d4"} fontWeight="500">
                      Occurs
                    </FormLabel>

                    <Flex align="center">
                      <Radio
                        isDisabled={occursByMonthDayNumber}
                        onClick={() => {
                          if (occursByMonthDayNumber) {
                            setOccursByMonthDayNumber(false);
                            setOccursByMonthPostion(true);
                          }
                        }}
                      >
                        Day
                      </Radio>

                      <Select
                        name="occurs_month_day_number"
                        value={formik.values.occurs_month_day_number}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        id="occurs_month_day_position"
                        variant="filled"
                        isDisabled={occursByMonthDayNumber}
                        width={{ justifyContent: "space-between" }}
                      >
                        {[...new Array(31)].map((_, index) => (
                          <option value={index + 1} key={index * 2}>
                            {index + 1}
                          </option>
                        ))}
                      </Select>

                      <Text>of the month</Text>
                    </Flex>
                  </Grid>

                  <br />
                  <Flex
                    align="center"
                    style={{ justifyContent: "center", width: "75%" }}
                  >
                    <Radio
                      isDisabled={occursByMonthPostion}
                      onClick={() => {
                        if (occursByMonthPostion) {
                          setOccursByMonthPostion(false);
                          setOccursByMonthDayNumber(true);
                        }
                      }}
                    ></Radio>

                    <Select
                      name="occurs_month_day_position"
                      value={formik.values.occurs_month_day_position}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      id="occurs_month_day_position"
                      variant="filled"
                      isDisabled={occursByMonthPostion}
                      width={{ justifyContent: "space-between" }}
                    >
                      <option value={"first"}>First</option>
                      <option value={"second"}>Second</option>
                      <option value={"fourth"}>Fourth</option>
                      <option value={"last"}>Last</option>
                    </Select>

                    <Select
                      marginLeft="10px"
                      name="occurs_month_day"
                      value={formik.values.occurs_month_day}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      id="occurs_month_day"
                      variant="filled"
                      isDisabled={occursByMonthPostion}
                      width={{ justifyContent: "space-between" }}
                    >
                      {CurrentOrgnisationSettings.work_days.includes(6) && (
                        <option value={6}>sunday</option>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(0) && (
                        <option value={0}>monday</option>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(1) && (
                        <option value={1}>tuesday</option>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(2) && (
                        <option value={2}>wednesday</option>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(3) && (
                        <option value={3}>thursday</option>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(4) && (
                        <option value={4}>friday</option>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(5) && (
                        <option value={5}>saturday</option>
                      )}
                    </Select>

                    <Text>of the month</Text>
                  </Flex>
                </>
              )}

              {selectedRoutine === "weekly" && (
                <CheckboxGroup
                  colorScheme="blue"
                  defaultValue={[
                    "monday",
                    "tuesday",
                    "wednesday",
                    "thursday",
                    "friday",
                  ]}
                >
                  <Box style={{ marginTop: "10px" }}>
                    <Text fontSize="xs" color="crimson">
                      {formik.errors.monday}
                    </Text>
                    <Grid
                      gridTemplateColumns="14% 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                      columnGap={1}
                      rowGap={8}
                    >
                      <FormLabel fontSize="xs" fontWeight="500">
                        Work Days
                      </FormLabel>

                      {CurrentOrgnisationSettings.work_days.includes(6) && (
                        <Checkbox
                          name="sunday"
                          isChecked={formik.values.sunday}
                          onChange={formik.handleChange}
                        >
                          Sunday
                        </Checkbox>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(0) && (
                        <Checkbox
                          name="monday"
                          isChecked={formik.values.monday}
                          onChange={formik.handleChange}
                        >
                          Mon
                        </Checkbox>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(1) && (
                        <Checkbox
                          name="tuesday"
                          isChecked={formik.values.tuesday}
                          onChange={formik.handleChange}
                        >
                          Tue
                        </Checkbox>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(2) && (
                        <Checkbox
                          name="wednesday"
                          isChecked={formik.values.wednesday}
                          onChange={formik.handleChange}
                        >
                          Wed
                        </Checkbox>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(3) && (
                        <Checkbox
                          name="thursday"
                          isChecked={formik.values.thursday}
                          onChange={formik.handleChange}
                        >
                          Thu
                        </Checkbox>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(4) && (
                        <Checkbox
                          name="friday"
                          isChecked={formik.values.friday}
                          onChange={formik.handleChange}
                        >
                          Fri
                        </Checkbox>
                      )}
                      {CurrentOrgnisationSettings.work_days.includes(5) && (
                        <Checkbox
                          name="saturday"
                          isChecked={formik.values.saturday}
                          onChange={formik.handleChange}
                        >
                          Sat
                        </Checkbox>
                      )}
                    </Grid>
                  </Box>
                </CheckboxGroup>
              )}

              <br />

              <Grid
                gridTemplateColumns="10% 1fr 1fr"
                alignItems="center"
                columnGap={4}
                rowGap={8}
              >
                <FormLabel fontSize="xs" htmlFor={"d41"} fontWeight="500">
                  End Date
                </FormLabel>

                <Flex>
                  <Radio
                    isDisabled={endByCalendarIsDisable}
                    defaultChecked={!endByCalendarIsDisable}
                    onClick={() => {
                      if (endByCalendarIsDisable) {
                        setEndByCalendarIsDisable(false);
                        setEndByOccurrenceIsDisable(true);
                      }
                    }}
                  >
                    By
                  </Radio>

                  <ExcludeDaysInCalendar
                    days_array={[...CurrentOrgnisationSettings.work_days].map(
                      (num) => num + 1
                    )}
                    required={true}
                    disabled={endByCalendarIsDisable}
                    name="end_date"
                    value={formik.values.end_date}
                    onChange={(date) => formik.setFieldValue("end_date", date)}
                    placeholder="Enter End Date"
                    formErrorMessage={
                      formik.errors.end_date ? "End Date Can Not Be empty" : ""
                    }
                    dateFormat="yyyy/MM/dd"
                  />
                </Flex>

                <Flex align="center">
                  <Radio
                    isDisabled={endByOccurrenceIsDisable}
                    onClick={() => {
                      if (endByOccurrenceIsDisable) {
                        setEndByOccurrenceIsDisable(false);
                        setEndByCalendarIsDisable(true);
                      }
                    }}
                  >
                    After
                  </Radio>

                  <Select
                    name="after_occurrence"
                    value={formik.values.after_occurrence}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isDisabled={endByOccurrenceIsDisable}
                    id="repeat_every_day"
                    variant="filled"
                  >
                    {[...new Array(7)].map((_, index) => (
                      <option value={index + 1} key={index * 2}>
                        {index + 1}
                      </option>
                    ))}
                  </Select>
                  <Text color={endByOccurrenceIsDisable ? "gray.400" : "black"}>
                    occurrences
                  </Text>
                </Flex>
              </Grid>
              <Text
                fontSize="xs"
                color="crimson"
                style={{ textAlign: "center" }}
              >
                {formik.errors.end_date}
              </Text>
            </Box>
          )}
        </div>

        <br />
        <div style={{ textAlign: "center" }}>
          <Button
            type="submit"
            variant="primary"
            loadingText="Creating..."
            isLoading={false} // Replace with your actual loading state
            w="80"
          >
            Submit
          </Button>
        </div>
      </Box>
    </>
  );
};

export default TaskCreateForm;
