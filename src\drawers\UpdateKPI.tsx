import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import React, { useState } from "react";
import { InputField, ReuseableForm } from "../components/custom/form";
import useCalculateEndDateOrOccurrence from "../hooks/useCalculateOccurrenceOrEndDate";
import { useUpdateKPI } from "../hooks/useKPIs";
import { useGetObjectives } from "../hooks/useObjectives";
import { TKPI } from "../types/kpis";
import { RoutineType, TObjective } from "../types/objectives";
import { capitalizeFirst } from "../utils";
import { formatDate } from "../utils/formatDate";
import { KPISchema, routineTypeArray } from "./AddKPI";
import { usePeopleEmails } from "../hooks/usePeople";

const UpdateKPI: React.FC<TKPI> = (kpi) => {
  const { onClose } = useDisclosure();
  const [page, setPage] = useState(1);
  const updateMutation = useUpdateKPI(kpi.id, onClose);
  const { data } = useGetObjectives(page, 50);
  const objectivesOptions = data?.data;
  const objectives = objectivesOptions?.data;
  const peopleList = usePeopleEmails();

  const initialValues = {
    name: kpi.name || localStorage.getItem("name") || "",
    uplineObjective:
      kpi.uplineObjective || localStorage.getItem("uplineObjective") || "",
    uplineProject:
      kpi.uplineProject || localStorage.getItem("uplineProject") || [],
    ownerEmail: kpi.ownerEmail || localStorage.getItem("ownerEmail") || "",
    routineType:
      kpi.routineType ||
      localStorage.getItem("routineType") ||
      RoutineType.ONCE,
    startDate: formatDate(kpi?.startDate?.toString(), "yyyy-MM-dd") || "",
    endDate:
      formatDate(kpi?.endDate?.toString() || "", "yyyy-MM-dd") ||
      localStorage.getItem("endDate") ||
      "",
    afterOccurrence:
      String(kpi.afterOccurrence) ||
      localStorage.getItem("afterOccurrence") ||
      "1",
    brief: "",
  };

  const formik = useFormik({
    validationSchema: KPISchema,
    initialValues,
    onSubmit: async (values, { resetForm }) =>
      updateMutation.mutate({ values, resetForm }),
  });

  useCalculateEndDateOrOccurrence(formik);

  const fields: InputField[] = [
    {
      name: "name",
      type: "text",
      label: "KPI Name",
      placeholder: "Enter KPI name",
      validate: true,
    },
    {
      name: "uplineObjective",
      type: "add-more",
      label: "Upline Objective",
      placeholder: "Select objective",
      addMoreOptions: objectives?.map((objective: TObjective) => ({
        id: objective.id,
        name: capitalizeFirst(objective?.name || ""),
      })),
      addMore: false,
      selected: kpi.uplineObjective,
    },
    {
      name: "uplineProject",
      type: "add-more",
      label: "Upline Project",
      placeholder: "Enter project name (optional)",
      addMore: false,
      selected: kpi.uplineProject,
    },
    {
      name: "ownerEmail",
      helpText:
        "Assign this Key Performance Indicator to an email of your team member/ staff",
      type: "select",
      label: "Owner Email",
      placeholder: "Enter owner's email",
      options: peopleList,
      validate: true,
    },

    {
      type: "grid",
      name: "routineGrid",
      gridCol: 2,
      gridInputs: [
        {
          name: "routineType",
          type: "select",
          label: "Routine Type",
          options: routineTypeArray,
          placeholder: "Select routine type",
          validate: true,
        },
        {
          name: "afterOccurrence",
          type: "number",
          label: "After Occurrence",
          placeholder: "Enter occurrences (optional)",
        },
      ],
    },
    {
      type: "grid",
      name: "dateGrid",
      gridCol: 2,
      gridInputs: [
        {
          name: "startDate",
          type: "date",
          label: "Start Date",
          placeholder: "Pick a start date",
          validate: true,
        },
        {
          name: "endDate",
          type: "date",
          label: "End Date",
          placeholder: "Pick an end date (optional)",
        },
      ],
    },
    {
      name: "brief",
      type: "upload",
      label: "Brief",
      placeholder: "Upload a brief description about the KPI",
      validate: true,
    },
  ];
  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update KPI
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="button"
          variant="primary"
          w="full"
          fontSize="small"
          onClick={formik.submitForm}
          isLoading={updateMutation.isPending}
          loadingText="Updating KPI..."
        >
          Update KPI
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UpdateKPI;
