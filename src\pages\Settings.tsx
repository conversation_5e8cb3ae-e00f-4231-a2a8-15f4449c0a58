import { <PERSON>, Tab, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, Tabs } from "@chakra-ui/react";
import { GrGroup } from "react-icons/gr";
import AppBar from "../components/AppBar";
import { useCheckUserType } from "../hooks/useCheckUserType";
import EmployeeFileStructure from "../tabs/settings/EmployeeFileStructure";
import LoggedInEmployeeDetails from "../tabs/settings/LoggedInEmployeeDetails";

const Settings = () => {
  const isSuperAdminOrAdmin = useCheckUserType(["employer", "hr"]);
  const isTeamLeadOrEmployee = useCheckUserType([
    "teamLead",
    "employee",
    "employer",
  ]);

  return (
    <>
      <AppBar heading="Settings" />

      <Tabs colorScheme="primary" isLazy>
        <TabList>
          {isSuperAdminOrAdmin && (
            <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
              <Box as="span" mr="2">
                <GrGroup size="22px" />
              </Box>
              Setting Up Employee Files Structure
            </Tab>
          )}
          {isTeamLeadOrEmployee && (
            <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
              <Box as="span" mr="2">
                <GrGroup size="22px" />
              </Box>
              Employee Profile
            </Tab>
          )}
        </TabList>

        <TabPanels pt="3">
          {isSuperAdminOrAdmin && (
            <TabPanel px="0">
              <EmployeeFileStructure />
            </TabPanel>
          )}
          {isTeamLeadOrEmployee && (
            <TabPanel>
              <LoggedInEmployeeDetails />
            </TabPanel>
          )}
        </TabPanels>
      </Tabs>
    </>
  );
};

export default Settings;
