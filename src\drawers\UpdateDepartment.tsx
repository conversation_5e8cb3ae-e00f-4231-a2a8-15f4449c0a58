import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useToast,
} from "@chakra-ui/react";
import { Field, Form, Formik } from "formik";
import { useState } from "react";
import * as yup from "yup";
import InputWithLabel from "../components/InputWithLabel";

interface UpdateDepartmentInputs {
  name: string;
}

interface UpdateDepartmentProps {
  department: UpdateDepartmentInputs;
}

const schema = yup.object().shape({
  name: yup.string().required("Name is required"),
});

const UpdateDepartment: React.FC<UpdateDepartmentProps> = ({ department }) => {
  const [localDepartment, setLocalDepartment] =
    useState<UpdateDepartmentInputs>(department);
  const toast = useToast();
  const ORG_NAME = localStorage.getItem("current_organization_short_name");

  const handleSubmit = (
    values: UpdateDepartmentInputs,
    { setSubmitting }: { setSubmitting: (isSubmitting: boolean) => void }
  ) => {
    console.log(values.name, "the rsh");
    if (ORG_NAME) {
      console.log("org");
    }
    setSubmitting(false);
  };

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Department
      </DrawerHeader>
      <DrawerBody>
        <Formik
          initialValues={{ name: localDepartment.name || "" }}
          validationSchema={schema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, errors, touched }) => (
            <Form id="add-department-form">
              <Field name="name">
                {({ field }: { field: any }) => (
                  <InputWithLabel
                    id="name"
                    label="Name of Department"
                    variant="filled"
                    bg="secondary.200"
                    mb="5"
                    formErrorMessage={
                      touched.name && errors.name ? errors.name : ""
                    }
                    {...field}
                  />
                )}
              </Field>
            </Form>
          )}
        </Formik>
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          form="add-department-form"
          variant="primary"
          w="full"
          size="sm"
          isLoading={false} // Formik's isSubmitting can be used
          loadingText="Updating Please Wait..."
        >
          Update
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UpdateDepartment;
