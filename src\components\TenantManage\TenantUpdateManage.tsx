import { But<PERSON> } from "@chakra-ui/react";
import { Field, FieldProps, Form, Formik } from "formik";
import { useEffect, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import * as yup from "yup";
import InputWithLabel from "../InputWithLabel";
import Preloader from "../Preloader";

export type TenantUpdateFormType = {
  name_of_account_manager: null | string;
  tel_of_account_manager: null | string;
  email_of_account_manager: null | string;
  name_of_account_HRmanager: null | string;
  tel_of_account_HRmanager: null | string;
  email_of_account_HRmanager: null | string;
  poc: number;
  company_short_name: string;
  employee_limit: number;
};

type Prop = {
  data: any;
};

const schema = yup.object({
  name_of_account_manager: yup.string(),
  tel_of_account_manager: yup.string(),
  email_of_account_manager: yup.string().email("Must be a valid email"),
  name_of_account_HRmanager: yup.string(),
  tel_of_account_HRmanager: yup.string(),
  email_of_account_HRmanager: yup.string().email("Must be a valid email"),
  poc: yup.number().required("POC is required"),
  employee_limit: yup.number().required("Employee limit is required"),
  company_short_name: yup.string(),
});

const TenantUpdateManage = ({ data }: Prop) => {
  const { showBoundary } = useErrorBoundary();
  const [status, setStatus] = useState<string>("");
  const [initialValues, setInitialValues] = useState<TenantUpdateFormType>({
    name_of_account_manager: "",
    tel_of_account_manager: "",
    email_of_account_manager: "",
    name_of_account_HRmanager: "",
    tel_of_account_HRmanager: "",
    email_of_account_HRmanager: "",
    poc: 0,
    company_short_name: "",
    employee_limit: 0,
  });

  useEffect(() => {
    setInitialValues({
      name_of_account_manager: data.name_of_account_manager || "",
      tel_of_account_manager: data.tel_of_account_manager || "",
      email_of_account_manager: data.email_of_account_manager || "",
      name_of_account_HRmanager: data.name_of_account_HRmanager || "",
      tel_of_account_HRmanager: data.tel_of_account_HRmanager || "",
      email_of_account_HRmanager: data.email_of_account_HRmanager || "",
      poc: data.poc || 0,
      employee_limit: data.employee_limit || 0,
      company_short_name: data.company_short_name || "",
    });
  }, [data]);

  const onSubmit = (values: TenantUpdateFormType) => {
    setStatus("loading");
    try {
      // Your submission logic here
      console.log(values);
      setStatus("");
    } catch (error) {
      showBoundary(error);
      setStatus("");
    }
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={schema}
      onSubmit={onSubmit}
      enableReinitialize
    >
      {({ isSubmitting }) => (
        <Form>
          {status === "loading" && <Preloader />}
          <Field name="poc">
            {({ field, form }: FieldProps) => (
              <InputWithLabel
                label="Poc"
                id="poc"
                {...field}
                formErrorMessage={
                  typeof form.errors.poc === "string"
                    ? form.errors.poc
                    : undefined
                }
              />
            )}
          </Field>

          <Field name="name_of_account_manager">
            {({ field, form }: FieldProps) => (
              <InputWithLabel
                label="Account Manager Name"
                id="acct_name"
                {...field}
                formErrorMessage={
                  typeof form.errors.name_of_account_manager === "string"
                    ? form.errors.name_of_account_manager
                    : undefined
                }
              />
            )}
          </Field>

          <Field name="tel_of_account_manager">
            {({ field, form }: FieldProps) => (
              <InputWithLabel
                label="Account Manager Tel"
                id="acct_tel"
                {...field}
                formErrorMessage={
                  typeof form.errors.tel_of_account_manager === "string"
                    ? form.errors.tel_of_account_manager
                    : undefined
                }
              />
            )}
          </Field>

          <Field name="email_of_account_manager">
            {({ field, form }: FieldProps) => (
              <InputWithLabel
                label="Account Manager Email"
                id="acct_email"
                type="email"
                {...field}
                formErrorMessage={
                  typeof form.errors.email_of_account_manager === "string"
                    ? form.errors.email_of_account_manager
                    : undefined
                }
              />
            )}
          </Field>

          <Field name="employee_limit">
            {({ field, form }: FieldProps) => (
              <InputWithLabel
                label="Number of Users"
                id="acct_users"
                type="number"
                {...field}
                formErrorMessage={
                  typeof form.errors.employee_limit === "string"
                    ? form.errors.employee_limit
                    : undefined
                }
              />
            )}
          </Field>

          <Field name="name_of_account_HRmanager">
            {({ field, form }: FieldProps) => (
              <InputWithLabel
                label="Hr Manager Name"
                id="hr_name"
                {...field}
                formErrorMessage={
                  typeof form.errors.name_of_account_HRmanager === "string"
                    ? form.errors.name_of_account_HRmanager
                    : undefined
                }
              />
            )}
          </Field>

          <Field name="tel_of_account_HRmanager">
            {({ field, form }: FieldProps) => (
              <InputWithLabel
                label="Hr Manager Tel"
                id="hr_tel"
                {...field}
                formErrorMessage={
                  typeof form.errors.tel_of_account_HRmanager === "string"
                    ? form.errors.tel_of_account_HRmanager
                    : undefined
                }
              />
            )}
          </Field>

          <Field name="email_of_account_HRmanager">
            {({ field, form }: FieldProps) => (
              <InputWithLabel
                label="Hr Manager Email"
                id="hr_email"
                type="email"
                {...field}
                formErrorMessage={
                  typeof form.errors.email_of_account_HRmanager === "string"
                    ? form.errors.email_of_account_HRmanager
                    : undefined
                }
              />
            )}
          </Field>

          <br />
          <br />

          <Button
            type="submit"
            variant="primary"
            fontWeight="500"
            isLoading={isSubmitting || status === "loading"}
            loadingText="Please Wait"
            size="lg"
          >
            Submit
          </Button>
        </Form>
      )}
    </Formik>
  );
};

export default TenantUpdateManage;
