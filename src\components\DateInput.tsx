import { Box, Text } from "@chakra-ui/react";
import { Field, FieldProps, FormikErrors } from "formik";
import { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

interface ExcludeDaysInCalendarProp {
  days_array: number[];
  name: string;
  placeholder?: string;
  formErrorMessage?: string;
  dateFormat: string;
  disabled?: boolean;
  required?: boolean;
  defaultValue?: Date;
  value?: string;
  onChange?: (date: string) => Promise<void | FormikErrors<{
    name: string;
    ownerEmail: string;
    routine_option: string;
    upline_objective_id: string;
    upline_initiative_id: string;
    start_time: string;
    end_time: string;
    after_occurrence: undefined;
    initiative_brief: null;
    initiative_id: string;
    recorded_allowance: number;
    start_date: string;
    end_date: string;
    leave_type_id: string;
    duration: number;
    deputizing_officer: string;
    hand_over_report: null;
  }>>;
}

export const ExcludeDaysInCalendar: React.FC<ExcludeDaysInCalendarProp> = ({
  days_array = [],
  name,
  placeholder = "Enter Date",
  formErrorMessage,
  dateFormat,
  disabled = false,
  required = true,
  defaultValue = new Date(),
  value,
  onChange,
}) => {
  const [startDate, setStartDate] = useState<Date | null>(null);

  const isWeekday = (date: Date) => {
    const day = date.getDay();
    return days_array.includes(day);
  };

  return (
    <Box>
      <Field name={name}>
        {({ field, form }: FieldProps<Date>) => (
          <DatePicker
            onChange={(date: Date | null) => {
              form.setFieldValue(name, date);
              setStartDate(date);
            }}
            disabled={disabled}
            required={required}
            selected={field.value || defaultValue}
            filterDate={isWeekday}
            placeholderText={placeholder}
            minDate={new Date()}
            dateFormat={dateFormat}
            className="chakra-input css-gzp9gs"
          />
        )}
      </Field>

      <Text fontSize="xs" color="crimson">
        {formErrorMessage}
      </Text>
    </Box>
  );
};
