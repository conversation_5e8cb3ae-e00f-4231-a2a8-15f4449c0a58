import { Box, Button, Grid } from "@chakra-ui/react";
import { Field, FieldProps, Form, Formik } from "formik";
import { useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import { toast } from "sonner";
import * as yup from "yup";
import axios from "../api";
import errorMessageGetter from "../utils/errorMessages";
import InputWithLabel from "./InputWithLabel";
import Preloader from "./Preloader";

type CreateHrAdminForm = {
  first_name: string;
  last_name: string;
  phone_number: string;
  email: string;
  password: string;
  confirm_password: string;
};

const validationSchema = yup.object().shape({
  first_name: yup.string().required("First Name is required"),
  last_name: yup.string().required("Last Name is required"),
  phone_number: yup.string().required("Phone Number is required"),
  email: yup
    .string()
    .email("Must be email format")
    .required("Email is required"),
  password: yup.string().required("Password is required").min(4),
  confirm_password: yup
    .string()
    .min(8)
    .oneOf(
      [yup.ref("password"), undefined],
      "Confirm Password Must Match Password"
    ),
});

const initialValues: CreateHrAdminForm = {
  first_name: "",
  last_name: "",
  phone_number: "",
  email: "",
  password: "",
  confirm_password: "",
};

const CreateHrAdmin = (): React.ReactElement => {
  const [isLoading, setIsLoading] = useState(false);
  const { showBoundary } = useErrorBoundary();

  const handleSubmit = async (values: CreateHrAdminForm) => {
    console.log({ "submitted data": values });
    const org_name = localStorage.getItem("current_organization_short_name");
    if (!org_name) return;
    setIsLoading(true);

    try {
      const resp = await axios.post(`/auth/register/admin/`, {
        ...values,
        organisation_short_name: org_name,
      });

      if (resp.data.status === 201) {
        console.log({ created: resp });
        toast.success("Created Successfully, please check your email");
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    } catch (err: any) {
      console.log(err.response);
      if (err.response.status === 403) {
        console.log({ "you don't have permission to this function": err });
        toast.error("You don't have permission to this function");
      } else if (err.response.status === 400) {
        console.log({ err: errorMessageGetter(err.response.data) });
        toast.error(errorMessageGetter(err.response.data));
      } else if (err.response.status === 401) {
        console.log({ "token expired": err });
        showBoundary(err);
      }
    }
    setIsLoading(false);
  };

  const inputFields = [
    { id: "first_name", label: "First Name", placeholder: "Nwokolo" },
    { id: "last_name", label: "Last Name", placeholder: "Matthew" },
    {
      id: "phone_number",
      label: "Phone Number",
      placeholder: "Please Input correct Phone Number",
    },
    { id: "email", label: "Email", placeholder: "Please Input correct Email" },
    {
      id: "password",
      label: "Password",
      placeholder: "Please Input correct Password",
    },
    {
      id: "confirm_password",
      label: "Confirm Password",
      placeholder: "Please Input correct Password",
    },
  ];

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {({ errors, touched }) => (
        <>
          {isLoading && <Preloader />}
          <Form>
            <Box style={{ width: "100%" }}>
              <Grid gridTemplateColumns="1f" columnGap={4} rowGap={8} mb="10">
                {inputFields.map(({ id, label, placeholder }) => (
                  <Field name={id} key={id}>
                    {({ field }: FieldProps) => (
                      <InputWithLabel
                        id={id}
                        label={label}
                        size="lg"
                        variant="filled"
                        placeholder={placeholder}
                        bg="secondary.200"
                        {...field}
                        formErrorMessage={
                          errors[id as keyof CreateHrAdminForm] &&
                          touched[id as keyof CreateHrAdminForm]
                            ? errors[id as keyof CreateHrAdminForm]
                            : undefined
                        }
                      />
                    )}
                  </Field>
                ))}
              </Grid>
              <Button
                fontWeight="500"
                variant="primary"
                type="submit"
                isLoading={isLoading}
                loadingText="Creating..."
              >
                Create Hr Admin
              </Button>
            </Box>
          </Form>
        </>
      )}
    </Formik>
  );
};

export default CreateHrAdmin;