import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect } from "react";
import { InputField, ReuseableForm } from "../components/custom/form";
import { useUpdateStructuralLevel } from "../hooks/useStructure";
import { StructureSchema } from "../schemas/structure";
import { TStructuralLevel } from "../types/structuralLevel";

const UpdateStructure = ({ structure }: { structure: TStructuralLevel }) => {
  const { onClose } = useDisclosure();
  const updateMutation = useUpdateStructuralLevel(structure.id, onClose);

  const initialValues = {
    name: structure?.name || "",
    tiers: structure?.tiers || [],
    description: structure?.description || "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: StructureSchema,
    onSubmit: (values, { resetForm }) =>
      updateMutation.mutate({
        values: { ...values, priority: structure.priority },
        resetForm,
      }),
  });

  const fields: InputField[] = [
    {
      name: "name",
      type: "text",
      label: "Structure Name",
      placeholder: "Enter structure name",
      validate: true,
    },
    {
      name: "description",
      type: "textarea",
      label: "Description",
      placeholder: "Enter structure description (optional)",
      validate: false,
    },
    {
      name: "tiers",
      type: "selection",
      label: "Tier Names",
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Structure
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          onClick={formik.submitForm}
          variant="primary"
          w="full"
          fontSize="sm"
          isLoading={updateMutation.isPending}
          loadingText="Updating structure..."
        >
          Update Structure
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UpdateStructure;
