import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@chakra-ui/react";
import { Formik, Form, Field } from "formik";
import * as yup from "yup";
import InputWithLabel from "../components/InputWithLabel";

export interface AddTeamInputs {
  desgination: string;
  team: string;
}

const validationSchema = yup.object().shape({
  desgination: yup.string().required("Designation is required"),
  team: yup.string().required("Team is required"),
});

const AddTeam = () => {
  const onSubmit = (values: AddTeamInputs) => {
    console.log(values);
  };

  return (
    <Formik
      initialValues={{
        desgination: "",
        team: "",
      }}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      {({ errors, touched }) => (
        <>
          <DrawerCloseButton />
          <DrawerHeader>Add Team</DrawerHeader>
          <DrawerBody>
            <Form id="add-team-form">
              <Field name="desgination">
                {({ field }: any) => (
                  <InputWithLabel
                    id="desgination"
                    label="Designation"
                    variant="filled"
                    bg="secondary.200"
                    {...field}
                    formErrorMessage={touched.desgination && errors.desgination}
                    mb="5"
                  />
                )}
              </Field>

              <Field name="team">
                {({ field }: any) => (
                  <InputWithLabel
                    id="team"
                    label="Team"
                    variant="filled"
                    bg="secondary.200"
                    {...field}
                    formErrorMessage={touched.team && errors.team}
                    mb="5"
                  />
                )}
              </Field>
            </Form>
          </DrawerBody>
          <DrawerFooter>
            <Button
              type="submit"
              form="add-team-form"
              variant="primary"
              w="full"
            >
              Add Team
            </Button>
          </DrawerFooter>
        </>
      )}
    </Formik>
  );
};

export default AddTeam;