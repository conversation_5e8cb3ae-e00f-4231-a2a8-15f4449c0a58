import axiosInstance from "../api";
import { isObject } from "../services/extraFunctions";
import { errorMessageForFileUpload } from "./errorMessages";
import { getSession } from "./session";

class UploadDocumentHandler {
  constructor(
    toast,
    url,
    typeOfUpload,
    selectedFile,
    isFilePicked,
    setSelectedFile,
    setIsFilePicked,
    handleError,
    onClose
  ) {
    this.toast = toast;
    this.url = url;
    this.typeOfUpload = typeOfUpload;
    this.isFilePicked = isFilePicked;
    this.selectedFile = selectedFile;
    this.setIsFilePicked = setIsFilePicked;
    this.setSelectedFile = setSelectedFile;
    this.handleError = handleError;
    this.onClose = onClose;
  }

  handleSubmit(event) {
    event.preventDefault();

    if (this.isFilePicked && this.selectedFile) {
      if (
        this.selectedFile.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        this.selectedFile.type === "application/vnd.ms-excel"
      ) {
        const formData = new FormData();
        formData.append("file", this.selectedFile);

        let formattedUrl = this.url;
        if (this.typeOfUpload === "dashboard") {
          formattedUrl = `dashboard/${this.url}`;
        }

        this.toast
          .promise(
            axiosInstance.post(formattedUrl, formData, {
              headers: {
                Authorization: "Bearer " + getSession(),
                "Content-Type": "multipart/form-data",
              },
            }),
            {
              loading: "Parsing file, please wait...",
              success: (response) => {
                this.onClose?.();
                return response.data.message || "Uploaded Successfully";
              },
              error: (error) =>
                error?.response?.data?.error ||
                "An error occurred while bulk uploading files",
            }
          )
          .then((res) => {
            if (res.success) this.onClose?.();
            this.setSelectedFile(undefined);
          })
          .catch((err) => {
            if (err.response.status === 401) {
              this.handleError(err);
            }

            if (err.response.status === 403) {
              this.toast.error(
                "You do not have permission to perform this action. This feature is not available for your user type"
              );
            }
            if (err.response.status === 500) {
              this.toast.error(
                "This was an unexpected server error. Please contact the development team"
              );
            }
            for (let errorObj of errorMessageForFileUpload(err.response)) {
              if (errorObj) {
                this.toast.error(
                  isObject(errorObj.message) === true
                    ? errorObj.message?.name
                    : errorObj.message
                );
              }
            }
          });
      } else {
        this.toast.error("File must be an excel document");
      }
    }
  }

  handleFileChange = (event) => {
    const files = event.target.files;

    if (!files[0]) return;
    this.setSelectedFile(files[0]);
    this.setIsFilePicked(true);
  };
}

export default UploadDocumentHandler;
