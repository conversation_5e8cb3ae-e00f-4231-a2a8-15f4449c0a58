import { toast } from "sonner";
import { TStructuralLevel } from "../types/structuralLevel";

export const structuralLevelIndexKey = "stepperIndex";

export const defaultStructuralLevel = {
  Corporate: "corporate",
  Division: "division",
  Group: "group",
  Department: "department",
  Unit: "unit",
};

export const defaultStructuralLevelArray = Array.from(
  Object.values(defaultStructuralLevel)
);

export const getCustomStructureLevel = (
  allStructureLevel: TStructuralLevel[]
) => {
  const structure = allStructureLevel?.filter(
    (sl) => !defaultStructuralLevelArray.includes(sl.name.toLowerCase())
  );

  return structure || [];
};

export const getDefaultStructureLevel = (
  allStructureLevel: TStructuralLevel[]
) => {
  const structure = allStructureLevel?.filter((sl) =>
    defaultStructuralLevelArray.includes(sl.name.toLowerCase())
  );

  return structure || [];
};

export const checkIfStructureExists = (
  name: string,
  allStructuralLevel: TStructuralLevel[]
) => {
  const exists = allStructuralLevel.find(
    (sl) => sl.name.trim().toLowerCase() === name.trim().toLowerCase()
  );

  return exists;
};

export const checkIfTierExists = (
  name: string,
  tiers?: string[]
) => {
  if (!tiers) return false;
  const matched = tiers?.includes(name.trim().toLowerCase()) ?? false;

  if (matched) {
    toast.error(`${name} level name already exists.`)

  }
  
  return matched;
};
