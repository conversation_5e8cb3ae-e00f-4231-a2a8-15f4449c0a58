import { Button } from "@chakra-ui/button";
import { Link, useLocation } from "react-router-dom";
import { goToLogin } from "../api";

function ErrorLoginButton() {
  const { pathname } = useLocation();
  return (
    <Link to={"/?next=" + pathname}>
      <Button onClick={goToLogin} variant="primary" fontWeight="500" size="sm">
        Re-login
      </Button>
    </Link>
  );
}

export default ErrorLoginButton;

export function RefreshButton() {
  return (
    <Button
      variant="primary"
      fontWeight="500"
      size="sm"
      onClick={() => {
        localStorage.clear();
        window.location.reload();
      }}
    >
      Refresh
    </Button>
  );
}
