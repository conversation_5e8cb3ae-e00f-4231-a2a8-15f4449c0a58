import { Box } from "@chakra-ui/layout";
import { Route } from "react-router-dom";
import Layout from "../../components/serviceAccount/Layout";

import {organizationRoutes} from "../../routes";

const ServiceAccountDashboard = () => {
  return (
    <Box overflowX="hidden">
      <Layout>
        <>
          <>
            {organizationRoutes.map((route) => (
              <Route key={route.path} {...route} />
            ))}
          </>
        </>
      </Layout>
    </Box>
  );
};

export default ServiceAccountDashboard;
