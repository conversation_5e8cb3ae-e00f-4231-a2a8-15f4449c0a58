import {
  Box,
  Stack,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
} from "@chakra-ui/react";
import { useEffect, useState } from "react";
import { HiOutlinePlus } from "react-icons/hi";
import { RiSendPlaneFill } from "react-icons/ri";
import { toast } from "sonner";
import axiosInstance from "../api";
import CreateHrAdminDrawer from "../drawers/CreateHrAdminDrawer";
import CustomDrawer from "../drawers/CustomDrawer";
import { UpdateHrAdminDrawer } from "../drawers/UpdateHrAdminDrawer";
import Preloader from "./Preloader";

const HrAdminTable = (): React.ReactElement => {
  const [isLoading, setIsLoading] = useState(false);
  const orgName = localStorage.getItem("current_organization_short_name");
  const [hrAdmins, setHrAdmins] = useState<any[]>([]);

  const fetchHrAdmins = async () => {
    setIsLoading(true);
    try {
      const response = await axiosInstance.get(
        `auth/register/admin/?organisation_short_name=${orgName}`
      );
      if (response.status === 200) {
        setHrAdmins(response.data.data);
      }
    } catch (error) {
      toast.error("Some Error Occurred, please refresh");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchHrAdmins();
  }, []);

  return (
    <Box>
      {isLoading && <Preloader />}

      <Stack direction="row" spacing={4} justifyContent="space-between">
        <Text as="small" fontWeight="500"></Text>
        <CustomDrawer
          showModalBtnText="Add New Hr Admin"
          showModalBtnVariant="primary"
          showModalBtnColor="white"
          leftIcon={<HiOutlinePlus />}
        >
          <CreateHrAdminDrawer />
        </CustomDrawer>
      </Stack>
      <br />

      <Table size="sm" variant="striped" borderRadius="lg" overflow="hidden">
        <Thead bg="gray.100" style={{ textTransform: "capitalize" }}>
          <Tr>
            <Th py="3">First Name</Th>
            <Th py="3">Last Name</Th>
            <Th py="3">Phone Number</Th>
            <Th py="3">Email</Th>
            <Th py="3"></Th>
          </Tr>
        </Thead>

        <Tbody>
          {hrAdmins.map((admin, index) => (
            <Tr key={index}>
              <Td>{admin.first_name}</Td>
              <Td>{admin.last_name}</Td>
              <Td>{admin.phone_number}</Td>
              <Td>{admin.email}</Td>
              <Td>
                <CustomDrawer
                  showModalBtnText=""
                  showModalBtnVariant="primary"
                  showModalBtnColor="white"
                  leftIcon={<RiSendPlaneFill />}
                  drawerSize="md"
                >
                  <UpdateHrAdminDrawer {...admin} />
                </CustomDrawer>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};

export default HrAdminTable;
