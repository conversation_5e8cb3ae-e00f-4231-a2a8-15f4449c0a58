import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect, useMemo } from "react";

import { InputField, ReuseableForm } from "../components/custom/form";
import { useAddDesignation } from "../hooks/useDesignation";
import { designationSchema } from "./UpdateDesignation";
import { useOrganizationStructure } from "../hooks/organization";

const AddDesignation = () => {
  const { onClose } = useDisclosure();
  const addMutation = useAddDesignation(onClose);

  const structureLvldata = useOrganizationStructure();
  const structureLvls = useMemo(() => {
    const levels = structureLvldata;

    const structureLevelNames = levels?.map((item: any) => {
      return { name: item.name, id: item.id };
    });
    const structureLevelTiersMap = levels?.reduce((acc: any, item: any) => {
      acc[item?.name] = item?.tiers;
      return acc;
    }, {} as Record<string, string[]>);

    return { levels: structureLevelNames, tiers: structureLevelTiersMap };
  }, [structureLvldata]);

  const initialValues = {
    name: localStorage.getItem("name") || "",
    structuralLevel: localStorage.getItem("structuralLevel") || [],
    description: localStorage.getItem("description") || "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: designationSchema,
    onSubmit: async (values, { resetForm }) =>
      addMutation.mutate({ values, resetForm }),
  });

  const fields: InputField[] = [
    {
      name: "name",
      type: "text",
      label: "Designation Name",
      placeholder: "Enter designation name",
      validate: true,
    },
    {
      name: "structuralLevel",
      type: "select",
      label: "Structural Level",
      placeholder: "Select Organization structure",
      validate: true,
      addMoreOptions: structureLvls?.levels,
    },
    {
      name: "description",
      type: "textarea",
      label: "Designation Description ",
      placeholder: "Enter a brief description of this designation",
      validate: true,
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Add Job Designation
      </DrawerHeader>

      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          variant="primary"
          w="full"
          size="sm"
          isLoading={addMutation.isPending}
          onClick={formik.submitForm}
          loadingText="Adding designation..."
        >
          Add Designation
        </Button>
      </DrawerFooter>
    </>
  );
};

export default AddDesignation;
