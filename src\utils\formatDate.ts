import { addMinutes, format, isValid, parse } from "date-fns";

export function formatDate(dateString: string, formatType: string): string {
  const date = new Date(dateString);

  // Validate the input date
  if (!isValid(date)) {
    return "Invalid Date";
  }

  try {
    return format(date, formatType);
  } catch (error) {
    console.log("error while formatting date " + error);

    return "Invalid Format";
  }
}

export const calculateEndTime = (startTime: string, duration: number) => {
  if (!startTime || !duration) return "";
  const parsedStart = parse(startTime, "HH:mm", new Date());
  const endTime = addMinutes(parsedStart, duration);
  return format(endTime, "HH:mm");
};

export function convertTo24Hour(time12h: string): string {
  const [time, modifier] = time12h.split(" ");

  let [hours, minutes] = time.split(":").map(Number);

  if (modifier.toLowerCase() === "pm" && hours !== 12) {
    hours += 12;
  }
  if (modifier.toLowerCase() === "am" && hours === 12) {
    hours = 0;
  }

  const newTime = `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
    2,
    "0"
  )} ${modifier.toUpperCase()}`;

  return newTime;
}
