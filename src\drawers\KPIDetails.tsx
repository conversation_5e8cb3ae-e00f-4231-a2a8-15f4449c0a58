import {
  Box,
  DrawerBody,
  Drawer<PERSON>lose<PERSON>utton,
  <PERSON>er<PERSON><PERSON>er,
  Flex,
  Grid,
  <PERSON>ing,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  Badge,
  Progress,
} from "@chakra-ui/react";
import { HiOutlineChevronLeft } from "react-icons/hi";
import { useCurrentOrganization } from "../hooks/organization";
import { TOrganization } from "../types/organization";
import { TSpread, TObjective, Status } from "../types/objectives";
import { formatDate } from "../utils/formatDate";
import { JSX } from "react";
import { TKPI } from "../types/kpis";
import { DocumentLink } from "../components/DocumentLink";

const KPIDetails = ({ kpi }: { kpi: TKPI }) => {
  return (
    <>
      <DrawerHeader fontWeight="500" w="95%" mx="auto" mt="5">
        <Flex justify="space-between" alignItems="center">
          <Flex alignItems="baseline">
            <DrawerCloseButton
              as={HiOutlineChevronLeft}
              display="block"
              position="relative"
            />
            <Text mt={-1} ml={5} fontSize="lg">
              KPI Details
            </Text>
          </Flex>
        </Flex>
      </DrawerHeader>
      <DrawerBody w="100%" mx="auto">
        <Tabs colorScheme="primary">
          <TabList>
            <Tab fontWeight="500" color="gray.600" mr={8}>
              Basic Information
            </Tab>
            <Tab fontWeight="500" color="gray.600">
              Objectives & Projects
            </Tab>
            <Tab fontWeight="500" color="gray.600">
              Progress Details
            </Tab>
          </TabList>
          <TabPanels>
            <TabPanel>
              <KPIBasicInfo {...kpi} />
            </TabPanel>
            <TabPanel>
              <KPIObjectivesInfo {...kpi} />
            </TabPanel>
            <TabPanel>
              <KPIProgressInfo {...kpi} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </DrawerBody>
    </>
  );
};

export default KPIDetails;

const DetailItem: React.FC<{
  label: string;
  value: string | number | JSX.Element;
}> = ({ label, value }) => (
  <Box mb="4">
    <Text fontSize="smaller" color="gray.500">
      {label}
    </Text>
    <Text>{value || "N/A"}</Text>
  </Box>
);

const StatusBadge: React.FC<{ status: Status }> = ({ status }) => {
  let colorScheme;

  switch (status) {
    case Status.PENDING:
      colorScheme = "gray";
      break;
    case Status.ACTIVE:
      colorScheme = "blue";
      break;
    case Status.CLOSE:
      colorScheme = "green";
      break;
    default:
      colorScheme = "gray";
  }

  return <Badge colorScheme={colorScheme}>{status.replace(/_/g, " ")}</Badge>;
};

const KPIBasicInfo = (props: TKPI) => {
  let { data: currentOrganization } = useCurrentOrganization();
  currentOrganization = currentOrganization?.data as TOrganization;

  return (
    <Box>
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <DetailItem label="KPI Name" value={props.name} />
        <DetailItem
          label="Status"
          value={<StatusBadge status={props.status} />}
        />
        <DetailItem
          label="Start Date"
          value={formatDate(
            props.startDate,
            currentOrganization?.primaryDateFormat
          )}
        />
        <DetailItem
          label="End Date"
          value={
            props.endDate
              ? formatDate(
                  props.endDate,
                  currentOrganization?.primaryDateFormat
                )
              : "N/A"
          }
        />
        <DetailItem
          label="Routine Type"
          value={props.routineType.replace(/_/g, " ")}
        />
        <DetailItem label="After Occurrence" value={props.afterOccurrence} />
        <DetailItem label="Owner Email" value={props.ownerEmail} />
        <DetailItem label="Created By" value={props.createdBy} />
      </Grid>

      <DetailItem
        label="Brief"
        value={
          props.brief ? (
            <>
              <Box h={2} />
              <DocumentLink url={props.brief || ""} fileName="KPI Brief" />
            </>
          ) : (
            "N/A"
          )
        }
      />
    </Box>
  );
};

const KPIObjectivesInfo = (props: TKPI) => {
  return (
    <Box>
      <Heading as="h5" size="sm" color="primary" mb="4">
        Related Objectives
      </Heading>
      {props.uplineObjective && props.uplineObjective.length > 0 ? (
        props.uplineObjective.map((objective: TSpread, index: number) => (
          <Box key={index} mb="4" p="3" borderWidth="1px" borderRadius="md">
            <Flex justifyContent="space-between" alignItems="center">
              <Box>
                <Text fontWeight="500">{objective.name}</Text>
                {/* <Text fontSize="sm" color="gray.600">
                  ID: {objective.id}
                </Text> */}
              </Box>
              <Flex direction="column" alignItems="flex-end">
                <Text fontSize="sm" fontWeight="500">
                  Relative Point: {objective.relativePoint}
                </Text>
              </Flex>
            </Flex>
          </Box>
        ))
      ) : (
        <Text color="gray.500">No related objectives found</Text>
      )}

      <Heading as="h5" size="sm" color="primary" mt="6" mb="4">
        Related Projects
      </Heading>
      {props.uplineProject && props.uplineProject.length > 0 ? (
        props.uplineProject.map((project: TSpread, index: number) => (
          <Box key={index} mb="4" p="3" borderWidth="1px" borderRadius="md">
            <Flex justifyContent="space-between" alignItems="center">
              <Box>
                <Text fontWeight="500">{project.name}</Text>
                {/* <Text fontSize="sm" color="gray.600">
                  ID: {project.id}
                </Text> */}
              </Box>
              <Flex direction="column" alignItems="flex-end">
                <Text fontSize="sm" fontWeight="500">
                  Relative Point: {project.relativePoint}
                </Text>
              </Flex>
            </Flex>
          </Box>
        ))
      ) : (
        <Text color="gray.500">No related projects found</Text>
      )}
    </Box>
  );
};

const KPIProgressInfo = (props: TKPI) => {
  const getStatusColor = (status: Status) => {
    switch (status) {
      case Status.PENDING:
        return "gray.500";
      case Status.ACTIVE:
        return "blue.500";
      case Status.CLOSE:
        return "green.500";
      default:
        return "gray.500";
    }
  };

  const getProgressValue = (status: Status) => {
    switch (status) {
      case Status.PENDING:
        return 0;
      case Status.ACTIVE:
        return 50;
      case Status.CLOSE:
        return 100;
      default:
        return 0;
    }
  };

  let { data: currentOrganization } = useCurrentOrganization();
  currentOrganization = currentOrganization?.data as TOrganization;

  return (
    <Box>
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <Box>
          <Text fontSize="smaller" color="gray.500">
            Current Status
          </Text>
          <Flex alignItems="center" mt="1">
            <Box
              w="3"
              h="3"
              borderRadius="full"
              bg={getStatusColor(props.status)}
              mr="2"
            />
            <Text>{props.status.replace(/_/g, " ")}</Text>
          </Flex>
        </Box>
        <DetailItem
          label="Routine Type"
          value={props.routineType.replace(/_/g, " ")}
        />
      </Grid>

      <Box mb="6">
        <Flex justifyContent="space-between" mb="2">
          <Text fontSize="sm">Progress</Text>
          <Text fontSize="sm" fontWeight="500">
            {getProgressValue(props.status)}%
          </Text>
        </Flex>
        <Progress
          value={getProgressValue(props.status)}
          colorScheme={"blue"}
          borderRadius="md"
          size="sm"
        />
      </Box>

      <Heading as="h5" size="sm" color="primary" mb="4">
        Timeline
      </Heading>
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <DetailItem
          label="Start Date"
          value={formatDate(
            props.startDate,
            currentOrganization?.primaryDateFormat
          )}
        />
        <DetailItem
          label="End Date"
          value={
            props.endDate
              ? formatDate(
                  props.endDate,
                  currentOrganization?.primaryDateFormat
                )
              : "N/A"
          }
        />
        <DetailItem label="After Occurrence" value={props.afterOccurrence} />
      </Grid>

      <Heading as="h5" size="sm" color="primary" mb="4">
        Ownership Information
      </Heading>
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <DetailItem label="Owner Email" value={props.ownerEmail} />
        <DetailItem label="Created By" value={props.createdBy} />
        {/* <DetailItem label="Tenant ID" value={props.tenantId} /> */}
      </Grid>
    </Box>
  );
};
