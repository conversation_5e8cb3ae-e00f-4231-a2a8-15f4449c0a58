import { Flex, Text } from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect } from "react";
import { IoMdLogIn } from "react-icons/io";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import * as Yup from "yup";
import { loginUser } from "../api/authentication";
import { getCurrentUser } from "../api/user";
import LoginIllustration from "../assets/images/login-illustration.svg";
import AuthContainer from "../components/authentication/authContainer";
import AuthSideImage from "../components/AuthSideImage";
import { ReuseableForm } from "../components/custom/form";
import SEO from "../components/SEO";
import { passwordErrorMessage, passwordRegex } from "../constants";

export interface TenantLoginInputs {
  email: string;
  password: string;
}

const loginSchema = Yup.object().shape({
  email: Yup.string()
    .email("Invalid Email Address")
    .required("Fill in a valid email address"),
  password: Yup.string()
    .min(8, "Password should be at least 8 characters long")
    .matches(passwordRegex, passwordErrorMessage)
    .required("Fill in your password"),
});

const TenantLogin = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const nextUrl = searchParams.get("next");

  const initialValues = { email: "", password: "" };

  const formik = useFormik({
    initialValues,
    validationSchema: loginSchema,
    onSubmit: async ({ email, password }, { resetForm }) => {
      const success = await loginUser({ email, password, resetForm });
      if (success) {
        const currentUser = await getCurrentUser();

        navigate(
          nextUrl || !currentUser?.isSuperAdmin
            ? "/dashboard"
            : currentUser?.hasCompletedCompanyProfile
            ? "/organization/structure"
            : "/organization/setup"
        );
      }
    },
  });

  const fields = [
    {
      name: "email",
      type: "email",
      label: "Organization email",
      placeholder: "Enter a valid email address",
      validate: true,
    },
    {
      name: "password",
      type: "password",
      label: "Password",
      placeholder: "Enter a 8 digit password",
      validate: true,
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <Flex minH="100vh" w="100vw" overflowY="hidden">
      <SEO title="Login to your emetrics organization" />
      <AuthSideImage SignUpIllustration={LoginIllustration} />
      <AuthContainer
        title="Login to your organization"
        subtitle="Welcome Back"
        bottomLink={{ text: "Create an organization", url: "/sign-up" }}
      >
        <ReuseableForm
          formik={formik}
          inputArray={fields}
          button={{
            text: "Login",
            icon: <IoMdLogIn />,
            type: "submit",
            buttonLoadingText: "Checking for account...",
          }}
          bottomCustomComponents={
            <Text
              as="small"
              color="primary"
              textAlign="right"
              display="block"
              fontWeight="500"
              my="6"
            >
              <Link to="/forgot-password">Forgot Password?</Link>
            </Text>
          }
        />
      </AuthContainer>
    </Flex>
  );
};

export default TenantLogin;
