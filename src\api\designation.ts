import { TDesignation } from "../types/designations";
import { CrudService } from "./crud";

const designationCrud = new CrudService<TDesignation>("designations", {
  includeTenantAndUser: false,
});

export const getAllDesignations = async (page?: number, limit?: number) => {
  return await designationCrud.getAll(page, limit);
};

export const getDesignationById = async (id: number) => {
  return await designationCrud.getById(id);
};

export const createDesignation = async (
  designation: Omit<TDesignation, "id">,
  resetForm: () => void
) => {
  return await designationCrud.create(designation, resetForm);
};

export const updateDesignation = async (
  id: number,
  designation: Partial<Omit<TDesignation, "id">>,
  resetForm?: () => void
) => {
  return await designationCrud.update(id, designation, resetForm);
};

export const deleteDesignation = async (id: number) => {
  return await designationCrud.delete(id);
};

export const bulkCreateDesignations = async (formData: FormData) => {
  return await designationCrud.bulkCreate(formData);
};
