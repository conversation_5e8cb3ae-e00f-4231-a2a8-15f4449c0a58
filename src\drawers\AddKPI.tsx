import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  useDisclosure,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import * as yup from "yup";
import { InputField, ReuseableForm } from "../components/custom/form";
import useCalculateEndDateOrOccurrence from "../hooks/useCalculateOccurrenceOrEndDate";
import { useAddKPI } from "../hooks/useKPIs";
import { useGetObjectives } from "../hooks/useObjectives";
import { RoutineType, TObjective } from "../types/objectives";
import { capitalizeFirst } from "../utils";
import { spreadSchema } from "./UpdateObjective";
import { uploadFile } from "../api/storage";
import { useGetPeople, usePeopleEmails } from "../hooks/usePeople";

export const routineTypeArray = [
  RoutineType.ONCE,
  RoutineType.MONTHLY,
  RoutineType.QUARTERLY,
  RoutineType.BI_ANNUALLY,
  RoutineType.ANNUALLY,
];

export const FILE_SIZE_LIMIT = 10 * 1024 * 1024;
const SUPPORTED_FORMATS = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];

export const kpiSpreadSchema = yup.object().shape({
  id: yup.string().optional(),
  name: yup.string().optional(),
  relativePoint: yup.number().optional(),
});

const validationSchema = yup
  .object()
  .shape({
    name: yup.string().required("KPI Name is required"),
    uplineObjective: yup
      .array()
      .of(spreadSchema)
      .min(1, "Assign an Objective to this KPI")
      .required("Objective is required"),
    uplineProject: yup.array().of(kpiSpreadSchema).optional(),
    ownerEmail: yup.string().required("Owner email is required"),
    routineType: yup.string().required("Routine type is required"),
    startDate: yup.string().required("Start date is required"),
    afterOccurrence: yup.number().optional(),
    endDate: yup.string().optional(),
    brief: yup
      .mixed()
      .optional()
      .test("fileSize", "File too large. Max 10MB", function (value: any) {
        if (!value) return true;
        return value.size <= FILE_SIZE_LIMIT;
      })
      .test(
        "fileFormat",
        "Unsupported file type. Only PDF or Word allowed",
        function (value: any) {
          if (!value) return true;
          return SUPPORTED_FORMATS.includes(value.type);
        }
      ),
  })
  .test(
    "endDate-or-afterOccurrence-required",
    "Please select either End Date or After Occurrence",
    function (values) {
      if (!values.afterOccurrence && !values.endDate) {
        return this.createError({
          path: "endDate",
          message: "Please provide either End Date or After Occurrence",
        });
      }
      return true;
    }
  )
  .test(
    "once-requires-endDate",
    "For a 'once' routine, End Date is required",
    function (values) {
      if (values.routineType === "once" && !values.endDate) {
        return this.createError({
          path: "endDate",
          message: "End Date is required for a 'once' routine",
        });
      }
      return true;
    }
  )
  .test(
    "periodic-needs-endDate-or-occurrence",
    "Recurring routines need End Date or Occurrence",
    function (values) {
      if (
        ["monthly", "quarterly", "half-yearly", "yearly"].includes(
          values.routineType
        ) &&
        !values.afterOccurrence &&
        !values.endDate
      ) {
        return this.createError({
          path: "endDate",
          message:
            "Please select either End Date or number of occurrences for recurring routines",
        });
      }
      return true;
    }
  );

export { validationSchema as KPISchema };

const AddKPI = () => {
  const { onClose } = useDisclosure();
  const [page, setPage] = useState(1);
  const addMutation = useAddKPI(onClose);
  const [isLoading, setIsLoading] = useState(false);

  const { data } = useGetObjectives(page, 50);
  const objectivesOptions = data?.data;
  const objectives = objectivesOptions?.data;
  const peopleList = usePeopleEmails();

  const initialValues = {
    name: localStorage.getItem("name") || "",
    uplineObjective: localStorage.getItem("uplineObjective") || "",
    uplineProject: localStorage.getItem("uplineProject") || [],
    ownerEmail: localStorage.getItem("ownerEmail") || "",
    routineType: localStorage.getItem("routineType") || RoutineType.ONCE,
    startDate: localStorage.getItem("startDate") || "",
    endDate: localStorage.getItem("endDate") || "",
    afterOccurrence: localStorage.getItem("afterOccurrence") || "1",
    brief: "",
  };

  const formik = useFormik({
    validationSchema,
    initialValues,
    onSubmit: async (values: any, { resetForm }: any) => {
      setIsLoading(true);

      function updateEmptyIdsByKey(obj: any, targetKey: string) {
        if (!obj || typeof obj !== "object") return;

        const target = obj[targetKey];

        if (Array.isArray(target)) {
          target.forEach((item) => {
            if (item && typeof item === "object" && item.id === "") {
              item.id = 0;
            }
          });
        }
        return obj;
      }

      // Update the uplineProject emptyIds
      values = updateEmptyIdsByKey(values, "uplineProject");
      // if (values.brief && values.brief instanceof File) {
      //   console.log({ brief: values.brief });

      //   const formData = new FormData();
      //   formData.append("file", values.brief);
      //   values.brief = (await uploadFile(formData))?.data;
      // }
      // console.log({ values });
      setIsLoading(false);
      return addMutation.mutate({ values, resetForm });
    },
  });

  useCalculateEndDateOrOccurrence(formik);

  const fields: InputField[] = [
    {
      name: "name",
      type: "text",
      label: "KPI Name",
      placeholder: "Enter KPI name",
      validate: true,
    },
    {
      name: "uplineObjective",
      type: "add-more",
      label: "Upline Objective",
      placeholder: "Select objective",
      addMoreOptions: objectives?.map((objective: TObjective) => ({
        id: objective.id,
        name: capitalizeFirst(objective?.name || ""),
      })),
      addMore: false,
    },
    {
      name: "uplineProject",
      type: "add-more",
      label: "Upline Project",
      placeholder: "Enter project name (optional)",
      addMore: false,
    },
    {
      name: "ownerEmail",
      helpText:
        "Assign this Key Performance Indicator to an email of your team member/ staff",
      type: "select",
      label: "Owner Email",
      placeholder: "Enter owner's email",
      options: peopleList,
      validate: true,
    },
    {
      type: "grid",
      name: "routineGrid",
      gridCol: 2,
      gridInputs: [
        {
          name: "routineType",
          type: "select",
          label: "Routine Type",
          options: routineTypeArray,
          placeholder: "Select routine type",
          validate: true,
        },
        {
          name: "afterOccurrence",
          type: "number",
          label: "After Occurrence",
          placeholder: "Enter occurrences (optional)",
        },
      ],
    },
    {
      type: "grid",
      name: "dateGrid",
      gridCol: 2,
      gridInputs: [
        {
          name: "startDate",
          type: "date",
          label: "Start Date",
          placeholder: "Pick a start date",
          validate: true,
        },
        {
          name: "endDate",
          type: "date",
          label: "End Date",
          placeholder: "Pick an end date (optional)",
        },
      ],
    },
    {
      name: "brief",
      type: "upload",
      label: "Brief",
      placeholder: "Upload a brief description about the KPI",
      validate: true,
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <>
      {
        <>
          <DrawerCloseButton />
          <DrawerHeader fontWeight="500" fontSize="md">
            Create New KPI
          </DrawerHeader>
          <DrawerBody>
            <ReuseableForm formik={formik} inputArray={fields} />
          </DrawerBody>
          <DrawerFooter>
            <Button
              variant="primary"
              w="full"
              onClick={formik.submitForm}
              isLoading={addMutation.isPending || isLoading}
              loadingText="Creating KPI..."
            >
              Create New KPI
            </Button>
          </DrawerFooter>
        </>
      }
    </>
  );
};

export default AddKPI;
