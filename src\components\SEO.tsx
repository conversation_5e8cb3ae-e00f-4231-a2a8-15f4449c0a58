import { Helmet } from "react-helmet-async";
import { useLocation } from "react-router-dom";
import { frontendUrl } from "../api";

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  relativeImagePath?: string;
  url?: string;
  type?: string;
}

const SEO: React.FC<SEOProps> = ({
  title = "E-Metrics Suite - Simplify Staff Management",
  description = "E-Metrics Suite is the ultimate platform for managing your workforce efficiently. Streamline employee records, track performance, and enhance team collaboration with our intuitive tools. Empower your HR team with E-Metrics today!",
  keywords = "staff management, employee records, HR tools, workforce management, team collaboration, performance tracking, employee database, HR software, staff performance, employee engagement, AI, payroll",
  image,
  relativeImagePath,
  url,
  type = "website",
}) => {
  const location = useLocation();
  const currentUrl = url || `${frontendUrl()}${location.pathname}`;

  const ogImage =
    image ||
    frontendUrl() + "/seo/" + (relativeImagePath || "default-image.png");

  return (
    <Helmet async prioritizeSeoTags defaultTitle={title}>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="E-Metrics Team" />
      <meta name="robots" content="index, follow" />

      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:url" content={currentUrl} />
      <link rel="canonical" href={currentUrl} />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
    </Helmet>
  );
};

export default SEO;
