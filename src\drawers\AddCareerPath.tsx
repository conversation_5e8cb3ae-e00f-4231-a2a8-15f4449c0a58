import { useFormik } from "formik";
import * as yup from "yup";
import {
  <PERSON>er<PERSON><PERSON>,
  Drawer<PERSON>lose<PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  useDisclosure,
} from "@chakra-ui/react";
import { ReuseableForm } from "../components/custom/form";
import { useAddCareerPath } from "../hooks/useCareerPaths";
import { educationQualifications } from "../utils/educationalQualifications";

const schema = yup.object().shape({
  level: yup.string().required("Grade Level is required"),
  name: yup.string().required("Name of Grade Level is required"),
  educationalQualification: yup
    .string()
    .required("Educational Qualification is required"),
  minAge: yup.string().required("Min Age is required"),
  maxAge: yup.string().required("Max Age is required"),
  positionLifespan: yup.string().required("Position Lifespan is required"),
  yearsOfExperience: yup
    .string()
    .required("Years of Experience Required is required")
    .typeError("You must specify a number"),
  annualPackage: yup.string().required("Annual Package is required"),
  slotsAvailable: yup.string().required("Slots Available is required"),
});
export { schema as careerPathSchema };
export const fields = [
  {
    name: "name",
    type: "text",
    label: "Name of Grade Level",
    placeholder: "Enter grade level name",
    validate: true,
  },
  {
    name: "level",
    type: "text",
    label: "Grade Level",
    placeholder: "Enter grade level",
    validate: true,
  },
  {
    name: "minAge",
    type: "text",
    label: "Min Age",
    placeholder: "Enter minimum age",
    validate: true,
  },
  {
    name: "maxAge",
    type: "text",
    label: "Max Age",
    placeholder: "Enter maximum age",
    validate: true,
  },
  {
    name: "positionLifespan",
    type: "text",
    label: "Position Lifespan",
    placeholder: "Enter position lifespan",
    validate: true,
  },
  {
    name: "yearsOfExperience",
    type: "text",
    label: "Years of Experience Required",
    placeholder: "Enter years of experience required",
    validate: true,
  },
  {
    name: "educationalQualification",
    type: "select",
    label: "Educational Qualification",
    options: educationQualifications,
    validate: true,
  },
  {
    name: "annualPackage",
    type: "text",
    label: "Annual Package",
    placeholder: "Enter annual package",
    validate: true,
  },
  {
    name: "slotsAvailable",
    type: "text",
    label: "Slots Available",
    placeholder: "Enter slots available",
    validate: true,
  },
];

const AddCareerPath = () => {
  const { onClose } = useDisclosure();
  const addMutation = useAddCareerPath(onClose);

  const initialValues = {
    name: localStorage.getItem("name") || "",
    level: 0,
    minAge: 0,
    maxAge: 0,
    positionLifespan: 0,
    yearsOfExperience: 0,
    annualPackage: 0,
    slotsAvailable: 0,
    educationalQualification: "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: schema,
    onSubmit: (values, { resetForm }) =>
      addMutation.mutate({ values, resetForm }),
  });

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader>Add Career Path Level</DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          variant="primary"
          w="full"
          isLoading={addMutation.isPending}
          loadingText="Adding..."
          onClick={formik.submitForm}
        >
          Add Career Path
        </Button>
      </DrawerFooter>
    </>
  );
};

export default AddCareerPath;
