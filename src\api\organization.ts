import { toast } from "sonner";
import { OrganizationFormValues } from "../pages/organization/setupOrganization";
import {
  OrganizationStructureType,
  TOrganization,
} from "../types/organization";
import { calculateEndTime } from "../utils/formatDate";
import { dashboardService } from "./(dashboard)";
import { uploadFile } from "./storage";

export const getCurrentOrganization = async () => {
  const organization = await dashboardService({
    url: "organization/current",
    method: "GET",
  });

  return organization;
};

export const setupOrganization = async (
  values?: OrganizationFormValues,
  formData?: FormData,
  resetForm?: () => void,
  structureType?: OrganizationStructureType
) => {
  const { data: currentOrganization } = await getCurrentOrganization();

  if (structureType) {
    const organization = await dashboardService({
      url: "organization/" + currentOrganization.id,
      method: "PUT",
      data: {
        ...currentOrganization,
        logo: currentOrganization?.logo || undefined,
        address: currentOrganization.address || undefined,
        structureType: structureType || OrganizationStructureType.default,
      },
      resetForm,
    });
    return organization;
  }

  if (!values) {
    toast.info("No setup values provided");
    return;
  }

  const logoUrl = values.logo && formData ? await uploadFile(formData) : null;

  const breakEndTime = calculateEndTime(
    values.breakStartTime,
    Number(values?.breakDuration.split(" ")[0])
  );

  const formattedData: TOrganization = {
    logo: logoUrl?.data || undefined,
    name: values.name,
    shortName: values.shortName,
    primaryDateFormat: values.primaryDateFormat,
    primaryTimezone: values.primaryTimezone,
    structureType: OrganizationStructureType.default,
    workTimeRange: {
      start: values.workStartTime,
      end: values.workEndTime,
    },
    breakTimeRange: {
      start: values.breakStartTime,
      end: breakEndTime,
    },
    workDays: values.workDays?.filter((day) => day !== undefined),
    otherTimezones: values.otherTimezones?.filter(
      (timezone) => timezone !== undefined
    ),
    otherDateFormats: values.otherDateFormats?.filter(
      (format) => format !== undefined
    ),
  };

  const organization = await dashboardService({
    url: "organization/" + currentOrganization.id,
    method: "PUT",
    data: formattedData,
    resetForm,
  });

  return organization;
};
