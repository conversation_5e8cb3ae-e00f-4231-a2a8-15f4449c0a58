import { Box, But<PERSON>, Heading, Text } from "@chakra-ui/react";
import { useNavigate } from "react-router-dom";

const PageNotFound = () => {
  const navigate = useNavigate();

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      height="100vh"
      bg="gray.100"
      color="gray.700"
    >
      <Heading as="h1" size="xl" mb={4}>
        404 - No Content Found
      </Heading>
      <Text color="gray.500">
        Sorry! We couldn't retrieve any content for this page
      </Text>
      <Box mt={6} display="flex" gap={4}>
        <Button
          variant="outline"
          fontWeight="400"
          fontSize="sm"
          size="sm"
          onClick={() => navigate(-1)}
        >
          Go Back
        </Button>
        <Button
          fontWeight="400"
          fontSize="sm"
          size="sm"
          variant="primary"
          onClick={() => navigate("/")}
        >
          Go to Home
        </Button>
      </Box>
    </Box>
  );
};

export default PageNotFound;
