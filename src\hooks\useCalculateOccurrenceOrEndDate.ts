import debounce from "lodash.debounce";
import { useEffect, useRef } from "react";
import { toast } from "sonner";
import { calculateEndDateOrOccurrence } from "../utils/calculateEndDateOrOccurrence";

const FIELD_AFTER_OCCURRENCE = "afterOccurrence";
const FIELD_END_DATE = "endDate";

const useCalculateEndDateOrOccurrence = (formik: any) => {
  const lastEndDate = useRef(formik.values.endDate);
  const lastAfterOccurrence = useRef(formik.values.afterOccurrence);
  const lastEditedField = useRef<"afterOccurrence" | "endDate" | null>(null);
  const lastValues = useRef({
    afterOccurrence: formik.values.afterOccurrence,
    endDate: formik.values.endDate,
  });

  useEffect(() => {
    const { afterOccurrence, endDate } = formik.values;
    if (
      afterOccurrence !== lastValues.current.afterOccurrence &&
      afterOccurrence !== ""
    ) {
      lastEditedField.current = FIELD_AFTER_OCCURRENCE;
    }

    if (endDate !== lastValues.current.endDate && endDate !== "") {
      lastEditedField.current = FIELD_END_DATE;
    }

    lastValues.current = { afterOccurrence, endDate };
  }, [formik.values.afterOccurrence, formik.values.endDate]);

  const autoCalculate = debounce(() => {
    const { startDate, routineType, afterOccurrence, endDate } = formik.values;

    if (!startDate || !routineType) return;

    const { calculatedEndDate, calculatedAfterOccurrence, errorMessage } =
      calculateEndDateOrOccurrence({
        startDate,
        routineType,
        afterOccurrence,
        endDate,
      });

    if (errorMessage) {
      toast.error(errorMessage);
      formik.setFieldValue(FIELD_END_DATE, "");
      formik.setFieldValue(FIELD_AFTER_OCCURRENCE, "");
      lastEndDate.current = "";
      lastAfterOccurrence.current = "";
      return;
    }

    if (lastEditedField.current === FIELD_AFTER_OCCURRENCE) {
      if (
        calculatedEndDate &&
        calculatedEndDate.toISOString().split("T")[0] !== formik.values.endDate
      ) {
        formik.setFieldValue(
          FIELD_END_DATE,
          calculatedEndDate.toISOString().split("T")[0]
        );
      }
      if (afterOccurrence !== formik.values.afterOccurrence) {
        formik.setFieldValue(FIELD_AFTER_OCCURRENCE, afterOccurrence);
      }
    } else if (lastEditedField.current === FIELD_END_DATE) {
      if (calculatedAfterOccurrence !== formik.values.afterOccurrence) {
        formik.setFieldValue(
          FIELD_AFTER_OCCURRENCE,
          calculatedAfterOccurrence || ""
        );
      }
      if (endDate !== formik.values.endDate) {
        formik.setFieldValue(FIELD_END_DATE, endDate);
      }
    }
  }, 300);

  useEffect(() => {
    if (
      formik.values.startDate !== lastValues.current.afterOccurrence ||
      formik.values.routineType !== lastValues.current.endDate ||
      formik.values.afterOccurrence !== lastValues.current.afterOccurrence ||
      formik.values.endDate !== lastValues.current.endDate
    ) {
      autoCalculate();
    }
  }, [
    formik.values.startDate,
    formik.values.routineType,
    formik.values.afterOccurrence,
    formik.values.endDate,
  ]);
};

export default useCalculateEndDateOrOccurrence;
