import {
  Box,
  DrawerBody,
  <PERSON>er<PERSON><PERSON><PERSON>utton,
  Drawer<PERSON>eader,
  Flex,
  Grid,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Tag,
  TagLabel,
  Text,
} from "@chakra-ui/react";
import { HiOutlineChevronLeft } from "react-icons/hi";
import { TStructuralLevel } from "../types/structuralLevel";
import { capitalizeFirst } from "../utils";

const StructureDetails = (props: TStructuralLevel) => {
  return (
    <>
      <DrawerHeader fontWeight="500" w="100%" mx="auto">
        <Flex justify="space-between" alignItems="center">
          <Flex alignItems="baseline">
            <DrawerCloseButton
              as={HiOutlineChevronLeft}
              size="small"
              display="block"
              position="relative"
            />
            <Text ml={5} fontSize="sm" fontWeight={500}>
              Structure Details
            </Text>
          </Flex>
        </Flex>
      </DrawerHeader>
      <DrawerBody w="100%" mx="auto">
        <Tabs colorScheme="primary">
          <TabList>
            <Tab fontWeight="500" color="gray.600" mr={8}>
              Basic Information
            </Tab>
          </TabList>
          <TabPanels>
            <TabPanel>
              <StructureBasicInfo {...props} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </DrawerBody>
    </>
  );
};

export default StructureDetails;

const DetailItem: React.FC<{ label: string; value: string | number }> = ({
  label,
  value,
}) => (
  <Box mb="4">
    <Text fontSize="smaller" color="gray.500">
      {label}
    </Text>
    <Text>{value || "N/A"}</Text>
  </Box>
);

const StructureBasicInfo = (props: TStructuralLevel) => {
  return (
    <Box>
      <Grid gridTemplateColumns="repeat(2, 1fr)" gap="5" mb="5">
        <DetailItem label="Name" value={capitalizeFirst(props.name)} />
        <DetailItem label="Priority" value={props.priority} />
      </Grid>

      <DetailItem
        label="Description"
        value={props.description || "No description provided"}
      />

      <Box bg="gray.100" p="2">
        <Text fontSize="smaller" color="gray.500" mb="2">
          Tier Names
        </Text>
        {(props?.tiers || []).map((val) => (
          <Tag key={val} borderRadius="xl" variant="outline" mr="2">
            <TagLabel fontSize="small">{val}</TagLabel>
          </Tag>
        ))}
      </Box>
    </Box>
  );
};
