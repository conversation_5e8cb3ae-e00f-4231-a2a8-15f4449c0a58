import { RiSendPlane2Fill } from "react-icons/ri";
import { useParams } from "react-router-dom";
import { getStructuralLevelById } from "../../api/structuralLevel";
import AppBar from "../../components/AppBar";
import CrudUI, { CrudRow } from "../../components/CrudUi";
import CustomDrawer from "../../drawers/CustomDrawer";
import UpdateStructureTierName from "../../drawers/UpdateStructureTierName";
import { structuralLevelsQueryKey } from "../../hooks/useStructure";
import { useDeleteTier } from "../../hooks/useTier";
import { TTier } from "../../types/tier";
import { capitalizeFirst } from "../../utils";

const StructureNamePage = () => {
  const { structureName } = useParams();

  const deleteMutation = useDeleteTier();

  const renderRow = (tier: TTier) => {
    const structureLevelTableInformation = [
      { label: "Tier Name", value: capitalizeFirst(tier.tierName.trim()) },
      { label: "Staff Count", value: "" },
      { label: "Downline", value: "" },
      { label: "Upline", value: tier.uplineName },
    ];

    return (
      <CrudRow
        item={null}
        itemName={tier.tierName || "structure"}
        fields={structureLevelTableInformation}
        renderDrawer={
          <>
            <CustomDrawer
              showModalBtnText=""
              showModalBtnVariant="primary"
              showModalBtnColor="white"
              leftIcon={<RiSendPlane2Fill />}
              drawerSize="sm"
            >
              <UpdateStructureTierName item={tier} />
            </CustomDrawer>
          </>
        }
        onDelete={() => {
          deleteMutation.mutate(tier.id as number);
        }}
        tableDataFields={structureLevelTableInformation.map(({ value }) =>
          String(value)
        )}
      />
    );
  };

  return (
    <>
      <AppBar heading={structureName || "Structure"} />

      <CrudUI
        columns={["Tier Name", "Staff Count", "Downline", "Actions"]}
        queryFn={(page) => getStructuralLevelById(page, structureName)}
        queryKey={structuralLevelsQueryKey[0]}
        renderRow={renderRow}
        title={`${capitalizeFirst(structureName || "")} tier levels`}
        singleRecordField="tier"
      />
    </>
  );
};

export default StructureNamePage;
