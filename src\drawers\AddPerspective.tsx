import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@chakra-ui/react";

import { useFormik } from "formik";
import * as yup from "yup";
import { ReuseableForm } from "../components/custom/form";
import { useAddPerspective } from "../hooks/usePerspectives";

export interface AddPerspectiveInputs {
  name: string;
}

const validationSchema = yup.object().shape({
  name: yup.string().required("Perspective name is required"),
});

const AddNewPerspective = () => {
  const { onClose } = useDisclosure();

  const addMutation = useAddPerspective(onClose);

  const formik = useFormik({
    initialValues: { name: localStorage.getItem("name") || "" },
    validationSchema,
    onSubmit: (values, { resetForm }) =>
      addMutation.mutate({ name: values.name, resetForm }),
  });

  const fields = [
    {
      name: "name",
      type: "text",
      label: "Perspective name",
      placeholder: "",
      validate: true,
    },
  ];

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Create New Perspective
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          onClick={formik.submitForm}
          variant="primary"
          w="full"
          size="sm"
          isLoading={addMutation.isPending}
          loadingText="Creating perspective..."
        >
          Create Perspective
        </Button>
      </DrawerFooter>
    </>
  );
};

export default AddNewPerspective;