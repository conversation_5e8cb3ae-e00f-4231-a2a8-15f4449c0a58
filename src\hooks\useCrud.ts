import { Query, useMutation, useQuery } from "@tanstack/react-query";
import { queryClient } from "../context/CustomProvider";

export const useCrudQuery = <TData>(
  key: string[],
  fetchFn: () => Promise<TData>,
  enabled?: boolean,
  refetchInterval?:
    | number
    | false
    | ((
        query: Query<TData, Error, TData, string[]>
      ) => number | false | undefined)
) => {
  return useQuery({
    queryKey: Array.isArray(key) ? key : key,
    queryFn: fetchFn,
    placeholderData: (previousData) => previousData,
    enabled,
    refetchInterval,
  });
};

export const useCrudCreate = (
  key: string[],
  createFn: (input: any) => Promise<any>,
  onClose?: () => void
) => {
  return useMutation({
    mutationFn: createFn,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: key });
      onClose?.();
    },
  });
};

export const useCrudUpdate = (
  key: string[],
  updateFn: (input: any) => Promise<any>,
  onClose?: () => void
) => {
  return useMutation({
    mutationFn: updateFn,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: key });
      onClose?.();
    },
  });
};

export const useCrudDelete = (
  key: string[],
  deleteFn: (id: number) => Promise<any>
) => {
  return useMutation({
    mutationFn: deleteFn,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: key });
    },
  });
};
