import { toast } from "sonner";

export function capitalizeFirst(value: string): string {
  return String(value)
    .toLowerCase()
    .replace(/^\w/, (c) => c.toUpperCase());
}

export const formatCamelCase = (key: string): string => {
  return key
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase());
};

export function timeAgo(timestamp: string | number | Date): string {
  const now = new Date();
  const past = new Date(timestamp);
  const seconds = Math.floor((now.getTime() - past.getTime()) / 1000);

  const intervals: Record<string, number> = {
    year: 31536000,
    month: 2592000,
    week: 604800,
    day: 86400,
    hour: 3600,
    minute: 60,
    second: 1,
  };

  for (const [unit, value] of Object.entries(intervals)) {
    const count = Math.floor(seconds / value);
    if (count > 0) {
      return count === 1 ? `a ${unit} ago` : `${count} ${unit}s ago`;
    }
  }

  return "just now"; // Default for timestamps very close to the current time
}

export function formatNumber(value: string | number): string {
  const num = Number(value);
  if (num < 0) return "0";
  if (num >= 1e12) return (num / 1e9).toFixed(1).replace(/\.0$/, "") + "T";
  if (num >= 1e9) return (num / 1e9).toFixed(1).replace(/\.0$/, "") + "B";
  if (num >= 1e6) return (num / 1e6).toFixed(1).replace(/\.0$/, "") + "M";
  if (num >= 1e3) return (num / 1e3).toFixed(1).replace(/\.0$/, "") + "K";
  return num.toString();
}

export async function customFormikFieldValidator(formik: {
  validateForm: (
    values: Record<string, any>
  ) => Promise<Record<string, string>>;
  values: Record<string, any>;
  setFieldTouched: (field: string, isTouched: boolean) => void;
}): Promise<boolean> {
  const errors = await formik.validateForm(formik.values);

  if (Object.keys(errors).length > 0) {
    Object.keys(formik.values).forEach((field) => {
      formik.setFieldTouched(field, true);
    });
    return false;
  }
  return true;
}

export interface ShareData {
  title?: string;
  text?: string;
  url?: string;
}

export const shareThis = async ({
  shareUrlString,
  shareData,
}: {
  shareUrlString: string;
  shareData: ShareData;
}): Promise<void> => {
  if (navigator.share) {
    try {
      await navigator.share(shareData);
    } catch (error) {
      console.error("Error sharing: ", error);
      toast.error("An error occurred while sharing");
    }
  } else {
    // Fallback for browsers that don't support the Web Share API
    const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
      shareUrlString
    )}`;
    window.open(shareUrl, "_blank");
  }
};

export const downloadFile = (url: string, filename?: string) => {
  const anchor = document.createElement("a");
  anchor.href = url;
  anchor.download = filename || url.split("/").pop() || "download";
  document.body.appendChild(anchor);
  anchor.click();
  document.body.removeChild(anchor);
};
