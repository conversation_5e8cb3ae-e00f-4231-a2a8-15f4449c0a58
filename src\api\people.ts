import { TPeople } from "../types/people";
import { CrudService } from "./crud";


const peopleCrud = new CrudService<TPeople>("people");

export const getAllPeople = async (page?: number, limit?: number) => {
  return await peopleCrud.getAll(page, limit);
};

export const getPeopleById = async (id: number) => {
  return await peopleCrud.getById(id);
};

export const createPeople = async (
  people: Omit<TPeople, "id">,
  resetForm: () => void
) => {
  return await peopleCrud.create(people, resetForm);
};

export const updatePeople = async (
  id: number,
  people: Partial<Omit<TPeople, "id">>,
  resetForm?: () => void
) => {
  return await peopleCrud.update(id, people, resetForm);
};

export const deletePeople = async (id: number) => {
  return await peopleCrud.delete(id);
};

export const bulkCreatePeoples = async (formData: FormData) => {
  return await peopleCrud.bulkCreate(formData);
};
