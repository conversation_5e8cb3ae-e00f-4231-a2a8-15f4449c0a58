import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect, useMemo } from "react";
import * as yup from "yup";
import { InputField, ReuseableForm } from "../components/custom/form";
import { useUpdateDesignation } from "../hooks/useDesignation";
import { TDesignation } from "../types/designations";
import { useOrganizationStructure } from "../hooks/organization";

export const designationSchema = yup.object().shape({
  name: yup.string().required("Designation name is required"),
  structuralLevel: yup
    .array()
    .of(
      yup.object().shape({
        id: yup.number().required(),
        name: yup.string().required(),
      })
    )
    .min(1, "Structural level is required")
    .required("Structural level is required"),
  description: yup.string().optional(),
});

const UpdateDesignation = ({ designation }: { designation: TDesignation }) => {
  const { onClose } = useDisclosure();
  const updateMutation = useUpdateDesignation(designation.id, onClose);

  const structureLvldata = useOrganizationStructure();
  const structureLvls = useMemo(() => {
    const levels = structureLvldata;

    const structureLevelNames = levels?.map((item: any) => {
      return { name: item.name, id: item.id };
    });
    const structureLevelTiersMap = levels?.reduce((acc: any, item: any) => {
      acc[item?.name] = item?.tiers;
      return acc;
    }, {} as Record<string, string[]>);

    return { levels: structureLevelNames, tiers: structureLevelTiersMap };
  }, [structureLvldata]);

  const initialValues = {
    name: designation.name || "",
    structuralLevel: designation.structuralLevel || [],
    description: designation.description || "",
  };

  const formik = useFormik({
    initialValues,
    validationSchema: designationSchema,
    onSubmit: async (values, { resetForm }) =>
      updateMutation.mutate({ values, resetForm }),
  });

  const fields: InputField[] = [
    {
      name: "name",
      type: "text",
      label: "Designation Name",
      placeholder: "Enter designation name",
      validate: true,
    },
    {
      name: "structuralLevel",
      type: "select",
      label: "Structural Level",
      placeholder: "Search & select structure",
      addMoreOptions: structureLvls?.levels,
      selected: designation.structuralLevel,
    },
    {
      name: "description",
      type: "textarea",
      label: "Designation Description ",
      placeholder: "Enter a brief description of this designation",
      validate: true,
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);
  }, []);

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Designation
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          variant="primary"
          w="full"
          size="sm"
          isLoading={updateMutation.isPending}
          onClick={formik.submitForm}
          loadingText="Updating designation..."
        >
          Update Designation
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UpdateDesignation;
