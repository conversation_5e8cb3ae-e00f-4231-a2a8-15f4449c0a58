export enum OrganizationStructureType {
  custom = "custom",
  default = "default",
}
export interface TOrganization {
  id?: string;
  name?: string;
  shortName?: string;
  logo?: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
  primaryTimezone: string;
  otherTimezones?: string[];
  primaryDateFormat: string;
  otherDateFormats?: string[];
  workDays: string[];
  workTimeRange: { start: string; end: string };
  breakTimeRange?: { start: string; end: string };
  structureType?: OrganizationStructureType;
  ownerId?: string;
}
