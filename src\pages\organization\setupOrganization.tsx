import { CheckCircledIcon } from "@radix-ui/react-icons";
import { useFormik } from "formik";
import moment from "moment-timezone";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import * as yup from "yup";
import { setupOrganization } from "../../api/organization";
import AuthContainer from "../../components/authentication/authContainer";
import { ReuseableForm } from "../../components/custom/form";
import { useCurrentOrganization } from "../../hooks/organization";
import { TOrganization } from "../../types/organization";
import { dateTimeFormats, daysOfWeek } from "../../utils/dateFormats";

const validationSchema = yup.object().shape({
  logo: yup.mixed().nullable(),
  // .required("Please add an organization logo")
  // .test("fileSize", "Logo must be less than 6MB", function (value: any) {
  //   return !value || value.size <= 6 * 1024 * 1024;
  // })
  // .test("fileType", "Logo must be a valid image type", function (value: any) {
  //   return (
  //     !value ||
  //     [
  //       "image/jpeg",
  //       "image/jpg",
  //       "image/png",
  //       "image/avif",
  //       "image/webp",
  //     ].includes(value.type.toLowerCase())
  //   );
  // }),
  shortName: yup.string().required("Organization Short Name is required"),
  name: yup.string().required("Organization Name is required"),
  primaryTimezone: yup.string().required("Primary Timezone is required"),
  otherTimezones: yup.array().of(yup.string()).optional(),
  primaryDateFormat: yup.string().required("Primary Date Format is required"),
  otherDateFormats: yup.array().of(yup.string()).optional(),
  workDays: yup
    .array()
    .of(yup.string())
    .min(1, "At least one work day must be selected")
    .required("Work Days are required"),
  workStartTime: yup.string().required("Work Start Time is required"),
  workEndTime: yup.string().required("Work End Time is required"),
  breakStartTime: yup.string().required("Break Start Time is required"),
  breakDuration: yup.string().required("Break Duration is required"),
});

export type OrganizationFormValues = yup.InferType<typeof validationSchema>;

const CreateOrganization = () => {
  const navigate = useNavigate();

  const { data } = useCurrentOrganization() as {
    data: { data: TOrganization };
  };

  const organizationData = data?.data;

  const initialValues = {
    logo: organizationData?.logo || null,
    shortName: localStorage.getItem("shortName") || "",
    name: organizationData?.name || "",
    primaryTimezone:
      organizationData?.primaryTimezone ||
      localStorage.getItem("primaryTimezone") ||
      "",
    otherTimezones: JSON.parse(localStorage.getItem("otherTimezones") || "[]"),
    workDays:
      organizationData?.workDays ||
      JSON.parse(localStorage.getItem("workDays") || "[]"),
    workStartTime:
      organizationData?.workTimeRange?.start ||
      localStorage.getItem("workStartTime") ||
      "08:00",
    workEndTime:
      organizationData?.workTimeRange?.end ||
      localStorage.getItem("workEndTime") ||
      "16:00",
    breakStartTime:
      organizationData?.breakTimeRange?.end ||
      localStorage.getItem("breakStartTime") ||
      "12:00",
    breakDuration: localStorage.getItem("breakDuration") || "30 minutes",
    primaryDateFormat:
      organizationData?.primaryDateFormat ||
      localStorage.getItem("primaryDateFormat") ||
      "",
    otherDateFormats:
      organizationData?.otherDateFormats ||
      JSON.parse(localStorage.getItem("otherDateFormats") || "[]"),
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      const formData = new FormData();
      if (values.logo) formData.append("file", values.logo);

      const response = await setupOrganization(values, formData, resetForm);

      if (response.success) {
        navigate("/organization/structure");
      }
    },
  });

  const momentTzs = moment.tz.names().map((zone) => {
    const now = moment().tz(zone);
    return `${now.format("z").toUpperCase()} - ${zone}`;
  });

  useEffect(() => {
    formik.setValues(initialValues);
  }, [organizationData]);

  const fields = [
    {
      name: "logo",
      type: "upload",
      label: "Organization logo",
      placeholder: "Upload logo",
      validate: true,
    },
    {
      type: "grid",
      name: "organization-details",
      gridInputs: [
        {
          name: "shortName",
          type: "text",
          label: "Organization shortname",
          placeholder: "e.g EMS",
          validate: true,
        },
        {
          name: "name",
          type: "text",
          label: "Organization name",
          placeholder: "e.g E-metrics Suite",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "organization-timezones",
      gridInputs: [
        {
          name: "primaryTimezone",
          type: "select",
          label: "Primary timezone",
          placeholder: "Select a primary timezone",
          options: momentTzs,
          validate: true,
        },
        {
          name: "otherTimezones",
          type: "multiselect",
          label: "Secondary timezones",
          options: momentTzs,
          placeholder: "Select your alternative timezones",
          validate: true,
        },
      ],
    },
    {
      name: "workDays",
      type: "multiselect",
      label: "Work Days",
      placeholder: "e.g Select working days",
      options: daysOfWeek,
      validate: true,
    },
    {
      type: "grid",
      name: "organization-work-time",
      gridInputs: [
        {
          name: "workStartTime",
          type: "time",
          label: "resumption time",
          placeholder: "",
          validate: true,
        },
        {
          name: "workEndTime",
          type: "time",
          label: "closing time",
          placeholder: "",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "organization-break-time",
      gridInputs: [
        {
          name: "breakStartTime",
          type: "time",
          label: "Lunch break start time",
          placeholder: "",
          validate: true,
        },
        {
          name: "breakDuration",
          type: "select",
          label: "lunch break duration",
          placeholder: "Select duration of lunch break",
          options: Array.from(
            { length: 200 },
            (_, idx) => `${idx + 1} minute${idx + 1 > 1 ? "s" : ""}`
          ),
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "organization-datetime",
      gridInputs: [
        {
          name: "primaryDateFormat",
          type: "select",
          label: "Primary date time format",
          placeholder: "Select a primary date time format",
          options: dateTimeFormats.map((tz) => `${tz.value} * ${tz.label}`),
          validate: true,
        },
        {
          name: "otherDateFormats",
          type: "multiselect",
          label: "Secondary date time formats",
          options: dateTimeFormats.map((tz) => `${tz.value} * ${tz.label}`),
          placeholder: "Select your alternative date time format",
          validate: true,
        },
      ],
    },
  ];

  return (
    <AuthContainer
      title="Complete Organization Setup"
      subtitle="Congratulations on creating an organization, now we need more information to setup your organization properly"
      alignItems="flex-start"
      isAuthPage={false}
    >
      <ReuseableForm
        formik={formik}
        inputArray={fields}
        button={{
          text: "Complete account setup",
          icon: <CheckCircledIcon />,
          buttonLoadingText: "Setting up organization...",
        }}
      />
    </AuthContainer>
  );
};

export default CreateOrganization;
