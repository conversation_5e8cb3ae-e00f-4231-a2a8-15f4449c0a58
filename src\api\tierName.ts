import { TTier } from "../types/tier";
import { CrudService } from "./crud";

const tierCrud = new CrudService<TTier>("tier", {
  includeTenantAndUser: false,
});

export const getAllTierForLevel = async (
  page?: number,
  limit?: number,
  levelId?: number
) => {
  return await tierCrud.getByFilters({ levelId }, page, limit);
};

export const getTierById = async (id: number, name?: string) => {
  return await tierCrud.getById(id, name);
};

export const createTier = async (
  tier: Omit<TTier, "id">,
  resetForm?: () => void
) => {
  return await tierCrud.create(tier, resetForm);
};

export const updateTier = async (
  id: number,
  tier: Partial<Omit<TTier, "id">>,
  resetForm?: () => void
) => {
  return await tierCrud.patch(id, tier, resetForm);
};

export const deleteTier = async (id: number) => {
  return await tierCrud.delete(id);
};

export const bulkCreateTier = async (formData: FormData) => {
  return await tierCrud.bulkCreate(formData);
};
