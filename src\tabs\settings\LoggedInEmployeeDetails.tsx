import { Text } from "@chakra-ui/layout";
import { useEffect, useState } from "react";
import axiosInstance from "../../api";
import Preloader from "../../components/Preloader";
import EmployeeDetails from "../../drawers/EmployeeDetails";
import { getMyInfo } from "../../services/auth.service";

const LoggedInEmployeeDetails = () => {
  const [employee, setEmployee] = useState<any>();
  const [isLoading, setIsLoading] = useState(false);

  const getLoggedInEmployee = async () => {
    const orgName = localStorage.getItem("current_organization_short_name");
    const loggedInEmployeeUuid = getMyInfo();
    if (!orgName || !loggedInEmployeeUuid) return;

    setIsLoading(true);
    try {
      const { data } = await axiosInstance.get(
        `/client/${orgName}/employee/?user__email=${
          (loggedInEmployeeUuid as any)?.email
        }`
      );
      setEmployee(data.data[0]);
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getLoggedInEmployee();
  }, []);

  if (isLoading) return <Preloader />;

  if (!employee)
    return <Text textAlign="center">You don't have any employees</Text>;

  const {
    employee_basic_information: basicInfo,
    employee_contact_information: contactInfo,
    employee_employment_information: employmentInfo,
    user,
    corporate_level,
    group,
    unit,
    division,
  } = employee;

  const level =
    corporate_level || group || unit || division || "error finding level";

  return (
    <>
      <EmployeeDetails {...employee} />

      {/* <Document {...employee} removeAddDoc={false} /> */}
    </>
  );
};

export default LoggedInEmployeeDetails;
