import { TOrganization } from "../types/organization";
import { formatDate } from "../utils/formatDate";
import { useCurrentOrganization } from "./organization";

export const useOrganizationDateFormat = () => {
  const { data } = useCurrentOrganization();
  const currentOrganization = data?.data as TOrganization;

  return (date: string | Date, secondaryFormat?: string) =>
    formatDate(
      String(date),
      String(secondaryFormat || currentOrganization?.primaryDateFormat)
    );
};
