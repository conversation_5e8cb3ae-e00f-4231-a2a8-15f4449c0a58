name: Deploy

# Controls when the action will run. 
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches: [ fixHrAdminduplication ]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:       
    - name: Deploy using ssh
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USER }}
        key: ${{ secrets.SSH_PRI_KEY }}
        port: 22
        script: |
          cd /var/www/e-metricsuite/
          git branch
          git checkout fixHrAdminduplication
          git pull origin fixHrAdminduplication
          git status
          export PATH=$PATH:/usr/local/node/bin
          node -v
          npm -v
          source ~/.bashrc
          yarn install
          yarn build
          sudo systemctl restart nginx
