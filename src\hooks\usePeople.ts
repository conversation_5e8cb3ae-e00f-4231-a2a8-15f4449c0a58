import {
  createPeople,
  deletePeople,
  getAllPeople,
  updatePeople,
} from "../api/people";
import { TPeople } from "../types/people";
import {
  useCrudCreate,
  useCrudDelete,
  useCrudQuery,
  useCrudUpdate,
} from "./useCrud";

export const peopleQueryKey = ["people"];

export const useGetPeople = (page?: number, limit?: number) =>
  useCrudQuery(peopleQueryKey, () => getAllPeople(page, limit));

export const useAddPeople = (onClose: () => void) =>
  useCrudCreate(
    peopleQueryKey,
    ({ values, resetForm }) =>
      createPeople(values as Omit<TPeople, "id">, resetForm),
    onClose
  );

export const useUpdatePeople = (id: number, onClose?: () => void) =>
  useCrudUpdate(
    peopleQueryKey,
    ({ values, resetForm }) => updatePeople(id, values, resetForm),
    onClose
  );

export const useDeletePeople = () =>
  useCrudDelete(peopleQueryKey, deletePeople);

export const usePeopleEmails = () => {
  const { data: people } = useGetPeople();
  const peopleList = people?.data?.data?.map((item: any) => item.officialEmail);
  return peopleList;
};
