import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FormControl,
  FormLabel,
  Select,
  Text,
  useToast,
} from "@chakra-ui/react";
import { Field, Form, Formik } from "formik";
import { useEffect, useState } from "react";
import * as yup from "yup";
import InputWithLabel from "../components/InputWithLabel";

export interface JobDescriptionInputs {
  description: string;
  team: {
    name: string;
  };
  designation: {
    name: string;
  };
  grade_level: string[];
  upline?: string | null;
  level: number;
  target_point: number;
}

const validationSchema = yup.object().shape({
  description: yup.string().required("Job Description is required"),
  team: yup.object().shape({
    name: yup.string().required("Team Name is required"),
  }),
  designation: yup.object().shape({
    name: yup.string().required("Designation Name is required"),
  }),
  grade_level: yup.array().of(yup.string().required("Grade Level is required")),
  target_point: yup.number().required("Target point is required"),
});

const AddJD = () => {
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [designations, setDesignations] = useState<any[]>([]);

  const onSubmit = (values: JobDescriptionInputs) => {
    // if an upline wasn't chosen, remove it from the sent data
    const dataToSubmit = !values.upline
      ? (() => {
          const { upline, ...newData } = values;
          return newData;
        })()
      : values;

    console.log(dataToSubmit);
  };

  useEffect(() => {
    const org_name = localStorage.getItem("current_organization");
    // Fetch designations if needed
  }, []);

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontWeight="500">
        Add Job Description
      </DrawerHeader>
      {isLoading ? (
        <h1>Loading...</h1>
      ) : (
        <>
          <DrawerBody>
            <Formik
              initialValues={{
                description: "",
                team: {
                  name: "",
                },
                designation: {
                  name: "",
                },
                grade_level: [],
                upline: null,
                level: 0,
                target_point: 0,
              }}
              validationSchema={validationSchema}
              onSubmit={onSubmit}
            >
              {({ errors, touched, values }) => (
                <Form id="add-jd-form">
                  <Field name="description">
                    {({ field }: any) => (
                      <InputWithLabel
                        id="description"
                        label="Job Description"
                        variant="filled"
                        bg="secondary.200"
                        {...field}
                        formErrorMessage={
                          touched.description && errors.description
                        }
                        mb="5"
                      />
                    )}
                  </Field>

                  <FormControl mb="5">
                    <FormLabel htmlFor="designation.name" fontWeight="500">
                      Designation
                    </FormLabel>
                    <Field
                      as={Select}
                      placeholder="Select Designation"
                      variant="filled"
                      bg="secondary.200"
                      color="gray.400"
                      size="lg"
                      id="designation.name"
                      name="designation.name"
                    >
                      {designations.map((designation) => (
                        <option key={designation.name} value={designation.name}>
                          {designation.name}
                        </option>
                      ))}
                    </Field>
                    <Text fontSize="sm" color="crimson">
                      {touched.designation?.name && errors.designation?.name}
                    </Text>
                  </FormControl>

                  <Field name="upline">
                    {({ field }: any) => (
                      <InputWithLabel
                        id="upline"
                        label="Upline"
                        variant="filled"
                        bg="secondary.200"
                        {...field}
                        formErrorMessage={touched.upline && errors.upline}
                        mb="5"
                      />
                    )}
                  </Field>

                  <Field name="target_point">
                    {({ field }: any) => (
                      <InputWithLabel
                        id="target_point"
                        label="Target Point"
                        variant="filled"
                        bg="secondary.200"
                        type="number"
                        {...field}
                        formErrorMessage={
                          touched.target_point && errors.target_point
                        }
                      />
                    )}
                  </Field>
                </Form>
              )}
            </Formik>
          </DrawerBody>
          <DrawerFooter>
            <Button type="submit" form="add-jd-form" variant="primary" w="full">
              Add Job Description
            </Button>
          </DrawerFooter>
        </>
      )}
    </>
  );
};

export default AddJD;