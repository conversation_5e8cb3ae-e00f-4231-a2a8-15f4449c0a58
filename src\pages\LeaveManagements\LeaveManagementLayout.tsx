// import { Outlet } from "react-router-dom";

// const LeaveManagementLayout = () => {
//   return (
//     <div>
//       <Outlet /> {/* Renders the child route components */}
//     </div>
//   );
// };

// export default LeaveManagementLayout; 

import { Outlet, Navigate, useLocation } from "react-router-dom";

const LeaveManagementLayout = () => {
  const location = useLocation();

  // Redirect to request-leave if the exact path is /dashboard/leave-management
  if (location.pathname === "/dashboard/leave-management") {
    return <Navigate to="/dashboard/leave-management/request-leave" replace />;
  }

  return (
    <div>
      <Outlet /> {/* Renders the child route components */}
    </div>
  );
};

export default LeaveManagementLayout;