import AppBar from "../components/AppBar";
import CustomTab from "../components/custom/CustomTab";
import { useCheckUserType } from "../hooks/useCheckUserType";
import { useGetCurrentUser } from "../hooks/user";
import JDTaskSpread from "../tabs/strategyDeck/JDTaskSpread";
import ObjectiveAndJDInitiativeTab from "../tabs/strategyDeck/ObjectiveAndJDInitiative";
import ObjectivePerspectiveSpreadTab from "../tabs/strategyDeck/ObjectivePerspective";
import ObjectivesTab from "../tabs/strategyDeck/Objectives";
import PerspectivesTab from "../tabs/strategyDeck/Perspectives";
import StrategyKpiTab from "../tabs/strategyDeck/StrategyKpiTab";
import { TRole } from "../types/user";
import UnauthorizedPage from "./UnauthorizedPage";

const StrategyDeck = () => {
  const { data: currentUser, isLoading } = useGetCurrentUser();
  const authorizedType: TRole[] = ["employer", "hr", "admin"];

  const tabList = [
    { label: "Perspectives", showForAll: false, visibleTo: authorizedType },
    { label: "Objectives", showForAll: false, visibleTo: authorizedType },
    {
      label: "Objective - Perspective Spread",
      showForAll: false,
      visibleTo: authorizedType,
    },
    { label: "KPI", showForAll: true },
    {
      label: "Objective - KPI Spread",
      showForAll: false,
      visibleTo: authorizedType,
    },
    { label: "KPI Task Spread", showForAll: true },
  ];

  const tabPanels = [
    PerspectivesTab,
    ObjectivesTab,
    ObjectivePerspectiveSpreadTab,
    StrategyKpiTab,
    ObjectiveAndJDInitiativeTab,
    JDTaskSpread,
  ];

  const isAdmin = useCheckUserType(authorizedType);

  return (
    <>
      <AppBar heading="Strategy Deck" />

      {isLoading ? (
        <></>
      ) : isAdmin ? (
        <CustomTab
          tabId="StrategyDeckPageTab"
          tabList={tabList}
          tabPanels={tabPanels}
          userType={authorizedType}
          hasIcon
          variant={""}
        />
      ) : (
        <UnauthorizedPage />
      )}
    </>
  );
};

export default StrategyDeck;
