import {
  Avatar,
  Box,
  Button,
  Checkbox,
  Flex,
  FormLabel,
  Image,
  Input,
  Select,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
} from "@chakra-ui/react";
import { useEffect, useRef, useState } from "react";
import ReactQuill from "react-quill";
import FormikErrorResponse from "./formError";

import { ImageIcon, TrashIcon } from "@radix-ui/react-icons";
import { motion } from "framer-motion";
import {
  CountryCode,
  getCountries,
  getCountryCallingCode,
  parsePhoneNumberFromString,
} from "libphonenumber-js";
import React from "react";
import { GrCloudUpload, GrMultiple } from "react-icons/gr";
import "react-quill/dist/quill.snow.css";
import ReactSelect from "react-select";
import { FixedSizeList as List } from "react-window";
import { blue200, deepBlue, errorColor } from "../../theme/colors";
import customTheme from "../../theme/theme";
import { capitalizeFirst } from "../../utils";
import { MarkdownComponent } from "../MarkDownComponent";

export const inputClassNames = {
  mt: 2,
  w: "100%",
  bg: "gray.100",
  py: 2.5,

  borderRadius: "md",
  placeholder: { fontSize: "sm" },
  fontSize: "sm",
  transition: "all 0.3s",
  zIndex: 0,
};

interface CustomInputProps {
  type: string;
  placeholder?: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur: (e: React.FocusEvent<HTMLInputElement>) => void;
  value: string;
  name: string;
  className?: string;
  validate?: boolean;
  error?: boolean;
  formik?: any;
}

export default function CustomInput({
  type,
  placeholder,
  onChange,
  onBlur,
  value,
  name,
  className,
  validate,
  error,
}: CustomInputProps) {
  const [isPassword, setIsPassword] = useState(true);
  const [passwordType, setPasswordType] = useState("password");

  const handlePasswordVisibility: () => void = () => {
    setIsPassword(!isPassword);
    setPasswordType(!isPassword ? "password" : "text");
  };

  return (
    <Box position="relative" w="full" mt="-2.5">
      <Input
        autoComplete="true"
        {...inputClassNames}
        className={className}
        _placeholder={{ fontsize: "10px", color: "gray.400" }}
        focusBorderColor={deepBlue}
        flex="1"
        borderColor={
          validate && !error ? "gray.200" : error ? errorColor : "gray.200"
        }
        sx={{
          animation: error ? customTheme.animations.shake : null,
        }}
        bg={validate && !error ? "gray.50" : error ? "#9e3818/5" : "gray.50"}
        type={type === "password" ? passwordType : type}
        placeholder={placeholder}
        onChange={(e) => {
          if (type !== "password") localStorage.setItem(name, e.target.value);
          onChange(e);
        }}
        onBlur={onBlur}
        value={value}
        id={name}
        name={name}
      />
      {type === "password" && (
        <Text
          position="absolute"
          top="5"
          right="3"
          cursor="pointer"
          fontSize="xs"
          zIndex={1000}
          onClick={handlePasswordVisibility}
        >
          {isPassword ? "Show" : "Hide"}
        </Text>
      )}
    </Box>
  );
}

export const PhoneNumberInput = ({
  className,
  validate,
  error,
  placeholder,
  onChange,
  onBlur,
  value,
  name,
  formik,
}: CustomInputProps) => {
  const [country, setCountry] = useState<CountryCode>("NG");

  useEffect(() => {
    formik.setFieldValue(
      name,
      `+${getCountryCallingCode(country as CountryCode)}`
    );
  }, [country]);

  const isValid = () => {
    const raw = formik.values[name] || "";

    const expectedDialCode = `+${getCountryCallingCode(country)}`;

    const hasDialCode = /^\+\d+/.test(raw);

    const phoneNumber = parsePhoneNumberFromString(raw);

    return (
      hasDialCode && phoneNumber?.isValid() && raw.startsWith(expectedDialCode)
    );
  };

  return (
    <Flex gap={2} align="center">
      {/* <Flex align="center" px={2} bg="gray.50" borderRadius="md"> */}
      <Image
        src={`https://flagcdn.com/w40/${country.toLowerCase()}.png`}
        alt={country}
        boxSize="15px"
        rounded="sm"
      />
      <Select
        w="auto"
        flexShrink={0}
        value={country}
        fontSize="small"
        border="0"
        padding="0"
        focusBorderColor="transparent"
        onChange={(e) => {
          setCountry(e.target.value as CountryCode);
        }}
        className={className}
      >
        {getCountries().map((c: any) => (
          <option key={c} value={c}>
            {c}
          </option>
        ))}
      </Select>

      <CustomInput
        type="tel"
        name={name}
        onBlur={onBlur}
        onChange={onChange}
        error={error}
        placeholder={placeholder}
        validate={isValid() || validate}
        value={value}
      />
    </Flex>
  );
};

interface ImageSelectProps {
  label: string;
  name: string;
  formik: any;
  hasCaption?: boolean;
  captionName?: string;
  accept?: string;
}

export function ImageSelect({
  label,
  name,
  formik,
  hasCaption = true,
  captionName,
  accept = "image/*",
}: ImageSelectProps) {
  return (
    <Box>
      <FormLabel
        border="1px"
        borderColor={formik.values[name] ? "green.200" : "gray.100"}
        h="150px"
        borderRadius="md"
        bg={formik.values[name] ? "green.50" : "background"}
        display="flex"
        flexDirection="column"
        alignItems="center"
        gap={3}
        overflow="hidden"
        p={1}
        cursor="pointer"
        position="relative"
      >
        {label}
        <Input
          type="file"
          accept={accept}
          name={name}
          onChange={(event) => {
            formik.setFieldValue(name, event.currentTarget.files?.[0]);
          }}
          onBlur={(event) => {
            formik.setFieldValue(name, event.currentTarget.files?.[0]);
          }}
          display="none"
        />
        <ImageIcon color="gray.300" />
        {hasCaption && (
          <Input
            name={captionName}
            onChange={(event) => {
              localStorage.setItem(name, event.currentTarget.value);
              formik.setFieldValue(captionName, event.currentTarget.value);
            }}
            onBlur={(event) => {
              formik.setFieldValue(captionName, event.currentTarget.value);
            }}
            value={formik.values[`${captionName}`]}
            placeholder="Enter caption for image (optional)"
            borderColor="gray.200"
          />
        )}
      </FormLabel>
      <FormikErrorResponse
        touched={formik.touched}
        errors={formik.errors}
        name={name}
      />
    </Box>
  );
}

export function UploadFileButton({
  formik,
  name,
  label,
  placeholder,
  hasErrorComponent,
}: AvatarUploadProps) {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    formik.setFieldValue(name, e.currentTarget.files?.[0]);
  };

  const file = formik.values[name];
  const fileIsString = typeof file === "string";
  const isImage = file && file.type?.startsWith("image/");

  return (
    <Box>
      <Flex alignItems="center" gap="4" overflow="hidden" wrap={"wrap"}>
        <Button
          as="label"
          htmlFor={name}
          variant="outline"
          color={blue200}
          fontSize="xs"
          bg="gray.50"
          fontWeight="500"
          transition="all 0.35s"
          _hover={{ bg: "gray.100", borderColor: deepBlue }}
          cursor="pointer"
          leftIcon={<GrCloudUpload />}
          flexShrink="0"
        >
          Upload {label || "File"}
        </Button>
        <Input
          id={name}
          name={name}
          type="file"
          accept="*/*"
          onChange={handleFileChange}
          display="none"
        />
        {file && (
          <Flex alignItems="center" gap="3">
            {(isImage || fileIsString) && (
              <img
                src={fileIsString ? file : URL.createObjectURL(file)}
                alt={
                  "Preview of " +
                  (fileIsString ? "Organization logo" : (file as File)?.name)
                }
                style={{
                  width: "30px",
                  height: "30px",
                  borderRadius: "10000000px",
                }}
              />
            )}
            <Text fontSize="sm" isTruncated maxWidth={"200px"} color="gray.500">
              {fileIsString
                ? "Organization Logo"
                : file.name.split(".")[0] || "File selected"}
            </Text>
          </Flex>
        )}
        {hasErrorComponent && (
          <FormikErrorResponse
            errors={formik.errors}
            touched={formik.touched}
            name={name}
          />
        )}
      </Flex>
      <Text mt="1" fontSize="small" color="gray.600">
        {placeholder}
      </Text>
    </Box>
  );
}

interface AvatarUploadProps {
  formik: any;
  name: string;
  label?: string;
  placeholder?: string;
  className?: string;
  hasErrorComponent?: boolean;
}

export function AvatarUpload({
  formik,
  name,
  label,
  className,
  hasErrorComponent,
}: AvatarUploadProps) {
  const imageValue = formik.values[`${name}`];

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    formik.setFieldValue(name, e.currentTarget.files?.[0]);
  };

  return (
    <Box className={className}>
      <FormLabel>
        <Avatar
          src={imageValue ? URL.createObjectURL(imageValue) : ""}
          name="Image Upload"
          className="size-20"
          mx="auto"
          transition="all 0.3s"
          borderRadius="full"
          p={imageValue ? 0.5 : 0}
          bg={imageValue ? deepBlue : "transparent"}
        />
        <Input
          name={name}
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          onInput={handleFileChange}
          onBlur={handleFileChange}
          display="none"
        />
        {label && (
          <Text fontWeight="500" fontSize="xs" mt={2}>
            {label}
          </Text>
        )}
      </FormLabel>
      {hasErrorComponent && (
        <FormikErrorResponse
          errors={formik.errors}
          touched={formik.touched}
          validate={false}
          name={name}
        />
      )}
    </Box>
  );
}

interface CustomTextAreaProps {
  formik: any;
  name: string;
  placeholder?: string;
}

export const CustomTextArea = ({
  formik,
  name,
  placeholder,
}: CustomTextAreaProps) => {
  const selectedStyle = { color: deepBlue, bg: blue200, borderRadius: 6 };

  const getTextLength = (html: string) => {
    const div = document.createElement("div");
    div.innerHTML = html;

    const textLength = div.innerText.replaceAll(" ", "").length;
    return textLength;
  };

  const textLength = getTextLength(formik.values[`${name}`]);

  return (
    <>
      <Tabs>
        <TabList mt={1}>
          <Tab fontSize="xs" _selected={selectedStyle}>
            Editor
          </Tab>
          <Tab fontSize="xs" _selected={selectedStyle}>
            Preview
          </Tab>
        </TabList>
        <TabPanels>
          <TabPanel px={0}>
            <ReactQuill
              value={formik.values[`${name}`]}
              onChange={(value) => {
                const cleanedValue = value === "<p><br></p>" ? "" : value;

                localStorage.setItem(name, cleanedValue);
                formik.setFieldValue(name, cleanedValue);
              }}
              theme="snow"
              placeholder={placeholder}
            />
            <Box display="flex" justifyContent="end" gap={0.5} mt={1}>
              <motion.small
                key={formik.values[`${name}`].trim()}
                initial={{ y: -5 }}
                animate={{ opacity: 1, y: 0 }}
              >
                {textLength === 0 ? "" : textLength}
              </motion.small>
              {textLength === 0
                ? ""
                : textLength > 1
                ? "characters"
                : "character"}
            </Box>
          </TabPanel>
          <TabPanel mb={4} px={0}>
            <MarkdownComponent markdownContent={formik.values[`${name}`]} />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </>
  );
};

export function CustomMultiSelect({
  formik,
  name,
  placeholder,
  options,
}: {
  formik: any;
  name: string;
  placeholder?: string;
  options: string[];
}) {
  const selectedItems = formik.values[name] || [];
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [buttonWidth, setButtonWidth] = useState<number | undefined>(undefined);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const flexRef = useRef<HTMLDivElement>(null);

  // Parse and filter options
  const parsedOptions = options.map((opt) => {
    const [value, label] = opt.split("*");
    return { value, label };
  });

  const filteredOptions = parsedOptions.filter(({ value, label }) => {
    const text = `${value} ${label || ""}`.toLowerCase();
    return text.includes(searchTerm.toLowerCase());
  });

  const sortedOptions = searchTerm
    ? filteredOptions
    : [
        ...filteredOptions.filter((o) => selectedItems.includes(o.value)),
        ...filteredOptions.filter((o) => !selectedItems.includes(o.value)),
      ];

  const toggleSelection = (value: string) => {
    const updated = selectedItems.includes(value)
      ? selectedItems.filter((v: string) => v !== value)
      : [...selectedItems, value];

    formik.setFieldValue(name, updated);
    localStorage.setItem(name, JSON.stringify(updated));
  };

  const handleClickOutside = (e: MouseEvent) => {
    setSearchTerm("");
    if (wrapperRef.current && !wrapperRef.current.contains(e.target as Node)) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    if (flexRef.current) {
      setButtonWidth(flexRef.current.offsetWidth - 10);
    }
  }, [flexRef.current]);

  const selectedLabel =
    selectedItems.length === 0
      ? ""
      : selectedItems.length <= 2
      ? selectedItems.join(", ").replaceAll("_", " ")
      : `${selectedItems[0].replaceAll(
          "_",
          " "
        )}, ${selectedItems[1].replaceAll("_", " ")}... +${
          selectedItems.length - 2
        }`;

  return (
    <Box ref={wrapperRef} position="relative" width="100%">
      <Flex
        onClick={() => setIsOpen((prev) => !prev)}
        ref={flexRef}
        pos="relative"
      >
        <Input
          z-index="0"
          bg="gray.100"
          placeholder={selectedLabel || placeholder}
          value={searchTerm}
          m="0"
          onChange={(e) => setSearchTerm(e.target.value)}
          size="sm"
          border="1px solid"
          borderColor="gray.200"
          rounded="md"
          _focus={{ borderColor: "gray.300" }}
          px="4"
          h="40px"
        />
        <Box pos="absolute" right="4" top="30%" z-index="10">
          <GrMultiple fontSize="14px" color={isOpen ? "black" : "gray"} />
        </Box>
      </Flex>

      {isOpen && (
        <Box
          position="absolute"
          top="100%"
          mt={1}
          width={buttonWidth || "100%"}
          bg="white"
          shadow="md"
          border="1px solid"
          borderColor="gray.200"
          borderRadius="md"
          zIndex={1000}
        >
          {selectedItems.length > 1 && searchTerm === "" && (
            <Box
              px={3}
              py={2}
              borderBottom="1px solid"
              borderColor="gray.100"
              cursor="pointer"
              _hover={{ bg: "gray.50" }}
              onClick={() => {
                formik.setFieldValue(name, []);
                localStorage.removeItem(name);
              }}
            >
              <Text>Clear</Text>
            </Box>
          )}

          <List
            height={300}
            itemCount={sortedOptions.length}
            itemSize={40}
            width="100%"
          >
            {({ index, style }) => {
              const { value, label } = sortedOptions[index];
              const isSelected = selectedItems.includes(value);

              return (
                <Flex
                  key={value}
                  style={style}
                  px={3}
                  py={4}
                  mb="2"
                  h="fit-content"
                  alignItems="center"
                  cursor="pointer"
                  _hover={{ bg: "gray.100" }}
                  onClick={() => toggleSelection(value)}
                >
                  <Checkbox
                    isChecked={isSelected}
                    mr={2}
                    pointerEvents="none"
                  />
                  <Text fontWeight="normal" flex="1" wordBreak="break-all">
                    {capitalizeFirst(value.toLowerCase().replaceAll("_", " "))}{" "}
                    {label && (
                      <Text as="span" color="gray.400" ml={1}>
                        {label}
                      </Text>
                    )}
                  </Text>
                </Flex>
              );
            }}
          </List>
        </Box>
      )}
    </Box>
  );
}

interface CustomSelectProps {
  formik: any;
  name: string;
  placeholder?: string;
  options?: string[];
  addMoreOptions?: AddMoreOption[];
  selected?: AddMoreOption[];
  onChangeCallback?: (val: any) => void;
}

export const CustomSelect = ({
  formik,
  name,
  placeholder,
  options = [""],
  addMoreOptions,
  selected,
  onChangeCallback,
}: CustomSelectProps) => {
  // for addMore
  const [fields, setFields] = useState<
    { id?: string | number; name?: string }[]
  >(selected || []);

  const reactSelectOptions = addMoreOptions
    ? addMoreOptions.map((opt) => ({
        value: opt.id,
        label: opt.name,
      }))
    : options.map((opt) => {
        const [value, extraLabel] = opt.split("*");
        const formattedLabel = capitalizeFirst(
          value.toLowerCase().replaceAll("_", " ")
        );
        return {
          value,
          label: extraLabel
            ? `${formattedLabel} - ${extraLabel}`
            : formattedLabel,
        };
      });

  const selectedOption = fields?.[0]?.id
    ? {
        value: fields?.[0]?.id,
        label: fields?.[0]?.name,
      }
    : reactSelectOptions.find((opt) => opt.value === formik.values[name]);

  useEffect(() => {
    if (addMoreOptions) {
      formik.setFieldValue(name, fields);
    }
  }, [fields]);

  const handleChange = (index: number, value: string | number) => {
    const updatedFields = [...fields];
    const matchedOption = addMoreOptions?.find((opt) => opt.id === value);
    updatedFields[index] = {
      id: value,
      name: matchedOption?.name ?? "",
    };
    setFields(updatedFields);

    formik.setFieldValue(name, updatedFields);
  };

  return (
    <ReactSelect
      id={name}
      name={name}
      {...inputClassNames}
      options={reactSelectOptions}
      placeholder={placeholder}
      value={selectedOption || null}
      onChange={(selected: any) => {
        if (addMoreOptions) {
          handleChange(0, selected.value);
          return;
        }
        if (onChangeCallback) onChangeCallback(selected);
        localStorage.setItem(name, selected.value);
        formik.setFieldValue(name, selected.value);
      }}
      onBlur={() => {
        formik.setFieldTouched(name, true);
      }}
      styles={{
        control: (base: any, state: any) => ({
          ...base,
          backgroundColor: "#f9fafb",
          borderColor: state.isFocused ? deepBlue : "#e5e7eb",
          fontSize: "0.875rem",
          "&:hover": {
            borderColor: "gray.100",
          },
        }),
      }}
    />
  );
};

export interface AddMoreOption {
  id: string | number;
  name: string;
}

interface AddMoreProps {
  formik: any;
  name: string;
  placeholder?: string;
  options?: AddMoreOption[];
  selected?: AddMoreOption[];
  addMore?: boolean;
}

export const AddMore = ({
  formik,
  name,
  placeholder,
  options = [],
  selected,
  addMore,
}: AddMoreProps) => {
  const [fields, setFields] = useState<
    { id?: string | number; name?: string; relativePoint?: number }[]
  >(selected || [{ id: "", name: "", relativePoint: 0 }]);

  const handleChange = (index: number, key: string, value: string | number) => {
    const updatedFields = [...fields];
    const updatedField = { ...updatedFields[index], [key]: value };

    if (key === "id") {
      const matched = options?.find((opt) => String(opt.id) === String(value));
      updatedField.name = matched?.name || "";
    }

    updatedFields[index] = updatedField;

    setFields(updatedFields);
    localStorage.setItem(name, JSON.stringify(updatedFields));
    formik.setFieldValue(name, updatedFields);

    // updatedFields[index] = {
    //   ...updatedFields[index],
    //   [key]: value,
    //   ...(key === "id"
    //     ? {
    //         name: options?.find((opt) => opt.id === Number(value))?.name,
    //       }
    //     : {
    //         id: index + 1,
    //       }),
    // };
  };

  const handleAddField = () => {
    setFields((prev) => [...prev, { id: "", name: "", relativePoint: 0 }]);
  };

  const handleRemoveField = (index: number) => {
    if (fields.length === 1) return;
    const updated = [...fields];
    updated.splice(index, 1);
    setFields(updated);
    formik.setFieldValue(name, updated);
  };

  return (
    <Box>
      <Flex direction="column" gap="4">
        {fields.map((field, idx) => (
          <Flex key={idx} gap="2" align="center">
            {options.length > 0 ? (
              <Select
                key={idx}
                placeholder={placeholder}
                value={field.id || ""}
                onChange={(e) => handleChange(idx, "id", e.target.value)}
                focusBorderColor={deepBlue}
                fontSize="small"
              >
                {options
                  ?.filter((opt) => {
                    const otherSelectedIds = fields
                      .filter((_, i) => i !== idx)
                      .map((f) => String(f.id));
                    return !otherSelectedIds.includes(String(opt.id));
                  })
                  ?.map((opt) => (
                    <option key={opt.id} value={opt.id}>
                      {opt.name}
                    </option>
                  ))}
              </Select>
            ) : (
              <CustomInput
                name={name}
                onBlur={(e) => {
                  handleChange(idx, "name", e.target.value);
                }}
                onChange={(e) => {
                  handleChange(idx, "name", e.target.value);
                }}
                formik={formik}
                type="text"
                value={field.name || ""}
                placeholder={placeholder}
              />
            )}
            <Input
              type="number"
              placeholder="Point"
              width="80px"
              fontSize="small"
              textAlign="center"
              value={field.relativePoint || ""}
              onChange={(e) =>
                handleChange(idx, "relativePoint", Number(e.target.value))
              }
              focusBorderColor={deepBlue}
            />
            {fields.length > 1 && (
              <Button
                variant="outline"
                onClick={() => handleRemoveField(idx)}
                colorScheme="red"
              >
                <TrashIcon width="18" />
              </Button>
            )}
          </Flex>
        ))}
      </Flex>
      {addMore && options?.length > fields.length && (
        <Button
          type="button"
          onClick={handleAddField}
          size="sm"
          mt="2"
          colorScheme="blue"
          fontWeight="500"
        >
          Add more
        </Button>
      )}
    </Box>
  );
};
