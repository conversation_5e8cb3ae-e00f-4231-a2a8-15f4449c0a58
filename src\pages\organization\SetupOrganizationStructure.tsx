import { Flex, Text } from "@chakra-ui/react";
import { PlusCircledIcon } from "@radix-ui/react-icons";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useNavigate, useSearchParams } from "react-router-dom";
import * as yup from "yup";
import {
  createStructuralLevel,
  updateStructuralLevel,
} from "../../api/structuralLevel";
import AppBar from "../../components/AppBar";
import CustomStepper from "../../components/custom/CustomStepper";
import { InputField, ReuseableForm } from "../../components/custom/form";
import { useOrganizationStructure } from "../../hooks/organization";
import {
  useAddStructuralLevel,
  useGetStructuralLevels,
} from "../../hooks/useStructure";
import { TStructuralLevel } from "../../types/structuralLevel";
import { capitalizeFirst } from "../../utils";
import {
  checkIfStructureExists,
  checkIfTierExists,
  structuralLevelIndexKey,
} from "../../utils/structuralLevel";

const LevelSchema = yup.object().shape({
  name: yup.string().required("Level Name is required"),
});

const SetupOrganizationStructure = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const currentIndex =
    Number(localStorage.getItem(structuralLevelIndexKey)) || 0;
  const { data: allStructuralLevelData } = useGetStructuralLevels();
  const organizationStructure = useOrganizationStructure();
  const addMutation = useAddStructuralLevel();
  // const updataOrganizationMutation = useMutation({
  //   queryKey: ["setupOrganization"],
  //   queryFn: () => setupOrganization(values, formData, resetForm);
  // })

  const allStructuralLevels = allStructuralLevelData?.data?.data || [];

  const priorityStructure = organizationStructure.sort(
    (a, b) => a.priority - b.priority
  );
  const isDefaultStructureType = searchParams.get("structureType") || "default";

  // useEffect(() => {
  //   if (isDefaultStructureType === "default") {
  //     defaultStructuralLevelArray.forEach((level, index) => {
  //       if (!checkIfStructureExists(level, allStructuralLevels)) {
  //         addMutation.mutate({
  //           values: {
  //             name: level,
  //             priority: index + 1,
  //           },
  //         });
  //       }
  //     });
  //   }
  // }, [allStructuralLevels, addMutation, isDefaultStructureType]);

  const steps = priorityStructure.map((level) => ({
    label: capitalizeFirst(level.name),
    completed: currentIndex > level.priority,
  }));

  return (
    <>
      <AppBar heading="Structure Setup" />
      <CustomStepper
        steps={steps}
        currentStep={currentIndex}
        components={priorityStructure.map((level) => (
          <CustomSetup
            key={level.name}
            name={level.name}
            priority={level.priority}
            helpText={`Add ${level.name} level name in full e.g Chief Executive Officer instead of CEO`}
            allStructuralLevels={allStructuralLevels}
            navigate={navigate}
          />
        ))}
      />
    </>
  );
};

export default SetupOrganizationStructure;

const CustomSetup = ({
  name,
  priority,
  helpText,
  allStructuralLevels,
  navigate,
}: {
  name: string;
  priority: number;
  helpText: string;
  allStructuralLevels: TStructuralLevel[];
  navigate: ReturnType<typeof useNavigate>;
}) => {
  const structureExists = checkIfStructureExists(name, allStructuralLevels);

  const mutation = useMutation({
    mutationFn: async (tierName: string) => {
      const newTier = tierName.toLowerCase();
      const isTierExist = checkIfTierExists(
        newTier,
        structureExists?.tiers?.map((tier) => tier.toLowerCase())
      );

      if (isTierExist) return;

      const payload = {
        name,
        description: "",
        priority,
        tiers: [newTier],
      };

      if (structureExists) {
        return await updateStructuralLevel(structureExists.id, {
          name: structureExists.name,
          description: structureExists.description || "",
          priority: structureExists.priority,
          tiers: [...(structureExists.tiers || []), newTier],
        });
      } else {
        return await createStructuralLevel(payload);
      }
    },
    onSuccess: () => {
      localStorage.setItem("organizationStructure", "1");
      localStorage.setItem(structuralLevelIndexKey, String(0));
      navigate("/organization/structure");
    },
  });

  const formik = useFormik({
    initialValues: { name: "" },
    validationSchema: LevelSchema,
    onSubmit: (data) => mutation.mutate(data.name),
  });

  const fields: InputField[] = [
    {
      name: "name",
      type: "text",
      label: "Tier Name",
      validate: true,
      helpText: `This will represent the name of the level structure to categorize people. ${helpText}.`,
    },
  ];

  return (
    <Flex direction="column" textAlign="start" maxW="500px" mb="6">
      <Text fontWeight="500" fontSize="lg" color="primary">
        Setup {capitalizeFirst(name)} Level
      </Text>
      <Text fontWeight="400" fontSize="sm" color="gray.400">
        Add a new tier name for your {name} structure, you can add as many{" "}
        {name}s as your organization needs
      </Text>

      <ReuseableForm
        formik={formik}
        inputArray={fields}
        button={{
          text: "Add Level",
          icon: <PlusCircledIcon />,
          buttonLoadingText: "Adding Level...",
          loading: mutation.isPending,
        }}
      />
    </Flex>
  );
};
