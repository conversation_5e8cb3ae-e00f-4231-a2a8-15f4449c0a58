import { Navigate, Outlet } from "react-router-dom";
import TokenService from "../services/token.service";

const TenantAdminRoute = () => {
  const accessToken = TokenService.getLocalAccessToken();
  const isAuthenticated = accessToken ? true : false;
  // const decodedToken = jwtDecode(accessToken);
  //Here is where we should check if user is admin or super_admin e.t.c
  // if (decodedToken.user === "admin") {
  //   history('/admin')
  // }
  return !isAuthenticated ? (
    <Navigate to="/tenant-management/login" />
  ) : (
    <Outlet />
  );
};

export default TenantAdminRoute;
