import {
  createObjective,
  deleteObjective,
  getAllObjectives,
  updateObjective,
} from "../api/objectives";
import { Status, TObjective } from "../types/objectives";
import {
  useCrudCreate,
  useCrudDelete,
  useCrudQuery,
  useCrudUpdate,
} from "./useCrud";

export const objectivesQueryKey = ["objectives"];

export const useGetObjectives = (
  page?: number,
  limit?: number,
  status?: Status
) =>
  useCrudQuery(objectivesQueryKey, () => getAllObjectives(page, limit, status));

export const useAddObjective = (onClose: () => void) =>
  useCrudCreate(
    objectivesQueryKey,
    ({ values, resetForm }) =>
      createObjective(values as Omit<TObjective, "id">, resetForm),
    onClose
  );

export const useUpdateObjective = (id: number, onClose?: () => void) =>
  useCrudUpdate(
    objectivesQueryKey,
    ({ values, resetForm }) => updateObjective(id, values, resetForm),
    onClose
  );

export const useDeleteObjective = () =>
  useCrudDelete(objectivesQueryKey, deleteObjective);
