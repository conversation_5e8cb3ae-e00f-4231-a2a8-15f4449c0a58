import { Flex } from "@chakra-ui/react";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import * as Yup from "yup";

import { toast } from "sonner";
import { resetPassword } from "../api/authentication";
import ForgotPasswordIllustration from "../assets/images/forgotpassword.svg";
import AuthSideImage from "../components/AuthSideImage";
import AuthContainer from "../components/authentication/authContainer";
import { ReuseableForm } from "../components/custom/form";
import {
  passwordErrorMessage,
  passwordMinLength,
  passwordRegex,
} from "../constants";

const resetPasswordSchema = Yup.object().shape({
  password: Yup.string()
    .required("Password is required")
    .matches(passwordRegex, passwordErrorMessage)
    .min(passwordMinLength, "Password must be at least 8 characters"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("password")], "Password and confirm password must match")
    .required("Confirm your password"),
});

const ResetPassword = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isTokenValid, setIsTokenValid] = useState(false);

  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const token = searchParams.get("token");

  const initialValues = { password: "", confirmPassword: "" };

  const formik = useFormik({
    initialValues,
    validationSchema: resetPasswordSchema,
    onSubmit: async ({ password }, { resetForm }) => {
      setIsLoading(true);
      const success = await resetPassword({
        password: password,
        token,
        resetForm,
      });
      setIsLoading(false);

      if (success) {
        navigate("/");
      }
    },
  });

  const fields = [
    {
      name: "password",
      type: "password",
      label: "New Password",
      placeholder: "Enter your new password",
      validate: true,
    },
    {
      name: "confirmPassword",
      type: "password",
      label: "Confirm Password",
      placeholder: "Re-enter your new password",
      validate: true,
    },
  ];

  useEffect(() => {
    if (!token) {
      toast.error("No token found, Check your mail for a password reset email");
      navigate("/forgot-password");
      return;
    }
  }, [token, navigate]);

  return (
    <Flex minH="100vh" w="100vw" overflowY="hidden">
      <AuthSideImage SignUpIllustration={ForgotPasswordIllustration} />
      <AuthContainer
        title="Reset Password"
        subtitle="Enter your new password"
        bottomText="Remember your password?"
        bottomLink={{ text: "Login", url: "/login" }}
      >
        {
          <ReuseableForm
            formik={formik}
            inputArray={fields}
            button={{
              text: "Reset Password",
              type: "submit",
              buttonLoadingText: "Resetting password...",
            }}
          />
        }
      </AuthContainer>
    </Flex>
  );
};

export default ResetPassword;
