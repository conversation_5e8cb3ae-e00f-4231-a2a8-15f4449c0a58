import { Box, Flex, Heading, ResponsiveValue, Text } from "@chakra-ui/react";
import { motion } from "framer-motion";
import { ReactNode } from "react";
import { Link } from "react-router-dom";
import { blue200 } from "../../theme/colors";
import SEO from "../SEO";

interface AuthContainerProps {
  title: string;
  subtitle?: string;
  seoTitle?: string;
  children: ReactNode;
  bottomText?: string;
  bottomLink?: {
    text: string;
    url: string;
  };
  alignItems?: ResponsiveValue<
    "stretch" | "center" | "flex-start" | "flex-end" | "baseline"
  >;
  isAuthPage?: boolean;
}

const MotionFlex = motion.create(Flex);

const AuthContainer = ({
  title,
  seoTitle,
  subtitle,
  children,
  bottomText,
  bottomLink,
  alignItems = "center",
  isAuthPage = true,
}: AuthContainerProps) => {
  return (
    <MotionFlex
      h="100vh"
      w="100%"
      bg="white"
      flex="1"
      shrink="0"
      overflowY="auto"
      justifyContent="center"
      alignItems={alignItems}
      px={{ base: "4", sm: "8" }}
      pt="8"
      pb="16"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <SEO title={seoTitle || title} description={subtitle} />
      <Box
        w={{
          base: "100%",
          sm: isAuthPage ? "85%" : "100%",
          md: isAuthPage ? "400px" : "700px",
        }}
        h="fit-content"
      >
        <Heading as="h1" color="primary" fontSize="1.8rem" fontWeight={700}>
          {title}
        </Heading>
        {subtitle && (
          <Text fontSize="sm" mb="8" mt="2" color="gray.500" maxW="500px">
            {subtitle}
          </Text>
        )}

        {children}

        {bottomLink && (
          <Text
            as="small"
            textAlign="center"
            display="block"
            fontWeight="500"
            mt="4"
          >
            {bottomText && bottomText}{" "}
            <Link style={{ color: blue200 }} to={bottomLink.url}>
              {bottomLink.text}
            </Link>
          </Text>
        )}
      </Box>
    </MotionFlex>
  );
};

export default AuthContainer;
