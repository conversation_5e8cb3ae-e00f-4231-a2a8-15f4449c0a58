import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FormControl,
  FormLabel,
  Input,
  Text,
  useToast,
} from "@chakra-ui/react";
import { Field, Form, Formik } from "formik";
import moment from "moment";
import { useEffect, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";
import * as yup from "yup";
import axiosInstance from "../api";
import SelectAsyncPaginate from "../components/AsyncSelect";
import { ExcludeDaysInCalendar } from "../components/DateInput";
import InputWithLabel from "../components/InputWithLabel";
import Preloader from "../components/Preloader";
import { getLoggedInUserEmail } from "../services/auth.service";
import { CurrentOrgnisationSettingsType } from "../services/list.service";

const validationSchema = yup.object().shape({
  recorded_allowance: yup.number(),
  start_date: yup.string().required("Start Date is required"),
  end_date: yup.string(),
  leave_type_id: yup.number().required("You Need to Pick a Leave"),
  duration: yup.number().required("Number of Days is needed"),
  deputizing_officer: yup
    .string()
    .required("Please fill the info of the deputizing officer"),
  hand_over_report: yup.mixed().required("Please submit hand over report"),
});

const AddLeaveApplication = (): React.ReactElement => {
  const org_name = localStorage.getItem("current_organization_short_name");
  const { showBoundary } = useErrorBoundary();
  const [currentLeave, setCurrentLeave] = useState<any>();
  const toast = useToast();
  const [loggedInEmployee, setLoggedInEmployee] = useState<any>(null);
  const [CurrentOrgnisationSettings, setCurrentOrgnisationSettings] =
    useState<CurrentOrgnisationSettingsType>({
      company_name: "",
      ownerEmail: "",
      owner_first_name: "",
      owner_last_name: "",
      company_short_name: "",
      owner_phone_number: "",
      work_start_time: "",
      work_stop_time: "",
      work_break_start_time: "",
      work_break_stop_time: "",
      work_days: [],
      timezone: "",
    });
  const [selectedDeputizingOfficer, setSelectedDeputizingOfficer] =
    useState<any>(null);

  const onSubmit = (values: any) => {
    const start_date_formated = moment(new Date(values.start_date)).format(
      "YYYY-MM-DD"
    );
    const start_date = new Date(values.start_date);
    const end_date = moment(
      new Date().setDate(start_date.getDate() + values.duration)
    ).format("YYYY-MM-DD");

    const data = {
      recorded_allowance: values.recorded_allowance,
      start_date: start_date_formated,
      end_date: end_date,
      leave_type_id: values.leave_type_id,
      duration: values.duration,
      deputizing_officer: values.deputizing_officer,
      hand_over_report: values.hand_over_report,
    };

    console.log({ "backend data": data });
    if (org_name) {
      console.log("org");
    }
  };

  const get_employee_careerpath = async () => {
    let loggedInuserEmail = getLoggedInUserEmail("client_tokens");

    try {
      const resp: any = await axiosInstance.get(
        `client/${org_name}/employee/?user__email=${loggedInuserEmail}`
      );

      if (resp.data.data) {
        const employee = resp.data.data[0];
        if (employee) {
          let data: any = {
            my_level_id: employee.career_path
              ? employee.career_path.level
              : null,
          };
          if (employee.corporate_level) {
            data[
              "team_id_filter"
            ] = `corporate_level__uuid=${employee.corporate_level.uuid}`;
          }
          if (employee.department) {
            data[
              "team_id_filter"
            ] = `department__uuid=${employee.department.uuid}`;
          }
          if (employee.division) {
            data["team_id_filter"] = `division__uuid=${employee.division.uuid}`;
          }
          if (employee.group) {
            data["team_id_filter"] = `group__uuid=${employee.group.uuid}`;
          }
          if (employee.unit) {
            data["team_id_filter"] = `unit__uuid=${employee.unit.uuid}`;
          }
          setLoggedInEmployee(data);
          localStorage.setItem(
            "my_level_id_and_employee_id",
            JSON.stringify(data)
          );
        } else {
          setLoggedInEmployee(null);
          toast({
            title: "Please ask your admin HR to assign career path to you",
            status: "error",
            position: "top",
            duration: 3000,
            isClosable: true,
          });
        }
      }
    } catch (err: any) {
      toast({
        title: "Please relogin we could not process your info",
        status: "error",
        position: "top",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    let org_settings = localStorage.getItem("org_info");
    if (org_settings) {
      setCurrentOrgnisationSettings(JSON.parse(org_settings));
    }
    const my_level_id_and_employee_id = localStorage.getItem(
      "my_level_id_and_employee_id"
    );
    if (my_level_id_and_employee_id) {
      setLoggedInEmployee(JSON.parse(my_level_id_and_employee_id));
    } else {
      get_employee_careerpath();
    }
  }, []);

  return (
    <div>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Leave Application
      </DrawerHeader>
      <DrawerBody>
        {loggedInEmployee ? (
          <Formik
            initialValues={{
              recorded_allowance: 100.0,
              start_date: "",
              end_date: "",
              leave_type_id: "",
              duration: 0,
              deputizing_officer: "",
              hand_over_report: null,
            }}
            validationSchema={validationSchema}
            onSubmit={onSubmit}
          >
            {({ errors, touched, setFieldValue, values }) => (
              <Form id="apply-leave-form">
                <br />

                <FormControl mb="4">
                  <FormLabel
                    fontSize="xs"
                    htmlFor="leave_type_id"
                    fontWeight="500"
                  >
                    Available Leave
                  </FormLabel>
                  <SelectAsyncPaginate
                    onPointChange={null}
                    url={`/client/${org_name}/leave-management/hr-leave-management/?filter_by_grade_level_id=${loggedInEmployee.my_level_id}`}
                    value={currentLeave}
                    onChange={(value: any) => {
                      setFieldValue("leave_type_id", value?.id);
                      setCurrentLeave(value);
                    }}
                    SelectLabel={(option: any) =>
                      `${option.leave_choice} for level ${option.grade_level}`
                    }
                    SelectValue={(option: any) => option.leave_choice}
                    placeholder=""
                  />
                  <Text fontSize="sm" color="crimson">
                    {touched.leave_type_id && errors.leave_type_id}
                  </Text>
                </FormControl>
                <br />

                <FormLabel fontSize="xs" fontWeight="500">
                  Start Date
                </FormLabel>
                <ExcludeDaysInCalendar
                  name="start_date"
                  onChange={(date: string) => setFieldValue("start_date", date)}
                  days_array={[...CurrentOrgnisationSettings.work_days].map(
                    (num) => num + 1
                  )}
                  placeholder="Enter Start Date"
                  formErrorMessage={
                    touched.start_date && errors.start_date
                      ? "Start Date Can Not Be empty"
                      : ""
                  }
                  dateFormat="yyyy/MM/dd"
                />
                <br />

                <Field name="duration">
                  {({ field }: any) => (
                    <InputWithLabel
                      id="duration"
                      type="number"
                      label="Number of days (duration)"
                      variant="filled"
                      bg="secondary.200"
                      {...field}
                      formErrorMessage={touched.duration && errors.duration}
                      mb="3"
                    />
                  )}
                </Field>

                <FormLabel
                  fontSize="xs"
                  htmlFor="hand_over_report"
                  fontWeight="500"
                >
                  Hand over report
                </FormLabel>
                <Input
                  type="file"
                  accept="application/pdf"
                  required
                  variant="filled"
                  bg="transparent"
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    if (e.target.files) {
                      setFieldValue("hand_over_report", e.target.files[0]);
                    }
                  }}
                />
                <Text fontSize="sm" color="crimson">
                  {touched.hand_over_report && errors.hand_over_report}
                </Text>

                <br />
                <br />

                <FormControl mb="4">
                  <FormLabel
                    fontSize="xs"
                    htmlFor="deputizing_officer"
                    fontWeight="500"
                  >
                    Deputizing officer
                  </FormLabel>
                  <SelectAsyncPaginate
                    onPointChange={null}
                    url={`/client/${org_name}/employee/?${loggedInEmployee.team_id_filter}`}
                    value={selectedDeputizingOfficer}
                    onChange={(value: any) => {
                      setFieldValue("deputizing_officer", value?.user.email);
                      setSelectedDeputizingOfficer(value);
                    }}
                    SelectLabel={(option: any) =>
                      `${option.user.first_name} ${option.user.last_name}`
                    }
                    SelectValue={(option: any) => option.user.email}
                    placeholder=""
                  />
                  <Text fontSize="sm" color="crimson">
                    {touched.deputizing_officer && errors.deputizing_officer}
                  </Text>
                </FormControl>
                <br />

                <Button
                  type="submit"
                  form="apply-leave-form"
                  variant="primary"
                  w="full"
                >
                  Submit Application
                </Button>
              </Form>
            )}
          </Formik>
        ) : (
          <Preloader />
        )}
      </DrawerBody>
    </div>
  );
};

export default AddLeaveApplication;