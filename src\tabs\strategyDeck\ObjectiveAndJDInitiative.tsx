import { useState } from "react";
import { getAllObjectives } from "../../api/objectives";
import Crud<PERSON>, { CrudRow } from "../../components/CrudUi";
import Preloader from "../../components/Preloader";
import { useGetKPIs } from "../../hooks/useKPIs";
import { TKPI } from "../../types/kpis";
import { TObjective } from "../../types/objectives";
import { capitalizeFirst } from "../../utils";
import { calculateObjectivePerspectiveTargetPoint } from "../../utils/calculateObjectivePerspectiveTargetPoint";

const ObjectiveAndKPISpread = () => {
  const [page, setPage] = useState(1);

  const { data, isLoading } = useGetKPIs(page);
  const kpiData: TKPI[] = data?.data?.data || [];

  const headers = [
    "Objective Name",
    ...kpiData?.map((kpi: TKPI) => capitalizeFirst(kpi.name.toLowerCase())),
    "Objective Target Point",
  ];

  const renderRow = (objective: TObjective) => {
    const fields = [
      {
        label: "Objective",
        value: capitalizeFirst(objective.name.toLowerCase()),
      },
      ...kpiData?.map((p) => {
        const kpiMatch = p.uplineObjective.find(
          (ok) => String(ok.id) === String(objective.id)
        );

        return {
          label: capitalizeFirst(p.name.toLowerCase()),
          value: String(kpiMatch?.relativePoint || 0),
        };
      }),
      {
        label: "Objective Target Point",
        value: calculateObjectivePerspectiveTargetPoint(
          objective.perspectives
          // objective.targetPoint
        ),
      },
    ];

    const tableDataFields = fields.map((field) => String(field.value));

    return (
      <CrudRow
        item={objective}
        itemName="Objective"
        fields={fields}
        onDelete={() => {}}
        tableDataFields={tableDataFields}
      />
    );
  };

  if (isLoading) return <Preloader />;

  return (
    <CrudUI<TObjective>
      title="Objectives"
      queryKey="objectives"
      queryFn={(page) => getAllObjectives(page)}
      columns={headers}
      renderRow={renderRow}
    />
  );
};

export default ObjectiveAndKPISpread;
