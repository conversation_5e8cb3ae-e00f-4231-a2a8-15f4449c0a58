import { <PERSON>, Tab, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>anel, <PERSON>b<PERSON><PERSON><PERSON>, Tabs } from "@chakra-ui/react";
import AppBar from "../../components/AppBar";

import { AiOutlineUserAdd } from "react-icons/ai";
import { GrGroup } from "react-icons/gr";
import HrAdminTable from "../../components/HrAdminTable";
import PublicHolidayTab from "../../tabs/settings/ServiceAcct/PublicHoliday";
import UpdateOrganisation from "../../tabs/settings/ServiceAcct/UpdateOrganisation";

const settings = () => {
  return (
    <div>
      <AppBar
        heading="Settings"
        avatar="../../logo.png"
        imgAlt="Organization Avatar"
      />

      <Tabs colorScheme="primary" isLazy>
        <TabList>
          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
            <Box as="span" mr="2">
              <GrGroup size="22px" />
            </Box>
            Organization settings
          </Tab>

          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
            <Box as="span" mr="2">
              <AiOutlineUserAdd size="22px" />
            </Box>
            Create Hr Admin
          </Tab>

          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="5">
            <Box as="span" mr="2">
              <AiOutlineUserAdd size="22px" />
            </Box>
            Manage Public Holiday
          </Tab>
        </TabList>

        <TabPanels pt="3">
          <TabPanel px="0">
            <UpdateOrganisation />
          </TabPanel>

          <TabPanel px="0">
            <HrAdminTable />
          </TabPanel>

          <TabPanel px="0">
            <PublicHolidayTab />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </div>
  );
};

export default settings;
