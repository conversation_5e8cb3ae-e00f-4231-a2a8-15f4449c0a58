



export const formonthly  =  [
    {
        "id": 1,
        "saved_employee_receivables": [
            {
                "fixed_receivables_element": "Hello fixead",
                "fixed_receivables_element_gross_percent": 50,
                "value": 2500.0
            }
        ],
        "saved_employee_regulatory_recievables": [
            {
                "Employee_regulatory_recievables": "Hello fixead Receivables",
                "Employee_regulatory_recievables_gross_percent": 50,
                "regulatory_rates": 10,
                "value": 2500.0
            }
        ],
        "saved_employee_regulatory_deductables": [
            {
                "Employee_regulatory_deductables": "Employee Pension Contribution",
                "Employee_regulatory_deductables_gross_percent": 10,
                "regulatory_rates": 220,
                "value": 500.0
            }
        ],
        "saved_employee_other_deductables": [
            {
                "Employee_other_deductables": "Tax (Payee)",
                "Employee_other_deductables_gross_percent": 50,
                "value": 2500.0
            }
        ],
        "gross_money": "5000.00",
        "created_on": "2022-09-22T08:54:27.128397Z",
        "generated_for": "2022-09-22",
        "updated_at": "2022-09-22T08:54:27.128427Z",
        "grade_level": 16,
        "employee": 121
    },
    {
        "id": 2,
        "saved_employee_receivables": [
            {
                "fixed_receivables_element": "Basic",
                "fixed_receivables_element_gross_percent": 40,
                "value": 2000.0
            },
            {
                "fixed_receivables_element": "Transportation Hellish",
                "fixed_receivables_element_gross_percent": 50,
                "value": 2500.0
            }
        ],
        "saved_employee_regulatory_recievables": [
            {
                "Employee_regulatory_recievables": "stuff",
                "Employee_regulatory_recievables_gross_percent": 10,
                "regulatory_rates": 100,
                "value": 500.0
            }
        ],
        "saved_employee_regulatory_deductables": [
            {
                "Employee_regulatory_deductables": "Employee Pension Contribution",
                "Employee_regulatory_deductables_gross_percent": 50,
                "regulatory_rates": 10,
                "value": 2500.0
            }
        ],
        "saved_employee_other_deductables": [
            {
                "Employee_other_deductables": "Tax (Payee)",
                "Employee_other_deductables_gross_percent": 10,
                "value": 500.0
            }
        ],
        "gross_money": "5000.00",
        "created_on": "2022-09-22T08:54:27.154520Z",
        "generated_for": "2022-09-22",
        "updated_at": "2022-09-22T08:54:27.154545Z",
        "grade_level": 13,
        "employee": 120
    }
]