import {
  createStructuralLevel,
  deleteStructuralLevel,
  getAllStructuralLevels,
  getStructuralLevelById,
  updateStructuralLevel,
} from "../api/structuralLevel";
import { TStructuralLevel } from "../types/structuralLevel";
import {
  useCrudCreate,
  useCrudDelete,
  useCrudQuery,
  useCrudUpdate,
} from "./useCrud";

export const structuralLevelsQueryKey = ["structuralLevels"];

export const useGetStructuralLevels = (page?: number, limit?: number) =>
  useCrudQuery(structuralLevelsQueryKey, () =>
    getAllStructuralLevels(page, limit)
  );

export const useGetStructuralLevelByIdOrName = (id?: number, name?: string) =>
  useCrudQuery([...structuralLevelsQueryKey, name || ""], () =>
    getStructuralLevelById(id || 1, name)
  );

export const useAddStructuralLevel = (onClose?: () => void) =>
  useCrudCreate(
    structuralLevelsQueryKey,
    ({ values, resetForm }) =>
      createStructuralLevel(values as Omit<TStructuralLevel, "id">, resetForm),
    onClose
  );

export const useUpdateStructuralLevel = (id: number, onClose?: () => void) =>
  useCrudUpdate(
    structuralLevelsQueryKey,
    ({ values, resetForm }) => updateStructuralLevel(id, values, resetForm),
    onClose
  );

export const useDeleteStructuralLevel = () =>
  useCrudDelete(structuralLevelsQueryKey, deleteStructuralLevel);
