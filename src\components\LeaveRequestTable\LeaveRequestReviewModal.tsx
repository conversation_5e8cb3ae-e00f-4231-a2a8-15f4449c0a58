import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>nt,
  DrawerCloseButton,
  Box,
  Text,
  Flex,
  Avatar,
  Badge,
  Button,
  Textarea,
  Grid,
  GridItem,
  IconButton,
  Link,
} from "@chakra-ui/react";
import { ChevronLeftIcon, ChevronRightIcon } from "@chakra-ui/icons";

interface LeaveRequestReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  requestData?: any;
}

const LeaveRequestReviewModal: React.FC<LeaveRequestReviewModalProps> = ({
  isOpen,
  onClose,
  requestData,
}) => {
  // Calendar functionality from LeaveRequestForm
  const generateCalendarDays = () => {
    const days = [];
    const firstDay = new Date(2025, 3, 1).getDay(); // April 1, 2025 (0 = Sunday)
    const daysInMonth = new Date(2025, 3, 30).getDate(); // April has 30 days

    // Adjust for Monday start (subtract 1, but handle Sunday as 6)
    const adjustedFirstDay = firstDay === 0 ? 6 : firstDay - 1;

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < adjustedFirstDay; i++) {
      days.push(null);
    }

    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }

    return days;
  };

  // Check if day is in the pre-selected range (1-5 for the leave request)
  const isPreSelectedDay = (day: any) => {
    return day && day >= 1 && day <= 5;
  };

  const calendarDays = generateCalendarDays();
  const weekDays = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
  return (
    <Drawer isOpen={isOpen} onClose={onClose} placement="right">
      <DrawerOverlay bg="blackAlpha.600" />
      <DrawerContent
        bg="white"
        position="fixed"
        top={0}
        right={0}
        p={0}
        maxW="calc(100vw - 8rem)" // Full width minus sidebar
        w="calc(100vw - 8rem)" // Full width minus sidebar
        h="100vh" // Full viewport height
        borderRadius={0}
      >
        {/* Header */}
        <Flex justify="space-between" align="center" p={6}>
          <Flex align="center" gap={4}>
            <IconButton
              aria-label="Previous"
              icon={<ChevronLeftIcon />}
              variant="ghost"
              size="sm"
            />
            <Text fontSize="sm" fontFamily="Inter" color="gray.600">
              1 of 50
            </Text>
            <IconButton
              aria-label="Next"
              icon={<ChevronRightIcon />}
              variant="ghost"
              size="sm"
            />
          </Flex>

          <DrawerCloseButton
            position="static"
            color="gray.500"
            _hover={{ color: "gray.700" }}
          />
        </Flex>

        <Box h="calc(100vh - 120px)" overflowY="auto">
          <Grid templateColumns="500px 200px 400px" gap={0} minH="100%" px={6}>
            {/* Left Panel */}
            <GridItem py={6} display="flex" flexDirection="column">
              {/* Employee Info */}
              <Flex align="center" gap={4} mb={6}>
                <Avatar size="lg" bg="gray.300" />
                <Box>
                  <Text fontSize="xl" fontWeight={600} fontFamily="Inter">
                    Tomiwa Ayandele
                  </Text>
                  <Text fontSize="sm" color="gray.600" fontFamily="Inter">
                    Role: UI/UX Design
                  </Text>
                  <Flex align="center" gap={2} mt={1}>
                    <Text fontSize="sm" color="gray.600" fontFamily="Inter">
                      Leave Status:
                    </Text>
                    <Badge
                      bg="#FFE4C6"
                      color="#FF8600"
                      px={2}
                      py={1}
                      borderRadius="4px"
                      fontSize="xs"
                      fontFamily="Inter"
                    >
                      Pending
                    </Badge>
                  </Flex>
                </Box>
              </Flex>

              {/* Leave Statistics */}
              <Box border="1px solid #E2E8F0" borderRadius="8px" p={4} mb={6}>
                <Flex justify="space-between" gap={2}>
                  <Box textAlign="center" flex={1}>
                    <Text fontSize="2xl" fontWeight={600} fontFamily="Inter">
                      5
                      <Text as="span" fontSize="sm">
                        /10
                      </Text>
                    </Text>
                    <Text fontSize="xs" color="gray.600" fontFamily="Inter">
                      Paid Leave Remaining
                    </Text>
                  </Box>
                  <Box textAlign="center" flex={1}>
                    <Text fontSize="2xl" fontWeight={600} fontFamily="Inter">
                      3
                      <Text as="span" fontSize="sm">
                        /10
                      </Text>
                    </Text>
                    <Text fontSize="xs" color="gray.600" fontFamily="Inter">
                      Unpaid Leave Remaining
                    </Text>
                  </Box>
                  <Box textAlign="center" flex={1}>
                    <Text fontSize="2xl" fontWeight={600} fontFamily="Inter">
                      2
                      <Text as="span" fontSize="sm">
                        /5
                      </Text>
                    </Text>
                    <Text fontSize="xs" color="gray.600" fontFamily="Inter">
                      Sick Leave
                    </Text>
                  </Box>
                </Flex>
              </Box>

              {/* Leave Details */}
              <Box mb={6}>
                <Text fontSize="sm" fontWeight={600} fontFamily="Inter" mb={2}>
                  Leave Type:
                </Text>
                <Text fontSize="lg" fontWeight={600} fontFamily="Inter" mb={4}>
                  Paid
                </Text>

                <Text fontSize="sm" fontWeight={600} fontFamily="Inter" mb={2}>
                  Start Date:
                </Text>
                <Text fontSize="md" fontFamily="Inter" mb={4}>
                  Apr 1, 2025
                </Text>

                <Text fontSize="sm" fontWeight={600} fontFamily="Inter" mb={2}>
                  End Date:
                </Text>
                <Text fontSize="md" fontFamily="Inter" mb={4}>
                  Apr 5, 2025
                </Text>

                <Link
                  color="#0B3178"
                  fontSize="sm"
                  textDecoration="underline"
                  fontFamily="Inter"
                  mb={4}
                  display="block"
                >
                  Download Document
                </Link>
              </Box>

              {/* Reason */}
              <Box mb={6} flex={1}>
                <Text fontSize="sm" fontWeight={600} fontFamily="Inter" mb={2}>
                  Reason
                </Text>
                <Textarea
                  placeholder="Notes..."
                  border="1px solid #E2E8F0"
                  borderRadius="8px"
                  fontSize="sm"
                  fontFamily="Inter"
                  resize="none"
                  bg="gray.50"
                  isReadOnly
                  h="200px"
                />
              </Box>
            </GridItem>

            {/* Middle Spacer */}
            <GridItem />

            {/* Right Panel - Calendar */}
            <GridItem pl={6} pr={12} py={6} bg="gray.50">
              <Box>
                <Text fontSize="lg" fontWeight={600} fontFamily="Inter" mb={2}>
                  Team Leave Calendar
                </Text>
                <Text fontSize="sm" color="gray.600" fontFamily="Inter" mb={4}>
                  Snapshot of upcoming team leave
                </Text>

                {/* Calendar from LeaveRequestForm */}
                <Box
                  border="1px solid"
                  borderColor="gray.200"
                  borderRadius="lg"
                  p={2}
                  bg="white"
                  w="294px"
                  h="240px"
                  display="flex"
                  flexDirection="column"
                  mb={4}
                >
                  {/* Calendar Header */}
                  <Text
                    fontSize="10px"
                    textAlign="center"
                    mb={0.5}
                    color="gray.700"
                    fontFamily="Inter"
                    fontWeight={600}
                  >
                    April 2025
                  </Text>

                  {/* Week Days Header */}
                  <Grid templateColumns="repeat(7, 1fr)" gap={0.5} mb={0.5}>
                    {weekDays.map((day) => (
                      <Box
                        key={day}
                        textAlign="center"
                        fontSize="10px"
                        fontWeight="medium"
                        color="gray.500"
                        py={0.5}
                        fontFamily="Inter"
                      >
                        {day}
                      </Box>
                    ))}
                  </Grid>

                  {/* Calendar Grid */}
                  <Grid templateColumns="repeat(7, 1fr)" gap={0.5} flex="1">
                    {calendarDays.map((day, index) => (
                      <Button
                        key={index}
                        isDisabled={!day}
                        variant="ghost"
                        size="xs"
                        h="28px"
                        w="100%"
                        fontSize="10px"
                        borderRadius="sm"
                        bg={
                          !day
                            ? "transparent"
                            : isPreSelectedDay(day)
                            ? "#0B3178"
                            : "transparent"
                        }
                        color={
                          !day
                            ? "transparent"
                            : isPreSelectedDay(day)
                            ? "white"
                            : "gray.700"
                        }
                        _hover={{
                          bg: !day
                            ? "transparent"
                            : isPreSelectedDay(day)
                            ? "#0B3178"
                            : "gray.100",
                        }}
                        _disabled={{
                          opacity: 0,
                          cursor: "default",
                        }}
                        fontWeight={isPreSelectedDay(day) ? "medium" : "normal"}
                      >
                        {day}
                      </Button>
                    ))}
                  </Grid>
                </Box>

                {/* Legend */}
                <Box mb="20rem">
                  <Flex align="center" gap={1} mb={1}>
                    <Box w={2} h={2} bg="#0B3178" borderRadius="sm" />
                    <Text fontSize="10px" color="gray.600" fontFamily="Inter">
                      Selected Leave days
                    </Text>
                  </Flex>
                  <Flex align="center" gap={1}>
                    <Box w={2} h={2} bg="gray.200" borderRadius="sm" />
                    <Text fontSize="10px" color="gray.600" fontFamily="Inter">
                      Already Selected Leave days
                    </Text>
                  </Flex>
                </Box>

                {/* Action Buttons */}
                <Box>
                  <Flex gap={3} mb={3}>
                    <Button
                      bg="gray.300"
                      color="gray.700"
                      _hover={{ bg: "gray.400" }}
                      fontSize="sm"
                      fontWeight={500}
                      px={6}
                      py={2}
                      borderRadius="8px"
                      fontFamily="Inter"
                      flex={1}
                    >
                      Decline Request
                    </Button>
                    <Button
                      bg="#0B3178"
                      color="white"
                      _hover={{ bg: "#0B3178" }}
                      fontSize="sm"
                      fontWeight={500}
                      px={6}
                      py={2}
                      borderRadius="8px"
                      fontFamily="Inter"
                      flex={1}
                    >
                      Approve Request
                    </Button>
                  </Flex>

                  <Button
                    variant="ghost"
                    color="#0B3178"
                    fontSize="sm"
                    fontWeight={500}
                    fontFamily="Inter"
                    w="full"
                    textDecoration="underline"
                  >
                    Request For Reschedule
                  </Button>
                </Box>
              </Box>
            </GridItem>
          </Grid>
        </Box>
      </DrawerContent>
    </Drawer>
  );
};

export default LeaveRequestReviewModal;
