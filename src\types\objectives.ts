export enum RoutineType {
  ONCE = "once",
  MONTHLY = "monthly",
  QUARTERLY = "quarterly",
  BI_ANNUALLY = "bi-Annually",
  ANNUALLY = "annually",
}

export enum Status {
  PENDING = "pending",
  ACTIVE = "active",
  CLOSE = "close",
}
export interface TSpread {
  id: number;
  name: string;
  relativePoint: number;
}

export interface TObjective {
  tierName?: string;
  structureLevel?: string;
  id: number;
  name: string;
  corporate: string;
  routineType: RoutineType;
  startDate: Date;
  endDate?: Date;
  afterOccurrence: number;
  targetPoint?: number;
  perspectives: TSpread[];
  targetPointUsed?: number;
  targetPointRemainder?: number;
  tenantId?: string;
  createdBy?: string;
  status: Status;
}
