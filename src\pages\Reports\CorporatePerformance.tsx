import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON> } from "@chakra-ui/react";
import AppBar from "../../components/AppBar";
import InitiativeReport from "../../tabs/reports/InitiativeReport";
import ObjectivesReport from "../../tabs/reports/Objectives";

import { useEffect, useState } from "react";

const CorporatePerformance = () => {
  const [tabIndex, setTabIndex] = useState<number>(0);

  useEffect(() => {
    const currentPageTab = localStorage.getItem("CPMPageTab");
    if (currentPageTab) {
      setTabIndex(JSON.parse(currentPageTab));
    }
  }, []);
  return (
    <>
      <AppBar
        heading="Corporate Performance Management"
        avatar="../../logo.png"
        imgAlt="Organization Avatar"
      />

      <Tabs
        colorScheme="primary"
        isLazy
        //   defaultIndex={1}
        //   colorScheme='blue'
        index={tabIndex}
        onChange={(currentIndex) => {
          setTabIndex(currentIndex);
          localStorage.setItem("CPMPageTab", JSON.stringify(currentIndex));
        }}
      >
        <TabList>
          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="10">
            Strategic Objective Report
          </Tab>

          <Tab fontWeight="500" fontSize="sm" color="gray.600" mr="10">
            KPI Report
          </Tab>
        </TabList>

        <TabPanels pt="8">
          <TabPanel>
            {/* <ObjectiveReport /> */}
            <ObjectivesReport />
          </TabPanel>

          <TabPanel>
            <InitiativeReport />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </>
  );
};

export default CorporatePerformance;
