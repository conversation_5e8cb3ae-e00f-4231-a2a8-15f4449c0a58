import { Route, useLocation } from "react-router-dom";
import { tenantManagement } from "../routes";
import Layout from "../components/TenantManage/Layout";
import Preloader from "../components/Preloader";

const TenantAdminRoutes = () => {
  const { pathname } = useLocation();
  console.log("Tenant pathname", pathname);
  return tenantManagement.map(({ component: Component, path }) => (
    <Route
      path={path}
      key={path}
      loader={<Preloader />}
      element={
        <Layout>
          <Component />
        </Layout>
      }
    />
  ));
};

export default TenantAdminRoutes;
