import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Button,
} from "@chakra-ui/react";
import { useRef, useState } from "react";
interface GoodAlertType {
  title: string;
  details: string;
  closeText: string;
  submitText: string;
  onCloseFunc: () => void;
  onSubmitFunc: () => void;
}

const GoodAlert: React.FC<GoodAlertType> = ({
  title,
  details,
  closeText,
  submitText,
  onCloseFunc,
  onSubmitFunc,
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(true);
  const onClose = () => setIsOpen(false);
  const cancelRef = useRef<any>(null);

  return (
    <>
      <AlertDialog
        motionPreset="slideInBottom"
        isOpen={isOpen}
        leastDestructiveRef={cancelRef}
        onClose={onClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="500">
              {title}
            </AlertDialogHeader>

            <AlertDialogBody>{details}</AlertDialogBody>

            <AlertDialogFooter>
              <Button
                ref={cancelRef}
                onClick={() => {
                  onCloseFunc();
                  onClose();
                }}
              >
                {closeText}
              </Button>
              <Button
                colorScheme="red"
                onClick={() => {
                  onSubmitFunc();
                  onClose();
                }}
                ml={3}
              >
                {submitText}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
};


export default GoodAlert