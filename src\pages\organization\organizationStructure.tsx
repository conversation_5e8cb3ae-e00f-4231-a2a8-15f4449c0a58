import {
  Box,
  Flex,
  Grid,
  Image,
  Link,
  ListIcon,
  ListItem,
  Text,
} from "@chakra-ui/react";
import { IoIosArrowForward } from "react-icons/io";
import { RiCheckboxBlankCircleFill } from "react-icons/ri";
import { Link as ReactLink } from "react-router-dom";
import orgSetupIllustration from "../../assets/images/org-setup.svg";
import AppBar from "../../components/AppBar";

import CustomTab, { TabItem } from "../../components/custom/CustomTab";
import Preloader from "../../components/Preloader";
import OrganizationSetupFormModal from "../../components/serviceAccount/OrganizationSetupFormModal";
import StructuresTable from "../../drawers/StructuresTable";
import UploadFileDrawer from "../../drawers/UploadFileDrawer";
import { useCurrentOrganization } from "../../hooks/organization";
import { deepBlue } from "../../theme/colors";
import { TOrganization } from "../../types/organization";
import { TRole } from "../../types/user";

const OrganizationStructure = () => {
  const userType: TRole[] = ["admin", "employer", "hr"];
  const tabList: TabItem[] = [
    {
      label: "Setup Organization",
      showForAll: false,
      visibleTo: userType,
    },
    {
      label: "View all Structures",
      showForAll: false,
      visibleTo: userType,
    },
  ];

  const tabPanels = [OrganizationSetupIllustration, StructuresTable];
  return (
    <>
      <AppBar heading="Home" />

      <CustomTab
        tabList={tabList}
        tabPanels={tabPanels}
        userType={["admin", "employer", "hr"]}
        tabId="adminOrganizationStructure"
      />
    </>
  );
};

export const OrganizationListItem = ({
  color,
  count,
  label,
  to,
  unit,
}: {
  to: string;
  color: string;
  label: string;
  count: number;
  unit: string;
}) => (
  <ListItem display="flex" alignItems="center">
    <ListIcon as={RiCheckboxBlankCircleFill} boxSize={3} color={color} />
    <Link as={ReactLink} to={to} flex="1" _hover={{ textDecoration: "none" }}>
      <Grid flex="1" gridTemplateColumns="1fr 1fr auto">
        <Box as="h5" color={color} fontWeight="500">
          {label}
        </Box>
        <Text fontWeight="500" color="gray.500">
          {count} {count > 1 ? unit + "s" : unit}
        </Text>
        <IoIosArrowForward size="20" />
      </Grid>
    </Link>
  </ListItem>
);

export default OrganizationStructure;

const OrganizationSetupIllustration = () => {
  const { data, isLoading } = useCurrentOrganization();

  const currentOrganization = data?.data as TOrganization;

  const organizationName = currentOrganization?.name;

  if (isLoading) return <Preloader />;

  return (
    <Flex
      px="1"
      py="4"
      textAlign="center"
      direction="column"
      alignItems="center"
      overflowY="auto"
    >
      <Image
        src={orgSetupIllustration}
        alt=""
        mb="6"
        mx="auto"
        height={{ base: "150px", md: "200px" }}
      />
      <Text
        color="secondary.900"
        fontSize="15px"
        my="6"
        maxW="500px"
        textAlign="center"
      >
        Welcome {organizationName ? "" : "onboard"}
        {organizationName ? (
          <>
            {" "}
            <Text as="strong" color={deepBlue}>
              {" "}
              {organizationName}
              {currentOrganization?.shortName &&
                `(${currentOrganization?.shortName})`}
              ,{" "}
            </Text>
            this is where you begin your organization structure setup
          </>
        ) : (
          <Text as="span">
            , Please complete your organization setup first before proceeding to
            organization structure setup
          </Text>
        )}
      </Text>

      {organizationName ? (
        <Flex wrap="wrap" gap="4" align="center" justifyContent="center">
          <UploadFileDrawer
            uploadUrl="/organization/setup/bulk-upload"
            resourceName="Organization Structure"
          />

          <OrganizationSetupFormModal />
          {/* <Link
            as={ReactLink}
            to="/organization/structure/setup/?structureType=default"
            onClick={() => {
              localStorage.setItem("organizationStructure", "1");
            }}
            color="primary"
            textDecoration="none"
            borderRadius="6"
            px="4"
            py="2"
            fontSize="small"
          >
            Use default Organization Structure
          </Link> */}
        </Flex>
      ) : (
        <Link
          as={ReactLink}
          to="/organization/setup"
          bg="primary"
          color="whitesmoke"
          textDecoration="none"
          borderRadius="6"
          px="4"
          py="2"
          fontSize="small"
        >
          Complete Organization Setup
        </Link>
      )}
    </Flex>
  );
};
