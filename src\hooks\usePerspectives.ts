import {
  createPerspective,
  deletePerspective,
  getAllPerspectives,
  updatePerspective,
} from "../api/perspectives";
import {
  useCrudCreate,
  useCrudDelete,
  useCrudQuery,
  useCrudUpdate,
} from "./useCrud";

export const perspectivesQueryKey = ["perspectives"];

export const useGetPerspectives = (page?: number, limit?: number) =>
  useCrudQuery(perspectivesQueryKey, () => getAllPerspectives(page, limit));

export const useAddPerspective = (onClose: () => void) =>
  useCrudCreate(
    perspectivesQueryKey,
    ({ name, resetForm }) => createPerspective(name, resetForm),
    onClose
  );

export const useUpdatePerspective = (id: number, onClose?: () => void) =>
  useCrudUpdate(
    perspectivesQueryKey,
    ({ name, resetForm }) => updatePerspective(id, name, resetForm),
    onClose
  );

export const useDeletePerspective = () =>
  useCrudDelete(perspectivesQuery<PERSON>ey, deletePerspective);
