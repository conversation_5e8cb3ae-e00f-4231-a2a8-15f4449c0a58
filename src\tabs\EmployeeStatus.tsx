import {
  Box,
  Button,
  Editable,
  Heading,
  List,
  ListIcon,
  ListItem,
} from "@chakra-ui/react";
import { useState } from "react";
import { AiFillEdit, AiOutlineCloseCircle } from "react-icons/ai";
import { MdCheckCircle } from "react-icons/md";
import { Radio, RadioGroup, Stack } from "@chakra-ui/react";
import { useErrorBoundary } from "react-error-boundary";
import Preloader from "../components/Preloader";
import { TPeople } from "../types/people";

const iconStyle: any = {
  display: "inline-block",
  margin: "0 10px",
  cursor: "pointer",
};

const EmployeeStatus = (data: TPeople): React.ReactElement => {
  const [isEdit, setIsEdit] = useState(false);
  const [value, setValue] = useState<string>(
    (data ).employeeEmploymentInformation.status
      ? (data ).employeeEmploymentInformation.status
      : "active"
  );
  const { showBoundary } = useErrorBoundary();
  const orgName = localStorage.getItem("current_organization_short_name");

  const handleSubmit = () => {
    let finalValue: string = "active";
    if (isEdit) {
      finalValue = value;
    } else {
      if ((data ).employeeEmploymentInformation.status) {
        finalValue = (data ).employeeEmploymentInformation.status;
      }
    }

    if (orgName) {
      const structure: any =
        ((data )?.corporateLevel && (data )?.corporateLevel) ||
        ((data )?.group && (data )?.group) ||
        ((data )?.unit && (data )?.unit) ||
        ((data )?.division && (data )?.division) ||
        ((data )?.department && (data )?.department);

      if (!structure) return;

      let levelName = "";
      if ((data ).corporateLevel) {
        levelName = "corporateLevel";
      }
      if ((data ).group) {
        levelName = "group";
      }
      if ((data ).department) {
        levelName = "department";
      }
      if ((data ).unit) {
        levelName = "unit";
      }
      if ((data ).division) {
        levelName = "division";
      }
      if ((data ).department) {
        levelName = "department";
      }

      const finalData: TPeople = {
        firstName: (data ).user.firstName,
        lastName: (data ).user.lastName,
        phoneNumber: (data ).user.phoneNumber,
        role: (data ).user.userRole,
        dateOfBirth: (data ).employeeBasicInformation.dateOfBirth,
        description:
          (data ).employeeBasicInformation.description,
        personalEmail: (data ).employeeContactInformation.personalEmail,
        address: (data ).employeeContactInformation.address,
         guarantor1FirstName:
          (data ).employeeContactInformation. guarantor1FirstName,
        guarantor1LastName:
          (data ).employeeContactInformation.guarantor1LastName,
        guarantor1Address:
          (data ).employeeContactInformation.guarantor1Address,
        guarantor1Occupation:
          (data ).employeeContactInformation.guarantor1Occupation,
        guarantor1Age: +(
          data 
        ).employeeContactInformation.guarantor1Age,
        guarantorOneIdCard: null,
        guarantorOnePassport: null,

        guarantor2FirstName:
          (data ).employeeContactInformation.guarantor2FirstName,
        guarantor2LastName:
          (data ).employeeContactInformation.guarantor2LastName,
        guarantor2Address:
          (data ).employeeContactInformation.guarantor2Address,
        guarantor2Occupation:
          (data ).employeeContactInformation.guarantor2Occupation,
          guarantor2Age: +(
          data 
        ).employeeContactInformation.guarantor2Age,
        guarantor2IdCard: null,
        guarantor2Passport: null,

        dateEmployed: (data ).employeeEmploymentInformation.dateEmployed
          ? (data ).employeeEmploymentInformation.dateEmployed
          : "",
          careerLevel: levelName,
        designationName:
          (data ).employeeBasicInformation.designation.name,
        careerPathLevel: (data ).careerPath
          ? (data ).careerPath.level.toString()
          : "",
        educationDetails:
          (data ).employeeBasicInformation.educationDetails,
        employeeUuid: (data ).uuid,
        status: finalValue,
      };
      console.log({ finalData });
    }
  };

  return (
    <Box>
      {status === "updating" && <Preloader />}
      {isEdit ? (
        <Box>
          <RadioGroup onChange={setValue} value={value}>
            <Heading as="h4" size="md">
              Please Pick Status
              <Box onClick={(e) => setIsEdit(!isEdit)} style={iconStyle}>
                {isEdit ? <AiOutlineCloseCircle /> : <AiFillEdit />}
              </Box>
            </Heading>
            <br />
            <Stack>
              <Radio value="active">Active</Radio>
              <Radio value="on_leave">On Leave</Radio>
              <Radio value="suspended">Suspended</Radio>
              <Radio value="absent">Absent</Radio>
              <Radio value="dismissed">Dismissed</Radio>
              <Radio value="resigned">Resigned</Radio>
            </Stack>
          </RadioGroup>
          <Button
            variant={"primary"}
            loadingText="updating..."
            isLoading={status === "updating"}
            onClick={(e) => {
              e.preventDefault();
              console.log("ddd");
              handleSubmit();
            }}
          >
            Submit
          </Button>
        </Box>
      ) : (
        <List spacing={3}>
          <Heading as="h4" size="md">
            Current Status
            <Box onClick={(e) => setIsEdit(!isEdit)} style={iconStyle}>
              {isEdit ? <AiOutlineCloseCircle /> : <AiFillEdit />}
            </Box>
          </Heading>
          <ListItem>
            <ListIcon as={MdCheckCircle} color={"green.500"} />
            {(data ).employeeEmploymentInformation.status}
          </ListItem>
        </List>
      )}
      <br />
    </Box>
  );
};

export default EmployeeStatus;
