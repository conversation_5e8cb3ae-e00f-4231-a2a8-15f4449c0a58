import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { <PERSON>rowser<PERSON>outer as Router } from "react-router-dom";
import App from "./App";

import "./index.css";

import "@fontsource/inter/400.css";
import "@fontsource/inter/900.css";

import "@fontsource/poppins/400.css";
import "@fontsource/poppins/700.css";

import { Toaster } from "sonner";
import CustomProvider from "./context/CustomProvider";

// Sentry.init({
//   dsn: "https://<EMAIL>/6170333",
//   tracesSampleRate: 1.0,
// });

const container = document.getElementById("root") as HTMLElement;
const root = ReactDOM.createRoot(container);

root.render(
  <StrictMode>
    <CustomProvider>
      <Router>
        <App />
      </Router>
    </CustomProvider>
    <Toaster duration={5000} closeButton position="top-center" />
  </StrictMode>
);
