import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useToast,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import { useErrorBoundary } from "react-error-boundary";
import * as yup from "yup";
import InputWithLabel from "../components/InputWithLabel";

interface UpdateUnitsType {
  name: string;
  uuid: string;
}

const schema = yup.object().shape({
  name: yup.string().required(),
});

const UpdateUnits = (props: UpdateUnitsType) => {
  const ORG_NAME = localStorage.getItem("current_organization_short_name");
  const { showBoundary } = useErrorBoundary();
  const toast = useToast();

  const formik = useFormik({
    initialValues: {
      name: props.name,
    },
    validationSchema: schema,
    onSubmit: (values) => {
      console.log(values.name, "the rsh");
      if (ORG_NAME) {
        console.log("org name");
        // Add your submission logic here
      }
    },
  });

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Update Unit
      </DrawerHeader>
      <DrawerBody>
        <form id="add-perspective-form" onSubmit={formik.handleSubmit}>
          <InputWithLabel
            id="name"
            label="Name of Corporate"
            variant="filled"
            bg="secondary.200"
            name="name"
            mb="5"
            value={formik.values.name}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            formErrorMessage={formik.errors.name}
          />
        </form>
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          form="add-perspective-form"
          variant="primary"
          w="full"
          size="sm"
          isLoading={false} // Replace with your actual loading state
          loadingText="Updating..."
        >
          Update
        </Button>
      </DrawerFooter>
    </>
  );
};

export default UpdateUnits;