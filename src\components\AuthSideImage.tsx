import { Image, VStack } from "@chakra-ui/react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import Logo from "../assets/images/logo.png";

const MotionVStack = motion.create(VStack);

export default function AuthSideImage({
  SignUpIllustration,
}: {
  SignUpIllustration: string;
}) {
  return (
    <MotionVStack
      h="100vh"
      w="100%"
      flex={1}
      bg="lightblue"
      spacing="20px"
      align="center"
      justify="center"
      py={20}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      display={{ base: "none", md: "flex" }}
    >
      <Link to="/">
        <Image src={Logo} alt="e-metric logo" mt={20} />
      </Link>
      <Image
        src={SignUpIllustration}
        w="60%"
        maxW="350px"
        alt="authentication illustration"
      />
    </MotionVStack>
  );
}
