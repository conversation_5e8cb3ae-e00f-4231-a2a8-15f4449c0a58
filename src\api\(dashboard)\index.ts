import { makeApiRequest, MakeApiRequestParams } from "..";

export const dashboardService = async ({
  url,
  data,
  type,
  method = "POST",
  resetForm,
  contentType,
  params,
}: MakeApiRequestParams) => {
  try {
    const response = await makeApiRequest({
      url: `dashboard/${url}`,
      method,
      data,
      resetForm,
      contentType,
      type: "dashboard-" + type,
      params,
    });

    if (!response) return null;

    return response;
  } catch (error) {
    console.error("Dashboard error:", error);
    return null;
  }
};

export const dashboardAPIRequest = async ({
  url,
  method,
  params,
  data,
  resetForm,
  contentType,
}: {
  url: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  params?: Record<string, any>;
  data?: any;
  resetForm?: () => void;
  contentType?: string;
}) => {
  return await dashboardService({
    url,
    method,
    params,
    data,
    resetForm,
    contentType,
  });
};
