import React, { useEffect, useMemo } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>eader,
  useDisclosure,
} from "@chakra-ui/react";
import { useFormik } from "formik";
import * as yup from "yup";
import { ReuseableForm, InputField } from "../components/custom/form";
import { useAddTask } from "../hooks/useTask";
import { useGetKPIs } from "../hooks/useKPIs";
import { RoutineType } from "../types/objectives";
import { usePeopleEmails } from "../hooks/usePeople";

// Yup validation schema based on backend Joi schema
export const TaskSchema = yup.object().shape({
  name: yup.string().required("Task name is required"),
  uplineKpi: yup.string().required("Upline Kpi is required"),
  ownerEmail: yup
    .string()
    .email("Invalid email address")
    .required("Owner email is required"),
  taskType: yup.string().required("Task type is required"),
  routineType: yup.string().required("Routine type is required"),
  startDate: yup.string().required("Start date is required"),
  startTime: yup.string().required("Start time is required"),
  duration: yup.string().required("Duration is required"),
  repeatEvery: yup.string().required("Repeat frequency is required"),
  occursOnDaysWeekly: yup.string().required("Weekly days are required"),
  occursOnDayNumberMonthly: yup
    .string()
    .required("Monthly day number is required"),
  occursDayPositionMonthly: yup
    .string()
    .required("Monthly position is required"),
  occursOnDayMonthly: yup.string().required("Monthly day is required"),
  endDate: yup.string().required("End date is required"),
  afterOccurrence: yup.string().required("After occurrence is required"),
  reworkLimit: yup.string().required("Rework limit is required"),
  qualityTargetPoint: yup.string().required("Quality target point is required"),
  quantityTargetPoint: yup
    .string()
    .required("Quantity target point is required"),
  quantityTargetUnit: yup.string().required("Quantity target unit is required"),
  turnAroundTimeTargetPoint: yup
    .string()
    .required("Turn-around time target point is required"),
});

const AddTask = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const addTaskMutation = useAddTask(onClose);
  const { data: kpiData } = useGetKPIs();
  const peopleList = usePeopleEmails();

  const kpiList = kpiData?.data?.data?.map((item: any) => item?.name);
  const initialValues = useMemo(
    () => ({
      name: "",
      uplineKpi: "",
      ownerEmail: "",
      taskType: "",
      routineType: "",
      startDate: "",
      startTime: "",
      duration: "",
      repeatEvery: "",
      occursOnDaysWeekly: "",
      occursOnDayNumberMonthly: "",
      occursDayPositionMonthly: "",
      occursOnDayMonthly: "",
      endDate: "",
      afterOccurrence: "",
      reworkLimit: "",
      qualityTargetPoint: "",
      quantityTargetPoint: "",
      quantityTargetUnit: "",
      turnAroundTimeTargetPoint: "",
    }),
    []
  );

  const formik = useFormik({
    initialValues,
    validationSchema: TaskSchema,
    onSubmit: (values, { resetForm }) => {
      addTaskMutation.mutate({
        values: {
          ...values,
          duration: values?.duration?.toString(),
          repeatEvery: values?.repeatEvery?.toString(),
        },
        resetForm,
      });
    },
  });

  const routineTypesArray = Array.from(Object.values(RoutineType));

  // Define form fields
  const fields: InputField[] = [
    {
      name: "name",
      type: "text",
      label: "Task Name",
      placeholder: "Enter task name",
      validate: true,
    },
    {
      name: "uplineKpi",
      type: "select",
      label: "Upline Kpi",
      options: kpiList,
      validate: true,
    },
    {
      type: "grid",
      name: "contact",
      gridCol: 2,
      gridInputs: [
        {
          name: "ownerEmail",
          type: "select",
          label: "Owner Email",
          placeholder: "Enter owner email",
          options: peopleList,
          validate: true,
        },
        {
          name: "taskType",
          type: "text",
          label: "Task Type",
          placeholder: "Enter task type",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "timing",
      gridCol: 2,
      gridInputs: [
        {
          name: "startDate",
          type: "date",
          label: "Start Date",
          placeholder: "Select start date",
          validate: true,
        },
        {
          name: "startTime",
          type: "time",
          label: "Start Time",
          placeholder: "Select start time",
          validate: true,
        },
        {
          name: "duration",
          type: "number",
          label: "Duration",
          placeholder: "Enter duration",
          helpText: "Durations are in mins",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "recurrence",
      gridCol: 2,
      gridInputs: [
        {
          name: "routineType",
          type: "select",
          label: "Routine Type",
          options: routineTypesArray,
          placeholder: "Select routine type",
          validate: true,
        },
        {
          name: "repeatEvery",
          type: "number",
          label: "Repeat Every",
          placeholder: "e.g., 1 week, 2 months",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "weekly",
      gridCol: 1,
      gridInputs: [
        {
          name: "occursOnDaysWeekly",
          type: "text",
          label: "Occurs On (Weekly)",
          placeholder: "e.g., Mon, Wed",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "monthly",
      gridCol: 2,
      gridInputs: [
        {
          name: "occursOnDayNumberMonthly",
          type: "text",
          label: "Day Number (Monthly)",
          placeholder: "e.g., 15",
          validate: true,
        },
        {
          name: "occursDayPositionMonthly",
          type: "text",
          label: "Day Position (Monthly)",
          placeholder: "e.g., First, Last",
          validate: true,
        },
        {
          name: "occursOnDayMonthly",
          type: "text",
          label: "Day (Monthly)",
          placeholder: "e.g., Monday",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "conclusion",
      gridCol: 2,
      gridInputs: [
        {
          name: "endDate",
          type: "date",
          label: "End Date",
          placeholder: "Select end date",
          validate: true,
        },
        {
          name: "afterOccurrence",
          type: "text",
          label: "After Occurrences",
          placeholder: "Enter number of occurrences",
          validate: true,
        },
      ],
    },
    {
      type: "grid",
      name: "targets",
      gridCol: 2,
      gridInputs: [
        {
          name: "qualityTargetPoint",
          type: "text",
          label: "Quality Target Point",
          placeholder: "Enter quality points",
          validate: true,
        },
        {
          name: "quantityTargetPoint",
          type: "text",
          label: "Quantity Target Point",
          placeholder: "Enter quantity points",
          validate: true,
        },
        {
          name: "quantityTargetUnit",
          type: "text",
          label: "Quantity Target Unit",
          placeholder: "Enter quantity units",
          validate: true,
        },
      ],
    },
    {
      name: "turnAroundTimeTargetPoint",
      type: "text",
      label: "Turn-Around Time Target Point",
      placeholder: "Enter TAT points",
      validate: true,
    },
    {
      name: "reworkLimit",
      type: "text",
      label: "Rework Limit",
      placeholder: "Enter rework limit",
      validate: true,
    },
  ];

  // Reset form values on mount
  useEffect(() => {
    formik.setValues(initialValues);
  }, [initialValues]);

  return (
    <>
      <DrawerCloseButton />
      <DrawerHeader fontWeight="500" fontSize="md">
        Create New Task
      </DrawerHeader>
      <DrawerBody>
        <ReuseableForm formik={formik} inputArray={fields} />
      </DrawerBody>
      <DrawerFooter>
        <Button
          type="submit"
          onClick={() => formik.submitForm()}
          variant="primary"
          w="full"
          isLoading={addTaskMutation.isPending}
          loadingText="Creating task..."
        >
          Create Task
        </Button>
      </DrawerFooter>
    </>
  );
};

export default AddTask;
