import React, { useState } from "react";
import AppBar from "../../components/AppBar";

import { Box, Container, Flex, Heading, Text } from "@chakra-ui/layout";
import { Button } from "@chakra-ui/button";
import LeaveHistoryTable from "../../components/LeaveRequestTable/LeaveHistoryTable";

const enum LeaveHistoryView {
  PROFILE = "profile",
  EXPORT = "export",
}

const LeaveHistory = () => {
  // Sample data - replace with actual data from your API/state
  const [tab, setTab] = useState<LeaveHistoryView>(LeaveHistoryView.PROFILE);
  const leaveHistoryData = [
    {
      id: 1,
      employee: "<PERSON><PERSON><PERSON>",
      role: "UI/UX Designer",
      leaveType: "Paid Leave",
      requestDate: "Mar 1 - Mar 5, 2025",
      status: "Pending",
    },
    {
      id: 2,
      employee: "<PERSON>",
      role: "Frontend Developer",
      leaveType: "Sick Leave",
      requestDate: "Feb 15 - Feb 17, 2025",
      status: "Pending",
    },
    {
      id: 3,
      employee: "<PERSON>",
      role: "Backend Developer",
      leaveType: "Paid Leave",
      requestDate: "Feb 8 - Feb 12, 2025",
      status: "Pending",
    },
    {
      id: 4,
      employee: "Mike <PERSON>",
      role: "Product Manager",
      leaveType: "Annual Leave",
      requestDate: "Jan 20 - Jan 24, 2025",
      status: "Approved",
    },
    {
      id: 5,
      employee: "Sarah Wilson",
      role: "QA Engineer",
      leaveType: "Paid Leave",
      requestDate: "Jan 10 - Jan 14, 2025",
      status: "Declined",
    },
    {
      id: 6,
      employee: "David Brown",
      role: "DevOps Engineer",
      leaveType: "Sick Leave",
      requestDate: "Dec 15 - Dec 17, 2024",
      status: "Pending",
    },
    {
      id: 7,
      employee: "Lisa Garcia",
      role: "Marketing Manager",
      leaveType: "Paid Leave",
      requestDate: "Dec 1 - Dec 5, 2024",
      status: "Declined",
    },
    {
      id: 8,
      employee: "Tom Anderson",
      role: "Sales Representative",
      leaveType: "Annual Leave",
      requestDate: "Nov 20 - Nov 30, 2024",
      status: "Pending",
    },
  ];

  // Handle row action (View Details button click)
  const handleRowAction = (item: any) => {
    console.log("View leave history details:", item);
    // Add your navigation logic here
    // e.g., navigate to leave history details page
  };

  return (
    <>
      <AppBar heading="Leave Management " />
      <Container maxWidth={"7xl"}>
        <Box borderBottom={"1px solid #E2E8F0"} mb={4} pb={2}>
          <Heading
            as={"h2"}
            fontSize={"2rem"}
            fontWeight={700}
            color={"#0B3178"}
            fontFamily="Inter"
          >
            Leave History
          </Heading>
        </Box>
        {tab === LeaveHistoryView.PROFILE && (
          <>
            <Flex justifyContent="space-between" alignItems="center" mb={10}>
              <Box>
                <Text fontWeight={550} fontSize="1.2rem" fontFamily="Inter">
                  Leave History
                </Text>
                <Text fontSize="sm" fontStyle="italic" color="gray.600" fontFamily="Inter" mt={1}>
                  View the status and summary of all your past and current <br /> leave
                  requests
                </Text>
              </Box>
              <Button
                backgroundColor="#0B3178"
                color="white"
                _hover={{ bg: "#0B3178" }}
                fontSize="14px"
                fontWeight={500}
                px={8}
                py={6}
                borderRadius={"10px"}
                fontFamily="Inter"
                onClick={() => setTab(LeaveHistoryView.EXPORT)}
              >
                Export History
              </Button>
            </Flex>

            {/* Leave History Table */}
            <LeaveHistoryTable
              data={leaveHistoryData}
              title="Select from Organization Tree"
              onRowAction={handleRowAction}
              actionLabel="View Details"
              showCheckboxes={true}
              showSearch={true}
              showFilter={true}
            />
          </>
        )}

        {tab === LeaveHistoryView.EXPORT && (
          <>
            <Box>
              <Text
                fontWeight={600}
                fontSize="1.2rem"
                fontFamily="Inter"
                mb={4}
              >
                Export Leave History
              </Text>
              <Text fontSize="sm" color="gray.600" fontFamily="Inter" mb={6}>
                Export your leave history data in various formats. You can
                filter by date range, employee, or leave type before exporting.
              </Text>

              <Flex gap={4} mb={4}>
                <Button
                  backgroundColor="#0B3178"
                  color="white"
                  _hover={{ bg: "#0B3178" }}
                  fontSize="14px"
                  fontWeight={500}
                  px={6}
                  py={4}
                  borderRadius={"8px"}
                  fontFamily="Inter"
                >
                  Export as PDF
                </Button>
                <Button
                  backgroundColor="green.500"
                  color="white"
                  _hover={{ bg: "green.600" }}
                  fontSize="14px"
                  fontWeight={500}
                  px={6}
                  py={4}
                  borderRadius={"8px"}
                  fontFamily="Inter"
                >
                  Export as Excel
                </Button>
                <Button
                  backgroundColor="blue.500"
                  color="white"
                  _hover={{ bg: "blue.600" }}
                  fontSize="14px"
                  fontWeight={500}
                  px={6}
                  py={4}
                  borderRadius={"8px"}
                  fontFamily="Inter"
                >
                  Export as CSV
                </Button>
              </Flex>
            </Box>
          </>
        )}
      </Container>
    </>
  );
};

export default LeaveHistory;
