import { Flex } from "@chakra-ui/react";
import { useEffect, useState } from "react";

import { VercelLogoIcon } from "@radix-ui/react-icons";
import { useFormik } from "formik";
import { useNavigate, useSearchParams } from "react-router-dom";
import * as Yup from "yup";
import { verifyAccount } from "../api/authentication";
import SignUpIllustration from "../assets/images/signup-illustration.svg";
import AuthSideImage from "../components/AuthSideImage";
import AuthContainer from "../components/authentication/authContainer";
import { ReuseableForm } from "../components/custom/form";

const verifyAccountSchema = Yup.object().shape({
  email: Yup.string()
    .email("Invalid Email Address")
    .required("Fill in a valid email address"),
});

const VerifyAccount = () => {
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const token = searchParams.get("token");

  const initialValues = { email: "" };

  const formik = useFormik({
    initialValues,
    validationSchema: verifyAccountSchema,
    onSubmit: async ({ email }, { resetForm }) => {
      // const success = await loginUser({ email, password, resetForm });
      // if (success) {
      //   navigate("/");
      // }
    },
  });

  const fields = [
    {
      name: "email",
      type: "email",
      label: "Organization email",
      placeholder: "Enter a valid email address",
      validate: true,
    },
  ];

  useEffect(() => {
    formik.setValues(initialValues);

    if (!token) return;

    (async () => {
      setIsLoading(true);
      const success = await verifyAccount({ token });
      setIsLoading(false);

      if (success) return navigate("/");
    })();
  }, []);

  return (
    <Flex minH="100vh" w="100vw" overflowY="hidden">
      <AuthSideImage SignUpIllustration={SignUpIllustration} />
      <AuthContainer
        title="Verify your organization"
        subtitle="Welcome Back"
        bottomLink={{ text: "Create an organization", url: "/sign-up" }}
      >
        {isLoading ? (
          <h1>Verifying, Please wait...</h1>
        ) : (
          <ReuseableForm
            formik={formik}
            inputArray={fields}
            button={{
              text: "Activate account",
              icon: <VercelLogoIcon />,
              type: "submit",
              buttonLoadingText: "Verifying account...",
            }}
          />
        )}
      </AuthContainer>
    </Flex>
  );
};

export default VerifyAccount;
