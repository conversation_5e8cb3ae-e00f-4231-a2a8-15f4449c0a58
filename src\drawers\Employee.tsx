import { PlusIcon, UploadIcon } from "@radix-ui/react-icons";
import { RiSendPlane2Fill } from "react-icons/ri";
import { getAllPeople } from "../api/people";
import CrudUI, { CrudRow } from "../components/CrudUi";
import { useOrganizationDateFormat } from "../hooks/useOrganizationDateFormat";
import { useDeletePeople } from "../hooks/usePeople";
import { PeopleAttributes } from "../types/people";
import { capitalizeFirst } from "../utils";
import AddEmployee from "./AddEmployee";
import CustomDrawer from "./CustomDrawer";
import EmployeeDetails from "./EmployeeDetails";
import UpdateEmployee from "./UpdateEmployee";
import UploadEmployees from "./UploadEmployees";

type TEmployee = PeopleAttributes;

const Employees = ({
  isInEmployeePage = true,
}: {
  isInEmployeePage?: boolean;
}) => {
  const deleteMutation = useDeletePeople();
  const organizationDateFormat = useOrganizationDateFormat();

  const renderRow = (employee: TEmployee) => {
    const employeeTableInformation = [
      {
        label: "People",
        value: `${capitalizeFirst(employee.firstName)} ${capitalizeFirst(
          employee.lastName
        )}`,
        key: "name",
      },
      {
        label: "Email",
        value: employee.personalEmail || "N/A",
        key: "email",
      },
      {
        label: "Phone",
        value:
          employee?.officialPhoneNumber ||
          employee.personalPhoneNumber ||
          "N/A",
        key: "phone",
      },
      {
        label: "Designation",
        value: capitalizeFirst(employee.designationName) || "N/A",
        key: "designation",
      },
      {
        label: "Role",
        value: capitalizeFirst(employee.role) || "N/A",
        key: "role",
      },
      {
        label: "Date Employed",
        value: organizationDateFormat(employee.dateEmployed),
        key: "dateEmployed",
      },
    ];

    return (
      <CrudRow
        item={employee}
        itemName="People"
        fields={employeeTableInformation}
        renderDrawer={
          <>
            <CustomDrawer showModalBtnText="View" drawerSize="lg">
              <EmployeeDetails {...employee} />
            </CustomDrawer>

            <CustomDrawer
              showModalBtnText=""
              showModalBtnVariant="primary"
              showModalBtnColor="white"
              leftIcon={<RiSendPlane2Fill />}
              drawerSize="lg"
            >
              <UpdateEmployee employee={employee} />
            </CustomDrawer>
          </>
        }
        onDelete={() => deleteMutation.mutate(employee.id)}
        tableDataFields={employeeTableInformation.map(({ value }) => value)}
      />
    );
  };

  const columns = [
    "Name",
    "Email",
    "Phone",
    "Designation",
    "Role",
    "Date Employed",
    "Actions",
  ];

  return (
    <CrudUI<TEmployee>
      title="People"
      queryKey="employees"
      queryFn={(page) => getAllPeople(page)}
      columns={columns}
      renderRow={renderRow}
      actions={
        isInEmployeePage && (
          <>
            <CustomDrawer
              showModalBtnText="Upload People"
              showModalBtnVariant="outline"
              showModalBtnColor="primary"
              drawerSize="md"
              leftIcon={<UploadIcon />}
            >
              <UploadEmployees />
            </CustomDrawer>
            <CustomDrawer
              showModalBtnText="Add New People"
              showModalBtnVariant="primary"
              showModalBtnColor="white"
              drawerSize="lg"
              leftIcon={<PlusIcon />}
            >
              <AddEmployee />
            </CustomDrawer>
          </>
        )
      }
    />
  );
};

export default Employees;
