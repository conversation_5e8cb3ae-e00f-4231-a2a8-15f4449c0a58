
import * as yup from "yup";

export const StructureSchema = yup.object().shape({
  name: yup.string().required("Structure Name is required"),
  priority: yup.number().required("Structure priority is required"),
  description: yup.string().optional(),
});

export const tierSchema = yup.object().shape({
  tierName: yup.string().required("Tier name is required"),
  tierDescription: yup.string().optional(),
  uplineName: yup.string().required("Upline name is required"),
  level: yup.string().required("Structural level is required"),
});
export const tierUpdateSchema = yup.object().shape({
  tierName: yup.string().required("Tier name is required"),
  tierDescription: yup.string().optional(),
});

