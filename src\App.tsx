
// import { lazy, Suspense } from "react";
// import { ErrorBoundary } from "react-error-boundary";
// import { Route, Routes } from "react-router-dom";

// import Layout from "./components/Layout";
// import Preloader from "./components/Preloader";
// import PrivateRoute from "./pages/PrivateRoute";
// import TenantAdminRoute from "./utils/TenantAdminRoute";
// import TenantPrivateRoute from "./utils/TenantPrivateRoute";

// import AccessTokenDenied from "./pages/ErrorPages/AccessTokenDenied";
// import ServerError from "./pages/ErrorPages/ServerError";
// import SomethingWentWrong from "./pages/ErrorPages/SomethingWentWrong";
// import UnAuthPage from "./pages/UnauthorizedPage";
// import PageNotFound from "./pages/notfound";

// import AuthLayout from "./pages/AuthLayout";
// import { organizationRoutes, tenantManagement, tenantRoutes } from "./routes";

// const SignUpPage = lazy(() => import("./pages/signUp"));
// const ForgotPasswordPage = lazy(() => import("./pages/forgotPassword"));
// const ResetPassword = lazy(() => import("./pages/ResetPassword"));
// const ValidateEmailPage = lazy(() => import("./pages/verifyAccount"));
// const LoginPage = lazy(() => import("./pages/tenantLogin"));
// const Logout = lazy(() => import("./pages/LogOut"));

// const ErrorFallback = ({ error }: { error: any }) => {
//   if (error?.response?.status === 401 || error?.status === 401) {
//     return <SomethingWentWrong message="" />;
//   }
//   if (
//     error?.status === 403 ||
//     error?.response?.status === 403 ||
//     error?.response?.status === 404 ||
//     error?.status === 404
//   ) {
//     return <AccessTokenDenied />;
//   }

//   if (error?.response?.status === 500) {
//     return <ServerError />;
//   }
//   // return <SomethingWentWrong />;
// };

// const errorHandler = (error: any, errorInfo: any) => {
//   console.error("Boundary For Handling Error", error, {
//     error: error?.message || "",
//     errorInfo: errorInfo,
//     status_code: error.status,
//   });
// };

// const AppRoutes = () => (
//   <Routes>
//     <Route element={<PageNotFound />} path="*" />

//     <Route element={<AuthLayout />}>
//       <Route element={<LoginPage />} path="/" />
//       <Route element={<SignUpPage />} path="/sign-up" />
//       <Route element={<ForgotPasswordPage />} path="/forgot-password" />
//       <Route element={<ResetPassword />} path="/reset-password" />
//       <Route element={<ValidateEmailPage />} path="/verify-account" />
//     </Route>

//     <Route element={<Logout />} path="/logout" />
//     <Route element={<UnAuthPage />} path="/unauthenticated" />

//     <Route path="/organization" element={<PrivateRoute />}>
//       {organizationRoutes.map(({ component: Component, path }) => (
//         <Route
//           path={path}
//           key={path}
//           element={
//             <Suspense fallback={<Preloader />}>
//               {path === "/setup" || path === "/settings" ? (
//                 <Component />
//               ) : (
//                 <Layout>
//                   <Component />
//                 </Layout>
//               )}
//             </Suspense>
//           }
//         />
//       ))}
//     </Route>
//     <Route path="/dashboard" element={<TenantPrivateRoute />}>
//       {tenantRoutes.map(({ component: Component, path, children }) => (
//         <Route
//           path={path}
//           key={path}
//           element={
//             <Suspense fallback={<Preloader />}>
//               <Layout>
//                 <Component />
//               </Layout>
//             </Suspense>
//           }
//         >
//           {/* Handle nested routes for leave-management */}
//           {children && path === "leave-management" && (
//             children.map((child) => (
//               <Route
//                 path={child.path}
//                 key={child.path}
//                 element={
//                   <Suspense fallback={<Preloader />}>
//                     <Layout>
//                       <child.component />
//                     </Layout>
//                   </Suspense>
//                 }
//               />
//             ))
//           )}
//         </Route>
//       ))}
//     </Route>
//     <Route path="/tenant-management" element={<TenantAdminRoute />}>
//       {tenantManagement.map(({ component: Component, path }) => (
//         <Route
//           path={path}
//           key={path}
//           element={
//             <Suspense fallback={<Preloader />}>
//               <Layout>
//                 <Component />
//               </Layout>
//             </Suspense>
//           }
//         />
//       ))}
//     </Route>
//   </Routes>
// );

// function App() {
//   return (
//     <Suspense fallback={<Preloader />}>
//       <ErrorBoundary FallbackComponent={ErrorFallback} onError={errorHandler}>
//         <AppRoutes />
//       </ErrorBoundary>
//     </Suspense>
//   );
// }

// export default App;



import { lazy, Suspense } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { Route, Routes } from "react-router-dom";

import Layout from "./components/Layout";
import Preloader from "./components/Preloader";
import PrivateRoute from "./pages/PrivateRoute";
import TenantAdminRoute from "./utils/TenantAdminRoute";
import TenantPrivateRoute from "./utils/TenantPrivateRoute";

import AccessTokenDenied from "./pages/ErrorPages/AccessTokenDenied";
import ServerError from "./pages/ErrorPages/ServerError";
import SomethingWentWrong from "./pages/ErrorPages/SomethingWentWrong";
import UnAuthPage from "./pages/UnauthorizedPage";
import PageNotFound from "./pages/notfound";

import AuthLayout from "./pages/AuthLayout";
import { organizationRoutes, tenantManagement, tenantRoutes } from "./routes";

const SignUpPage = lazy(() => import("./pages/signUp"));
const ForgotPasswordPage = lazy(() => import("./pages/forgotPassword"));
const ResetPassword = lazy(() => import("./pages/ResetPassword"));
const ValidateEmailPage = lazy(() => import("./pages/verifyAccount"));
const LoginPage = lazy(() => import("./pages/tenantLogin"));
const Logout = lazy(() => import("./pages/LogOut"));

const ErrorFallback = ({ error }: { error: any }) => {
  if (error?.response?.status === 401 || error?.status === 401) {
    return <SomethingWentWrong message="" />;
  }
  if (
    error?.status === 403 ||
    error?.response?.status === 403 ||
    error?.response?.status === 404 ||
    error?.status === 404
  ) {
    return <AccessTokenDenied />;
  }

  if (error?.response?.status === 500) {
    return <ServerError />;
  }
  // return <SomethingWentWrong />;
};

const errorHandler = (error: any, errorInfo: any) => {
  console.error("Boundary For Handling Error", error, {
    error: error?.message || "",
    errorInfo: errorInfo,
    status_code: error.status,
  });
};

const AppRoutes = () => (
  <Routes>
    <Route element={<PageNotFound />} path="*" />

    <Route element={<AuthLayout />}>
      <Route element={<LoginPage />} path="/" />
      <Route element={<SignUpPage />} path="/sign-up" />
      <Route element={<ForgotPasswordPage />} path="/forgot-password" />
      <Route element={<ResetPassword />} path="/reset-password" />
      <Route element={<ValidateEmailPage />} path="/verify-account" />
    </Route>

    <Route element={<Logout />} path="/logout" />
    <Route element={<UnAuthPage />} path="/unauthenticated" />

    <Route path="/organization" element={<PrivateRoute />}>
      {organizationRoutes.map(({ component: Component, path }) => (
        <Route
          path={path}
          key={path}
          element={
            <Suspense fallback={<Preloader />}>
              {path === "/setup" || path === "/settings" ? (
                <Component />
              ) : (
                <Layout>
                  <Component />
                </Layout>
              )}
            </Suspense>
          }
        />
      ))}
    </Route>
    <Route path="/dashboard" element={<TenantPrivateRoute />}>
      {tenantRoutes.map(({ component: Component, path, children }) => (
        <Route
          path={path}
          key={path}
          element={
            <Suspense fallback={<Preloader />}>
              <Layout>
                <Component />
              </Layout>
            </Suspense>
          }
        >
          {/* Handle nested routes for leave-management */}
          {children && path === "leave-management" && (
            children.map((child) => (
              <Route
                path={child.path}
                key={child.path}
                element={
                  <Suspense fallback={<Preloader />}>
                    <child.component /> {/* Remove Layout wrapper for child routes */}
                  </Suspense>
                }
              />
            ))
          )}
        </Route>
      ))}
    </Route>
    <Route path="/tenant-management" element={<TenantAdminRoute />}>
      {tenantManagement.map(({ component: Component, path }) => (
        <Route
          path={path}
          key={path}
          element={
            <Suspense fallback={<Preloader />}>
              <Layout>
                <Component />
              </Layout>
            </Suspense>
          }
        />
      ))}
    </Route>
  </Routes>
);

function App() {
  return (
    <Suspense fallback={<Preloader />}>
      <ErrorBoundary FallbackComponent={ErrorFallback} onError={errorHandler}>
        <AppRoutes />
      </ErrorBoundary>
    </Suspense>
  );
}

export default App;