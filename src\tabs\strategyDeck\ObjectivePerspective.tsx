import { useState } from "react";
import { getAllObjectives } from "../../api/objectives";
import Crud<PERSON>, { CrudRow } from "../../components/CrudUi";
import Preloader from "../../components/Preloader";
import { useGetPerspectives } from "../../hooks/usePerspectives";
import { TObjective } from "../../types/objectives";
import { TPerspective } from "../../types/perspectives";
import { capitalizeFirst } from "../../utils";
import { calculateObjectivePerspectiveTargetPoint } from "../../utils/calculateObjectivePerspectiveTargetPoint";

const ObjectivePerspectiveSpread = () => {
  const [page, setPage] = useState(1);

  const { data, isLoading } = useGetPerspectives(page);
  const perspectivesData: TPerspective[] = data?.data?.data || [];

  const headers = [
    "Objective Name",
    ...perspectivesData.map((p: TPerspective) => capitalizeFirst(p.name)),
    "Objective Target Point",
  ];

  const renderRow = (objective: TObjective) => {
    const fields = [
      { label: "Objective", value: capitalizeFirst(objective.name) },
      ...perspectivesData.map((p) => {
        const perspectiveMatch = objective.perspectives.find(
          (op) => String(op.id) === String(p.id)
        );

        return {
          label: capitalizeFirst(p.name),
          value: String(perspectiveMatch?.relativePoint || 0),
        };
      }),
      {
        label: "Objective Target Point",
        value: calculateObjectivePerspectiveTargetPoint(
          objective.perspectives
          // objective.targetPoint
        ),
      },
    ];

    const tableDataFields = fields.map((field) => String(field.value));

    return (
      <CrudRow
        item={objective}
        itemName="Objective"
        fields={fields}
        onDelete={() => {}}
        tableDataFields={tableDataFields}
      />
    );
  };

  if (isLoading) return <Preloader />;

  return (
    <CrudUI<TObjective>
      title="Objectives"
      queryKey="objectives"
      queryFn={(page) => getAllObjectives(page)}
      columns={headers}
      renderRow={renderRow}
    />
  );
};

export default ObjectivePerspectiveSpread;
